import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';

import 'model/report.dart';

abstract class ReportQuestionState {}

class ReportQuestionInitial extends ReportQuestionState {}

class ReportQuestionInProgress extends ReportQuestionState {}

class ReportQuestionSuccess extends ReportQuestionState {
  final List<Report>? reports;

  ReportQuestionSuccess({this.reports});
}

class ReportQuestionFailure extends ReportQuestionState {
  final String errorMessageCode;
  ReportQuestionFailure(this.errorMessageCode);
}

class ReportQuestionCubit extends Cubit<ReportQuestionState> {
  ReportQuestionRepository reportQuestionRepository;
  ReportQuestionCubit(this.reportQuestionRepository)
      : super(ReportQuestionInitial());

  void reportQuestion(
      {required String questionId,
      required String message,
      required String userId}) {
    emit(ReportQuestionInProgress());
    reportQuestionRepository
        .reportQuestion(
            message: message, questionId: questionId, userId: userId)
        .then((value) {
      emit(ReportQuestionSuccess());
    }).catchError((e) {
      emit(ReportQuestionFailure(e.toString()));
    });
  }

  void getReportList({required String userId}) {
    emit(ReportQuestionInProgress());
    reportQuestionRepository.reportQuestionList(userId: userId).then((value) {
      if (value.isEmpty) {
        emit(ReportQuestionFailure("err"));
      } else {
        emit(ReportQuestionSuccess(reports: value));
      }
    }).catchError((e) {
      emit(ReportQuestionFailure(e.toString()));
    });
  }

  void getReportDetail({required String id}) {
    emit(ReportQuestionInProgress());
    reportQuestionRepository.reportQuestionDetail(id: id).then((value) {
      emit(ReportQuestionSuccess(reports: [value]));
    }).catchError((e) {
      emit(ReportQuestionFailure(e.toString()));
    });
  }
}
