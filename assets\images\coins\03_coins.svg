<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="203.332" height="180.789" viewBox="0 0 203.332 180.789">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b38746"/>
      <stop offset="0.389" stop-color="#d9973b"/>
      <stop offset="0.75" stop-color="#ad6c10"/>
      <stop offset="0.858" stop-color="#d3953d"/>
      <stop offset="1" stop-color="#b07f35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffee76"/>
      <stop offset="1" stop-color="#1d1d1b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#e6a729"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f0cc62"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#e0c0a8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-17" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e0c0a8"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
  </defs>
  <g id="_03_coins" data-name="03_coins" transform="translate(-125.678 -309.778)">
    <g id="Group_3350" data-name="Group 3350" transform="translate(142.575 379.907)">
      <path id="Path_11832" data-name="Path 11832" d="M130.629,362.434c0,25.012,41.568,45.288,92.846,45.288s92.842-20.275,92.842-45.288l0-19.787H130.629v19.787Z" transform="translate(-130.629 -297.062)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11833" data-name="Path 11833" d="M179.071,360.307q5.815.379,11.846.382a177.362,177.362,0,0,0,23.852-1.583v19.753a135.5,135.5,0,0,1-23.852,1.577c-12.859,0-33.773-2.457-47.619-6.713V353.956a156.947,156.947,0,0,0,35.773,6.351Z" transform="translate(-98.5 -269.776)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11834" data-name="Path 11834" d="M175.392,375.608c17.634-8.218,28.725-19.9,28.937-32.886l0,19.531c0,13.088-11.136,24.876-28.94,33.142V375.608Z" transform="translate(-18.645 -296.881)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11835" data-name="Path 11835" d="M149.283,370.117v19.815c-11.7-7.624-18.654-17.156-18.654-27.5V342.647c.055,10.331,7,19.849,18.654,27.47Z" transform="translate(-130.629 -297.062)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11836" data-name="Path 11836" d="M172.59,358.558q5.815.379,11.846.382v19.746a170.356,170.356,0,0,1-38.452-4.249V354.681a163.742,163.742,0,0,0,26.606,3.877Z" transform="translate(-92.125 -268.027)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11837" data-name="Path 11837" d="M234.688,386.662a40.063,40.063,0,0,1-.544,6.785.468.468,0,0,1-.918,0,42.569,42.569,0,0,1,0-13.573.468.468,0,0,1,.918,0,40.075,40.075,0,0,1,.544,6.788Zm12.229-1.212a41.736,41.736,0,0,1-.52,6.788.445.445,0,0,1-.879,0,44.561,44.561,0,0,1,0-13.576.445.445,0,0,1,.879,0,41.732,41.732,0,0,1,.52,6.788Zm12.111-2.075a43.973,43.973,0,0,1-.5,6.785.423.423,0,0,1-.841,0,46.385,46.385,0,0,1,0-13.573.423.423,0,0,1,.841,0,43.983,43.983,0,0,1,.5,6.788Zm11.894-3.037a46,46,0,0,1-.471,6.788.4.4,0,0,1-.8,0,48.752,48.752,0,0,1,0-13.573.4.4,0,0,1,.8,0,45.99,45.99,0,0,1,.471,6.785Zm11.545-4.129a48.217,48.217,0,0,1-.45,6.788.38.38,0,0,1-.761,0,51.371,51.371,0,0,1,0-13.573.38.38,0,0,1,.761,0,48.2,48.2,0,0,1,.45,6.785Zm10.969-5.423a51.27,51.27,0,0,1-.426,6.788.362.362,0,1,1-.723,0,53.885,53.885,0,0,1,0-13.576.362.362,0,1,1,.723,0,51.266,51.266,0,0,1,.426,6.788Zm9.919-7.085a54,54,0,0,1-.4,6.785.344.344,0,1,1-.684,0,57.081,57.081,0,0,1,0-13.573.343.343,0,1,1,.684,0,54.055,54.055,0,0,1,.4,6.788ZM208.02,386.662a40.063,40.063,0,0,0,.544,6.785.468.468,0,0,0,.918,0,42.562,42.562,0,0,0,0-13.573.468.468,0,0,0-.918,0,40.075,40.075,0,0,0-.544,6.788Zm-12.229-1.212a41.743,41.743,0,0,0,.52,6.788.445.445,0,0,0,.88,0,44.561,44.561,0,0,0,0-13.576.445.445,0,0,0-.88,0,41.739,41.739,0,0,0-.52,6.788Zm-12.111-2.075a43.973,43.973,0,0,0,.5,6.785.423.423,0,0,0,.841,0,46.713,46.713,0,0,0,0-13.573.423.423,0,0,0-.841,0,43.983,43.983,0,0,0-.5,6.788Zm-11.894-3.037a46.01,46.01,0,0,0,.471,6.788.4.4,0,0,0,.8,0,49.1,49.1,0,0,0,0-13.573.4.4,0,0,0-.8,0,46,46,0,0,0-.471,6.785Zm-11.545-4.129a48.212,48.212,0,0,0,.45,6.788.38.38,0,0,0,.761,0,51.375,51.375,0,0,0,0-13.573.38.38,0,0,0-.761,0,48.2,48.2,0,0,0-.45,6.785Zm-10.969-5.423a51.26,51.26,0,0,0,.426,6.788.4.4,0,0,0,.363.375.391.391,0,0,0,.359-.375,53.879,53.879,0,0,0,0-13.576.391.391,0,0,0-.359-.375.4.4,0,0,0-.363.375,51.255,51.255,0,0,0-.426,6.788Zm-9.919-7.085a54,54,0,0,0,.4,6.785.344.344,0,1,0,.684,0,57.075,57.075,0,0,0,0-13.573.344.344,0,1,0-.684,0,54.059,54.059,0,0,0-.4,6.788Zm80.956,23.371a38.546,38.546,0,0,0,.565,6.785.494.494,0,0,0,.96,0,41.011,41.011,0,0,0,0-13.573.494.494,0,0,0-.96,0,38.558,38.558,0,0,0-.565,6.788Zm-88.8-32.585a57.138,57.138,0,0,0,.38,6.788c.024.218.161.375.321.375s.3-.157.321-.375a60.752,60.752,0,0,0,0-13.576c-.024-.219-.161-.375-.321-.375s-.3.157-.321.375a57.136,57.136,0,0,0-.38,6.788Zm179.695,0a57.137,57.137,0,0,1-.38,6.788c-.024.218-.16.375-.321.375s-.3-.157-.321-.375a60.749,60.749,0,0,1,0-13.576c.024-.219.161-.375.321-.375s.3.157.321.375a57.135,57.135,0,0,1,.38,6.788Z" transform="translate(-128.511 -285.78)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11838" data-name="Path 11838" d="M130.849,375.434c0,25.384,40.916,45.96,91.388,45.96s91.385-20.576,91.385-45.96-40.913-45.957-91.385-45.957-91.388,20.576-91.388,45.957Z" transform="translate(-130.098 -328.839)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11839" data-name="Path 11839" d="M130.629,374.748c0,25.1,41.568,45.455,92.846,45.455s92.842-20.35,92.842-45.455-41.568-45.458-92.842-45.458-92.846,20.35-92.846,45.458Z" transform="translate(-130.629 -329.29)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11840" data-name="Path 11840" d="M133.147,369.18c-.6,22.408,36.642,41.537,83.178,42.728S301.08,395.9,301.681,373.49s-36.638-41.537-83.178-42.728-84.755,16.013-85.356,38.418Z" transform="translate(-124.571 -325.878)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11841" data-name="Path 11841" d="M133.31,371.51c0,22.576,37.473,40.878,83.7,40.878s83.692-18.3,83.692-40.878-37.473-40.878-83.692-40.878-83.7,18.3-83.7,40.878Z" transform="translate(-124.16 -326.052)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11842" data-name="Path 11842" d="M300.541,374.033c-2.671-21.4-39.049-38.353-83.535-38.353s-80.868,16.951-83.539,38.353a20.2,20.2,0,0,1-.157-2.522c0-22.576,37.473-40.878,83.7-40.878s83.692,18.3,83.692,40.878a20.182,20.182,0,0,1-.157,2.522Z" transform="translate(-124.16 -326.052)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11843" data-name="Path 11843" d="M133.538,371.661c0,22.32,37.056,40.414,82.771,40.414s82.767-18.095,82.767-40.414-37.056-40.418-82.767-40.418-82.771,18.095-82.771,40.418Z" transform="translate(-123.61 -324.578)" fill-rule="evenodd" fill="url(#linear-gradient-9)" style="mix-blend-mode: multiply;isolation: isolate"/>
    </g>
    <g id="Group_3351" data-name="Group 3351" transform="translate(125.678 358.468)">
      <path id="Path_11844" data-name="Path 11844" d="M125.678,356.155c0,25.009,41.568,45.284,92.845,45.284s92.845-20.275,92.845-45.284l0-19.791H125.678v19.791Z" transform="translate(-125.678 -290.783)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11845" data-name="Path 11845" d="M174.123,354.028c3.873.253,7.829.382,11.842.382a177.3,177.3,0,0,0,23.852-1.584v19.75c-7.6,1.031-11,1.577-23.852,1.577s-33.77-2.454-47.619-6.71v-19.77a156.829,156.829,0,0,0,35.776,6.355Z" transform="translate(-93.549 -263.497)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11846" data-name="Path 11846" d="M170.442,369.326c17.63-8.218,28.722-19.9,28.937-32.886l0,19.531c0,13.085-11.136,24.876-28.94,33.142V369.326Z" transform="translate(-13.692 -290.599)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11847" data-name="Path 11847" d="M144.336,363.837v19.811c-11.706-7.621-18.658-17.153-18.658-27.493V336.364c.058,10.33,7,19.852,18.658,27.473Z" transform="translate(-125.678 -290.783)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-13)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11848" data-name="Path 11848" d="M167.643,352.276c3.873.253,7.829.382,11.842.382V372.4a170.4,170.4,0,0,1-38.452-4.246V348.4a163.724,163.724,0,0,0,26.61,3.877Z" transform="translate(-87.174 -261.745)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11849" data-name="Path 11849" d="M229.737,380.377a40.071,40.071,0,0,1-.544,6.788.453.453,0,0,1-.457.375.459.459,0,0,1-.461-.375,42.564,42.564,0,0,1,0-13.573.46.46,0,0,1,.461-.375.453.453,0,0,1,.457.375,40.057,40.057,0,0,1,.544,6.785Zm12.233-1.208a42.085,42.085,0,0,1-.52,6.785.447.447,0,0,1-.883,0,44.837,44.837,0,0,1,0-13.573.447.447,0,0,1,.883,0,42.133,42.133,0,0,1,.52,6.788Zm12.107-2.078a43.979,43.979,0,0,1-.5,6.788.423.423,0,0,1-.841,0,46.712,46.712,0,0,1,0-13.573.423.423,0,0,1,.841,0,43.968,43.968,0,0,1,.5,6.785Zm11.9-3.034a45.983,45.983,0,0,1-.475,6.788.416.416,0,0,1-.4.375.421.421,0,0,1-.4-.375,49.11,49.11,0,0,1,0-13.573.421.421,0,0,1,.4-.375.416.416,0,0,1,.4.375,45.967,45.967,0,0,1,.475,6.785Zm11.545-4.129a48.742,48.742,0,0,1-.45,6.788.382.382,0,0,1-.764,0,51.4,51.4,0,0,1,0-13.576.382.382,0,0,1,.764,0,48.743,48.743,0,0,1,.45,6.788Zm10.966-5.423a50.588,50.588,0,0,1-.426,6.785.362.362,0,1,1-.722,0,54.3,54.3,0,0,1,0-13.573.362.362,0,1,1,.722,0,50.679,50.679,0,0,1,.426,6.788Zm9.922-7.085a53.344,53.344,0,0,1-.4,6.785.344.344,0,1,1-.684,0,57.58,57.58,0,0,1,0-13.573.344.344,0,1,1,.684,0,53.358,53.358,0,0,1,.4,6.788Zm-95.335,22.958a40.464,40.464,0,0,0,.541,6.788.47.47,0,0,0,.921,0,42.838,42.838,0,0,0,0-13.573.47.47,0,0,0-.921,0,40.45,40.45,0,0,0-.541,6.785Zm-12.233-1.208a42.092,42.092,0,0,0,.52,6.785.445.445,0,0,0,.88,0,44.543,44.543,0,0,0,0-13.573.445.445,0,0,0-.88,0,42.14,42.14,0,0,0-.52,6.788Zm-12.111-2.078a43.538,43.538,0,0,0,.5,6.788.423.423,0,0,0,.841,0,46.715,46.715,0,0,0,0-13.573.423.423,0,0,0-.841,0,43.527,43.527,0,0,0-.5,6.785Zm-11.894-3.034a45.988,45.988,0,0,0,.475,6.788.4.4,0,0,0,.8,0,48.749,48.749,0,0,0,0-13.573.4.4,0,0,0-.8,0,45.971,45.971,0,0,0-.475,6.785Zm-11.545-4.129a48.21,48.21,0,0,0,.45,6.788.38.38,0,0,0,.761,0,51.4,51.4,0,0,0,0-13.576.38.38,0,0,0-.761,0,48.211,48.211,0,0,0-.45,6.788ZM144.321,364.5a50.544,50.544,0,0,0,.429,6.785.391.391,0,0,0,.359.375.4.4,0,0,0,.363-.375,54.289,54.289,0,0,0,0-13.573.4.4,0,0,0-.363-.375.391.391,0,0,0-.359.375,50.636,50.636,0,0,0-.429,6.788Zm-9.919-7.085a54.035,54.035,0,0,0,.4,6.785.391.391,0,0,0,.342.375.385.385,0,0,0,.342-.375,57.078,57.078,0,0,0,0-13.573.385.385,0,0,0-.342-.375.391.391,0,0,0-.342.375,54.049,54.049,0,0,0-.4,6.788Zm80.956,23.371a38.885,38.885,0,0,0,.565,6.785.477.477,0,0,0,.482.375.47.47,0,0,0,.478-.375,41.01,41.01,0,0,0,0-13.573.47.47,0,0,0-.478-.375.477.477,0,0,0-.482.375,38.9,38.9,0,0,0-.565,6.788Zm-88.8-32.585a57.073,57.073,0,0,0,.38,6.785c.028.218.161.375.325.375s.293-.157.321-.375a60.718,60.718,0,0,0,0-13.573c-.028-.218-.161-.375-.321-.375s-.3.157-.325.375a57.135,57.135,0,0,0-.38,6.788Zm179.695,0a57.126,57.126,0,0,1-.377,6.785c-.028.218-.164.375-.325.375s-.3-.157-.321-.375a60.729,60.729,0,0,1,0-13.573c.024-.218.161-.375.321-.375s.3.157.325.375a57.189,57.189,0,0,1,.377,6.788Z" transform="translate(-123.56 -279.498)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11850" data-name="Path 11850" d="M125.9,369.154c0,25.384,40.913,45.96,91.385,45.96s91.388-20.576,91.388-45.96-40.916-45.96-91.388-45.96S125.9,343.773,125.9,369.154Z" transform="translate(-125.145 -322.559)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11851" data-name="Path 11851" d="M125.678,368.463c0,25.108,41.568,45.458,92.845,45.458s92.845-20.351,92.845-45.458-41.568-45.455-92.845-45.455-92.845,20.35-92.845,45.455Z" transform="translate(-125.678 -323.008)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11852" data-name="Path 11852" d="M128.2,362.9c-.6,22.408,36.642,41.537,83.182,42.728s84.755-16.009,85.353-38.418-36.638-41.537-83.178-42.728S128.8,340.489,128.2,362.9Z" transform="translate(-119.62 -319.596)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11853" data-name="Path 11853" d="M128.36,365.225c0,22.579,37.469,40.878,83.692,40.878s83.692-18.3,83.692-40.878-37.469-40.875-83.692-40.875-83.692,18.3-83.692,40.875Z" transform="translate(-119.207 -319.77)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11854" data-name="Path 11854" d="M129.459,365.325c0,22.323,37.056,40.418,82.771,40.418S295,387.648,295,365.325s-37.056-40.418-82.767-40.418-82.771,18.1-82.771,40.418Z" transform="translate(-116.555 -318.426)" fill-rule="evenodd" fill="url(#linear-gradient-17)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11855" data-name="Path 11855" d="M295.587,367.75c-2.667-21.4-39.049-38.353-83.535-38.353s-80.868,16.951-83.535,38.353a20.243,20.243,0,0,1-.157-2.525c0-22.576,37.469-40.875,83.692-40.875s83.692,18.3,83.692,40.875a20.254,20.254,0,0,1-.157,2.525Z" transform="translate(-119.207 -319.77)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
    </g>
    <g id="Group_3352" data-name="Group 3352" transform="translate(137.626 333.49)">
      <path id="Path_11856" data-name="Path 11856" d="M129.179,349.873c0,25.009,41.568,45.284,92.845,45.284s92.842-20.275,92.842-45.284l0-19.791H129.179v19.791Z" transform="translate(-129.179 -284.501)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11857" data-name="Path 11857" d="M177.621,347.746q5.815.379,11.846.382a177.931,177.931,0,0,0,23.852-1.583v19.75a135.49,135.49,0,0,1-23.852,1.577c-12.859,0-33.773-2.454-47.619-6.71v-19.77a156.789,156.789,0,0,0,35.773,6.355Z" transform="translate(-97.05 -257.215)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11858" data-name="Path 11858" d="M173.942,363.04c17.634-8.218,28.725-19.9,28.937-32.882l0,19.531c0,13.085-11.136,24.876-28.94,33.142V363.04Z" transform="translate(-17.195 -284.317)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11859" data-name="Path 11859" d="M147.833,357.555v19.811c-11.7-7.624-18.654-17.153-18.654-27.493V330.082c.055,10.331,7,19.852,18.654,27.473Z" transform="translate(-129.179 -284.501)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11860" data-name="Path 11860" d="M171.14,345.994q5.815.379,11.846.382v19.743a170.361,170.361,0,0,1-38.452-4.245V342.117a164.12,164.12,0,0,0,26.606,3.877Z" transform="translate(-90.675 -255.463)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11861" data-name="Path 11861" d="M233.238,374.095a40.073,40.073,0,0,1-.544,6.788.468.468,0,0,1-.918,0,42.569,42.569,0,0,1,0-13.573.468.468,0,0,1,.918,0,40.065,40.065,0,0,1,.544,6.785Zm12.229-1.208a41.724,41.724,0,0,1-.52,6.785.445.445,0,0,1-.879,0,44.542,44.542,0,0,1,0-13.573.445.445,0,0,1,.879,0,41.738,41.738,0,0,1,.52,6.788Zm12.111-2.078a43.99,43.99,0,0,1-.5,6.788.423.423,0,0,1-.841,0,46.383,46.383,0,0,1,0-13.573.423.423,0,0,1,.841,0,43.973,43.973,0,0,1,.5,6.785Zm11.894-3.034a46.015,46.015,0,0,1-.471,6.788.4.4,0,0,1-.8,0,49.111,49.111,0,0,1,0-13.573.4.4,0,0,1,.8,0,45.934,45.934,0,0,1,.471,6.785Zm11.545-4.129a48.206,48.206,0,0,1-.45,6.788.38.38,0,0,1-.761,0,51.4,51.4,0,0,1,0-13.576.38.38,0,0,1,.761,0,48.2,48.2,0,0,1,.45,6.788Zm10.969-5.423a51.249,51.249,0,0,1-.426,6.785.4.4,0,0,1-.363.375.391.391,0,0,1-.359-.375,53.855,53.855,0,0,1,0-13.573.391.391,0,0,1,.359-.375.4.4,0,0,1,.363.375,51.264,51.264,0,0,1,.426,6.788Zm9.919-7.088a54.052,54.052,0,0,1-.4,6.788.344.344,0,1,1-.684,0,57.085,57.085,0,0,1,0-13.573.344.344,0,1,1,.684,0,54.038,54.038,0,0,1,.4,6.785ZM206.57,374.095a40.07,40.07,0,0,0,.544,6.788.468.468,0,0,0,.918,0,42.571,42.571,0,0,0,0-13.573.468.468,0,0,0-.918,0,40.061,40.061,0,0,0-.544,6.785Zm-12.229-1.208a41.729,41.729,0,0,0,.52,6.785.445.445,0,0,0,.88,0,44.539,44.539,0,0,0,0-13.573.445.445,0,0,0-.88,0,41.743,41.743,0,0,0-.52,6.788Zm-12.111-2.078a43.991,43.991,0,0,0,.5,6.788.423.423,0,0,0,.841,0,46.392,46.392,0,0,0,0-13.573.423.423,0,0,0-.841,0,43.973,43.973,0,0,0-.5,6.785Zm-11.894-3.034a46.015,46.015,0,0,0,.471,6.788.4.4,0,0,0,.8,0,49.1,49.1,0,0,0,0-13.573.4.4,0,0,0-.8,0,45.934,45.934,0,0,0-.471,6.785Zm-11.545-4.129a48.213,48.213,0,0,0,.45,6.788.38.38,0,0,0,.761,0,51.39,51.39,0,0,0,0-13.576.38.38,0,0,0-.761,0,48.21,48.21,0,0,0-.45,6.788Zm-10.969-5.423a51.244,51.244,0,0,0,.426,6.785.361.361,0,1,0,.722,0,53.849,53.849,0,0,0,0-13.573.361.361,0,1,0-.722,0,51.258,51.258,0,0,0-.426,6.788Zm-9.919-7.088a54.059,54.059,0,0,0,.4,6.788.344.344,0,1,0,.684,0,57.079,57.079,0,0,0,0-13.573.344.344,0,1,0-.684,0,54.045,54.045,0,0,0-.4,6.785ZM218.859,374.5a38.561,38.561,0,0,0,.565,6.788.494.494,0,0,0,.96,0,41.012,41.012,0,0,0,0-13.573.494.494,0,0,0-.96,0,38.521,38.521,0,0,0-.565,6.785Zm-88.8-32.582a57.115,57.115,0,0,0,.38,6.785c.024.218.161.375.321.375s.3-.157.321-.375a60.722,60.722,0,0,0,0-13.573c-.024-.218-.161-.375-.321-.375s-.3.157-.321.375a57.131,57.131,0,0,0-.38,6.788Zm179.695,0a57.127,57.127,0,0,1-.38,6.785c-.024.218-.16.375-.321.375s-.3-.157-.325-.375a61.276,61.276,0,0,1,0-13.573c.028-.218.164-.375.325-.375s.3.157.321.375a57.143,57.143,0,0,1,.38,6.788Z" transform="translate(-127.061 -273.216)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11862" data-name="Path 11862" d="M129.4,362.872c0,25.384,40.916,45.96,91.388,45.96s91.385-20.576,91.385-45.96-40.913-45.96-91.385-45.96S129.4,337.491,129.4,362.872Z" transform="translate(-128.648 -316.277)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11863" data-name="Path 11863" d="M129.179,362.181c0,25.1,41.568,45.458,92.845,45.458s92.842-20.354,92.842-45.458-41.568-45.455-92.842-45.455-92.845,20.35-92.845,45.455Z" transform="translate(-129.179 -316.726)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11864" data-name="Path 11864" d="M131.7,356.616c-.6,22.408,36.642,41.537,83.178,42.728s84.755-16.013,85.356-38.418-36.638-41.537-83.178-42.728S132.3,334.208,131.7,356.616Z" transform="translate(-123.121 -313.315)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11865" data-name="Path 11865" d="M131.86,358.945c0,22.576,37.473,40.878,83.7,40.878s83.692-18.3,83.692-40.878-37.473-40.878-83.692-40.878-83.7,18.3-83.7,40.878Z" transform="translate(-122.71 -313.491)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11866" data-name="Path 11866" d="M299.091,361.471c-2.671-21.4-39.049-38.356-83.535-38.356s-80.868,16.955-83.538,38.356a20.934,20.934,0,0,1-.157-2.526c0-22.576,37.473-40.878,83.7-40.878s83.692,18.3,83.692,40.878a20.243,20.243,0,0,1-.157,2.526Z" transform="translate(-122.71 -313.491)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11867" data-name="Path 11867" d="M132.012,358.41c0,22.323,37.056,40.418,82.771,40.418s82.767-18.095,82.767-40.418S260.494,318,214.783,318s-82.771,18.095-82.771,40.414Z" transform="translate(-122.344 -313.662)" fill-rule="evenodd" fill="url(#linear-gradient-9)" style="mix-blend-mode: multiply;isolation: isolate"/>
    </g>
    <g id="Group_3353" data-name="Group 3353" transform="translate(143.322 309.778)">
      <path id="Path_11868" data-name="Path 11868" d="M130.848,342.925c0,25.009,41.568,45.284,92.842,45.284s92.846-20.275,92.846-45.284l0-19.791H130.848v19.791Z" transform="translate(-130.848 -277.553)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11869" data-name="Path 11869" d="M179.29,340.8q5.815.379,11.842.382a177.4,177.4,0,0,0,23.855-1.583v19.75c-7.6,1.031-11,1.577-23.855,1.577s-33.77-2.454-47.615-6.71v-19.77A156.751,156.751,0,0,0,179.29,340.8Z" transform="translate(-98.719 -250.267)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11870" data-name="Path 11870" d="M175.611,356.092c17.634-8.215,28.722-19.9,28.937-32.882l0,19.531c0,13.085-11.136,24.876-28.941,33.142V356.092Z" transform="translate(-18.864 -277.37)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11871" data-name="Path 11871" d="M149.5,350.607v19.811c-11.7-7.621-18.654-17.153-18.654-27.493V323.134c.055,10.334,7,19.852,18.654,27.473Z" transform="translate(-130.848 -277.553)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-31)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11872" data-name="Path 11872" d="M172.809,339.046q5.815.379,11.842.382v19.743a170.353,170.353,0,0,1-38.448-4.245V335.169a163.733,163.733,0,0,0,26.606,3.877Z" transform="translate(-92.344 -248.515)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11873" data-name="Path 11873" d="M234.9,367.147a40.092,40.092,0,0,1-.541,6.788.46.46,0,0,1-.461.375.453.453,0,0,1-.457-.375,42.565,42.565,0,0,1,0-13.573.453.453,0,0,1,.457-.375.46.46,0,0,1,.461.375,40.081,40.081,0,0,1,.541,6.785Zm12.233-1.208a41.734,41.734,0,0,1-.52,6.785.445.445,0,0,1-.88,0,44.545,44.545,0,0,1,0-13.573.445.445,0,0,1,.88,0,41.746,41.746,0,0,1,.52,6.788Zm12.111-2.078a43.98,43.98,0,0,1-.5,6.788.436.436,0,0,1-.422.375.427.427,0,0,1-.419-.375,46.392,46.392,0,0,1,0-13.573.427.427,0,0,1,.419-.375.436.436,0,0,1,.422.375,43.966,43.966,0,0,1,.5,6.785Zm11.894-3.034a46.013,46.013,0,0,1-.471,6.788.421.421,0,0,1-.4.375.416.416,0,0,1-.4-.375,48.746,48.746,0,0,1,0-13.573.416.416,0,0,1,.4-.375.421.421,0,0,1,.4.375,46,46,0,0,1,.471,6.785Zm11.545-4.129a48.218,48.218,0,0,1-.45,6.788.38.38,0,0,1-.761,0,51.372,51.372,0,0,1,0-13.573.38.38,0,0,1,.761,0,48.13,48.13,0,0,1,.45,6.785Zm10.969-5.423a51.217,51.217,0,0,1-.426,6.785.4.4,0,0,1-.363.375.391.391,0,0,1-.359-.375,53.856,53.856,0,0,1,0-13.573.391.391,0,0,1,.359-.375.4.4,0,0,1,.363.375,51.271,51.271,0,0,1,.426,6.788Zm9.919-7.085a54.038,54.038,0,0,1-.4,6.785.344.344,0,1,1-.684,0,57.077,57.077,0,0,1,0-13.573.344.344,0,1,1,.684,0,54.057,54.057,0,0,1,.4,6.788Zm-95.335,22.958a40.073,40.073,0,0,0,.544,6.788.468.468,0,0,0,.918,0,42.567,42.567,0,0,0,0-13.573.468.468,0,0,0-.918,0,40.063,40.063,0,0,0-.544,6.785Zm-12.229-1.208a42.15,42.15,0,0,0,.516,6.785.447.447,0,0,0,.883,0,44.54,44.54,0,0,0,0-13.573.447.447,0,0,0-.883,0,42.162,42.162,0,0,0-.516,6.788ZM183.9,363.86a43.993,43.993,0,0,0,.5,6.788.423.423,0,0,0,.841,0,46.71,46.71,0,0,0,0-13.573.423.423,0,0,0-.841,0,43.979,43.979,0,0,0-.5,6.785ZM172,360.826a45.987,45.987,0,0,0,.475,6.788.4.4,0,0,0,.8,0,49.11,49.11,0,0,0,0-13.573.4.4,0,0,0-.8,0,45.97,45.97,0,0,0-.475,6.785ZM160.46,356.7a48.771,48.771,0,0,0,.447,6.788.382.382,0,0,0,.764,0,51.369,51.369,0,0,0,0-13.573.382.382,0,0,0-.764,0,48.682,48.682,0,0,0-.447,6.785Zm-10.969-5.423a51.2,51.2,0,0,0,.426,6.785.362.362,0,1,0,.722,0,54.3,54.3,0,0,0,0-13.573.362.362,0,1,0-.722,0,51.255,51.255,0,0,0-.426,6.788Zm-9.922-7.085a54.005,54.005,0,0,0,.4,6.785.344.344,0,1,0,.684,0,57.09,57.09,0,0,0,0-13.573.344.344,0,1,0-.684,0,54.023,54.023,0,0,0-.4,6.788Zm80.96,23.371a38.555,38.555,0,0,0,.565,6.785.494.494,0,0,0,.96,0,41.013,41.013,0,0,0,0-13.573.494.494,0,0,0-.96,0,38.566,38.566,0,0,0-.565,6.788Zm-88.8-32.585a57.083,57.083,0,0,0,.38,6.785c.024.218.161.375.321.375s.3-.157.321-.375a60.715,60.715,0,0,0,0-13.573c-.024-.218-.16-.375-.321-.375s-.3.157-.321.375a57.147,57.147,0,0,0-.38,6.788Zm179.695,0a57.077,57.077,0,0,1-.38,6.785c-.028.218-.161.375-.321.375s-.3-.157-.325-.375a60.725,60.725,0,0,1,0-13.573c.028-.218.164-.375.325-.375s.293.157.321.375a57.14,57.14,0,0,1,.38,6.788Z" transform="translate(-128.73 -266.268)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11874" data-name="Path 11874" d="M131.068,355.924c0,25.384,40.916,45.96,91.388,45.96s91.385-20.576,91.385-45.96-40.916-45.96-91.385-45.96-91.388,20.579-91.388,45.96Z" transform="translate(-130.317 -309.329)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11875" data-name="Path 11875" d="M130.848,355.236c0,25.1,41.568,45.455,92.842,45.455s92.846-20.35,92.846-45.455-41.568-45.458-92.846-45.458-92.842,20.35-92.842,45.458Z" transform="translate(-130.848 -309.778)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11876" data-name="Path 11876" d="M133.366,349.668c-.6,22.408,36.638,41.537,83.178,42.728S301.3,376.387,301.9,353.978s-36.638-41.537-83.178-42.728-84.755,16.009-85.356,38.418Z" transform="translate(-124.79 -306.366)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11877" data-name="Path 11877" d="M133.529,352c0,22.576,37.473,40.875,83.692,40.875s83.7-18.3,83.7-40.875-37.473-40.878-83.7-40.878-83.692,18.3-83.692,40.878Z" transform="translate(-124.379 -306.54)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11878" data-name="Path 11878" d="M300.76,354.521c-2.671-21.4-39.049-38.353-83.538-38.353s-80.864,16.951-83.535,38.353a20.228,20.228,0,0,1-.157-2.522c0-22.579,37.473-40.878,83.692-40.878s83.7,18.3,83.7,40.878a20.228,20.228,0,0,1-.157,2.522Z" transform="translate(-124.379 -306.54)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11879" data-name="Path 11879" d="M217.159,312.6c-42.837,0-78.16,15.7-83.141,35.954a20.961,20.961,0,0,0-.444,2.4,21.027,21.027,0,0,0,.794,3.659,26.7,26.7,0,0,0,4.848,8.795l.738-.106,103.877-14.747,53.11-7.539C286.147,324.53,254.507,312.6,217.159,312.6Z" transform="translate(-124.271 -302.972)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11880" data-name="Path 11880" d="M139.613,353.079c.534-19.948,30.812-35.766,70.214-38.162,4.513-.369,9.145-.56,13.871-.56,41.823,0,76.461,15.05,82.55,34.691.22.546.415,1.1.59,1.652l8.964-1.276c-5.828-22.361-44.84-39.646-92.1-39.646-51.278,0-92.85,20.35-92.85,45.458v.123c.038,6.846,3.167,13.337,8.741,19.156l9.1-1.3a37.927,37.927,0,0,1-2.71-3.007,26.721,26.721,0,0,1-4.834-8.795,21.279,21.279,0,0,1-1.465-6.058,19.851,19.851,0,0,1-.07-2.28Z" transform="translate(-130.848 -309.778)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
  </g>
</svg>
