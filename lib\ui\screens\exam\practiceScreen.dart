import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/exam/cubits/completedExamsCubit.dart';
import 'package:flutterquiz/features/exam/cubits/examsCubit.dart';
import 'package:flutterquiz/features/exam/examRepository.dart';

import 'package:flutterquiz/features/exam/models/examResult.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/result_model.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/ui/screens/exam/qlist_model.dart';

import 'package:flutterquiz/ui/screens/exam/widgets/examKeyBottomSheetContainer.dart';
import 'package:flutterquiz/ui/screens/exam/widgets/examResultBottomSheetContainer.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/userDisabled.dart';
import 'package:flutterquiz/ui/screens/inAppPurchase.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';

import 'package:flutterquiz/ui/widgets/customBackButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PracticeScreen extends StatefulWidget {
  PracticeScreen({Key? key}) : super(key: key);

  @override
  _PracticeScreenState createState() => _PracticeScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider<ExamsCubit>(create: (_) => ExamsCubit(ExamRepository())),
          BlocProvider<CompletedExamsCubit>(
              create: (_) => CompletedExamsCubit(ExamRepository())),
        ],
        child: PracticeScreen(),
      ),
    );
  }
}

class _PracticeScreenState extends State<PracticeScreen> {
  int _currentSelectedTab = 1; //1 and 2
  Map timeDoQuiz = {};
  Map numAnswered = {};
  int currentSelectedQuestionIndex = 0;
  bool internet = false;
  late Future<List<Qlist>> futurePost;
  late Future<List<Result>> futureResult;
  bool getListSuccess = false;
  bool getResultSuccess = false;
  late ScrollController _completedExamScrollController = ScrollController()
    ..addListener(hasMoreResultScrollListener);

  void hasMoreResultScrollListener() {
    if (_completedExamScrollController.position.maxScrollExtent ==
        _completedExamScrollController.offset) {
      print("At the end of the list");
      if (context.read<CompletedExamsCubit>().hasMoreResult()) {
        //
        context.read<CompletedExamsCubit>().getMoreResult(
            userId: context.read<UserDetailsCubit>().getUserId(),
            languageId: UiUtils.getCurrentQuestionLanguageId(context));
      } else {
        print("No more result");
      }
    }
  }

  Future<void> setRollBackPoint() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setString("popBack", "practice");
  }

  Future<void> checkUserStatus() async {
    final authCubit = context.read<AuthCubit>();
    final status = await authCubit.getUserScrumPassStatus();
    if (status == false) {
      showDialog(context: context, builder: (_) => UserDisabledDialog());
    }
  }

  Future<void> getListResult() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user, 'exam': '0'};

      final response = await dio.post(apiUrl + "get_result_list_by_user",
          data: FormData.fromMap({
            "key": getUserInfor().appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data)
          }));
      if (response.statusCode == 200) {
        preferences.setString('result', response.data.toString());
        loadRemoteDatatSucceed = true;
      }
    }
  }

  Future<void> getListQuiz() async {
    print('getList');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      /* if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      } */
      if (Common.username != '') {
        preferences.setString('username', Common.username);
      }
      String user = preferences.getString('username') ?? '';
      print("getList----------------------------------2");
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {'user': user, 'exam': '0'};

      final response = await dio.post(getUserInfor().url,
          data: FormData.fromMap({
            "key": getUserInfor().appkey,
            "token": token,
            "info": info,
            "data": jsonEncode(data),
          }));
      if (response.statusCode == 200) {
        preferences.setString('quizList', response.data.toString());
        loadRemoteDatatSucceed = true;
      }
    }
  }

  Future refreshList() async {
    setState(() {
      //getListQuiz();
      futurePost = fetchPost();
    });
    await new Future.delayed(Duration(seconds: 1));
  }

  Future refreshResult() async {
    setState(() async {
      await getListResult();
      futureResult = fetchResult();
    });
    await new Future.delayed(Duration(seconds: 1));
  }

  @override
  void initState() {
    super.initState();
    checkUserStatus();
    getExams();
    futurePost = fetchPost();
    futureResult = fetchResult();
    getCompletedExams();
    setRollBackPoint();
  }

  @override
  void dispose() {
    _completedExamScrollController.removeListener(hasMoreResultScrollListener);
    _completedExamScrollController.dispose();
    super.dispose();
  }

  void getExams() {
    Future.delayed(Duration.zero, () {
      context.read<ExamsCubit>().getExams(
          userId: context.read<UserDetailsCubit>().getUserId(),
          languageId: UiUtils.getCurrentQuestionLanguageId(context));
    });
  }

  Future<List<Qlist>> fetchPost() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();

    String localQuestionList = preferences.getString('quizList') ?? '';

    if (localQuestionList != '') {
      final parsed =
          json.decode(localQuestionList).cast<Map<String, dynamic>>();
      List<Qlist> qlist =
          parsed.map<Qlist>((json) => Qlist.fromMap(json)).toList();
      for (int i = 0; i < qlist.length; i++) {
        //Future<double> test = getPercentage(qlist[i].quiz_name);
        if (preferences.getInt('timeDoQuiz_' + qlist[i].quid) != null) {
          timeDoQuiz[qlist[i].quid] =
              preferences.getInt('timeDoQuiz_' + qlist[i].quid);
        }
        String answered =
            preferences.getString('answeredText_' + qlist[i].quid) ?? '';
        if (answered == '') {
          numAnswered[qlist[i].quid] = '0';
        } else {
          int lengthAnswered = 0;
          List answeredList = jsonDecode(answered);
          for (int j = 0; j < answeredList.length; j++) {
            if (answeredList[j].isNotEmpty) {
              lengthAnswered++;
            }
          }
          numAnswered[qlist[i].quid] = lengthAnswered.toString();
        }
      }
      getListSuccess = true;
      return qlist;
    } else {
      throw Exception('Failed to load list');
    }
  }

  Future<List<Result>> fetchResult() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String resultString = await prefs.getString('result') ?? "";
    if (resultString != "") {
      final parsed = json.decode(resultString).cast<Map<String, dynamic>>();
      List<Result> qResult =
          parsed.map<Result>((json) => Result.fromMap(json)).toList();
      getResultSuccess = true;
      return qResult;
    } else {
      return <Result>[];
    }
  }

  void getCompletedExams() {
    Future.delayed(Duration.zero, () {
      /* context.read<CompletedExamsCubit>().getCompletedExams(
          userId: context.read<UserDetailsCubit>().getUserId(),
          languageId: UiUtils.getCurrentQuestionLanguageId(context)); */
      fetchPost();
    });
  }

  void showExamKeyBottomSheet(
      BuildContext context, Qlist exam) //Accept exam object as parameter
  {
    showModalBottomSheet(
        isDismissible: true,
        enableDrag: true,
        isScrollControlled: true,
        elevation: 5.0,
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        builder: (context) {
          return ExamKeyBottomSheetContainer(
            navigateToExamScreen: navigateToExamScreen,
            exam: exam,
            type: 'practice',
            examTheme: ExamTheme.Elite,
          );
        });
  }

  void showExamResultBottomSheet(BuildContext context,
      ExamResult examResult) //Accept exam object as parameter
  {
    showModalBottomSheet(
        isScrollControlled: true,
        elevation: 5.0,
        context: context,
        enableDrag: true,
        isDismissible: true,
        shape: RoundedRectangleBorder(
          borderRadius: UiUtils.getBottomSheetRadius(),
        ),
        builder: (context) {
          return ExamResultBottomSheetContainer(
            examResult: examResult,
          );
        });
  }

  void navigateToExamScreen() async {
    Navigator.of(context).pop();

    Navigator.of(context).pushNamed(Routes.exam).then((value) {
      Future.delayed(Duration(milliseconds: 100), () {
        if (mounted) {
          print("Fetch exam details again");
          //fetch exams again with fresh status
          context.read<ExamsCubit>().getExams(
              userId: context.read<UserDetailsCubit>().getUserId(),
              languageId: UiUtils.getCurrentQuestionLanguageId(context));
          //fetch completed exam again with fresh status
          context.read<CompletedExamsCubit>().getCompletedExams(
              userId: context.read<UserDetailsCubit>().getUserId(),
              languageId: UiUtils.getCurrentQuestionLanguageId(context));
        }
      });
    });
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.only(bottom: 15),
      child: Stack(
        children: [
          Positioned.fill(
            top: 15,
            child: Align(
              alignment: Alignment.center,
              child: Text(
                AppLocalization.of(context)!
                    .getTranslatedValues("practiceMode")!,
                style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Positioned.fill(
              top: 15,
              left: 25,
              child: Align(
                alignment: AlignmentDirectional.centerStart,
                child: CustomBackButton(
                  onTap: () {
                    Navigator.of(context).pushNamed(Routes.home);
                  },
                  removeSnackBars: false,
                  iconColor: Theme.of(context).primaryColor,
                ),
              )),
          Align(
            alignment: AlignmentDirectional.bottomCenter,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTabContainer(
                    AppLocalization.of(context)!
                        .getTranslatedValues("examList")!,
                    1),
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.1,
                ),
                _buildTabContainer(
                    AppLocalization.of(context)!
                        .getTranslatedValues("completed")!,
                    2),
              ],
            ),
          ),
        ],
      ),
      height: MediaQuery.of(context).size.height *
          (UiUtils.appBarHeightPercentage + 0.04),
      decoration: BoxDecoration(
          boxShadow: [UiUtils.buildAppbarShadow()],
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.0),
              bottomRight: Radius.circular(20.0))),
    );
  }

  Widget _buildTabContainer(String title, int index) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentSelectedTab = index;
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Text(
          title,
          style: TextStyle(
            color: Theme.of(context)
                .primaryColor
                .withOpacity(_currentSelectedTab == index ? 1.0 : 0.5),
            fontSize: 16.0,
          ),
        ),
      ),
    );
  }

  Widget _buildExamResults() {
    return BlocConsumer<CompletedExamsCubit, CompletedExamsState>(
      listener: (context, state) {
        if (state is CompletedExamsFetchFailure) {
          if (state.errorMessage == unauthorizedAccessCode) {
            UiUtils.showAlreadyLoggedInDialog(context: context);
          }
        }
      },
      bloc: context.read<CompletedExamsCubit>(),
      builder: (context, state) {
        /* if (state is CompletedExamsFetchInProgress ||
            state is CompletedExamsInitial) {
          return Center(
            child: CircularProgressContainer(
              useWhiteLoader: false,
            ),
          );
        }
        if (state is CompletedExamsFetchFailure) {
          return Center(
            child: ErrorContainer(
                errorMessageColor: Theme.of(context).primaryColor,
                errorMessage: AppLocalization.of(context)!.getTranslatedValues(
                    convertErrorCodeToLanguageKey(state.errorMessage)),
                onTapRetry: () {
                  getCompletedExams();
                },
                showErrorImage: true),
          );
        } */
        return /* ListView.builder(
          controller: _completedExamScrollController,
          padding: EdgeInsets.only(
            right: MediaQuery.of(context).size.width * (0.05),
            left: MediaQuery.of(context).size.width * (0.05),
            top: MediaQuery.of(context).size.height *
                    UiUtils.appBarHeightPercentage +
                10,
            bottom: MediaQuery.of(context).size.height * 0.075,
          ),
          itemCount:
              (state as CompletedExamsFetchSuccess).completedExams.length,
          itemBuilder: (context, index) {
            return _buildResultContainer(
              examResult: state.completedExams[index],
              hasMoreResultFetchError: state.hasMoreFetchError,
              index: index,
              totalExamResults: state.completedExams.length,
              hasMore: state.hasMore,
            );
          },
        ); */
            Padding(
          padding: EdgeInsets.only(
            right: MediaQuery.of(context).size.width * (0.05),
            left: MediaQuery.of(context).size.width * (0.05),
            top: MediaQuery.of(context).size.height *
                UiUtils.appBarHeightPercentage *
                (0.1),
            bottom: MediaQuery.of(context).size.height * 0.075,
          ),
          child: FutureBuilder<List<Result>>(
            future: futureResult,
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              //print(snapshot.data);
              if (snapshot.hasData) {
                if (snapshot.data?.isEmpty ?? true) {
                  return Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("noData"),
                        onTapRetry: () {
                          getCompletedExams();
                        },
                        showErrorImage: true),
                  );
                } else {
                  return RefreshIndicator(
                    onRefresh: refreshResult,
                    child: ListView.builder(
                      itemCount: snapshot.data.length,
                      itemBuilder: (BuildContext context, int index) {
                        return _buildResultContainer(snapshot.data[index]);
                      },
                    ),
                  );
                }
              } else if (snapshot.hasError) {
                if (internet == false) {
                  return Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("noInternet"),
                        onTapRetry: () {
                          getCompletedExams();
                        },
                        showErrorImage: true),
                  );
                } else {
                  return Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("defaultErrorMessage"),
                        onTapRetry: () {
                          getCompletedExams();
                        },
                        showErrorImage: true),
                  );
                }
              } else {
                return Scaffold(
                    body: Center(child: CircularProgressIndicator()));
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildTodayExams() {
    return BlocConsumer<ExamsCubit, ExamsState>(
      listener: (contexe, state) {
        if (state is ExamsFetchFailure) {
          // Fix tạm thời lỗi đã đăng nhập do api khác site
          // if (state.errorMessage == unauthorizedAccessCode) {
          //   UiUtils.showAlreadyLoggedInDialog(context: context);
          // }
        }
      },
      bloc: context.read<ExamsCubit>(),
      builder: (context, state) {
        /* if (state is ExamsFetchInProgress || state is ExamsInitial) {
          return Center(
            child: CircularProgressContainer(
              useWhiteLoader: false,
            ),
          );
        }
        if (state is ExamsFetchFailure) {
          return Center(
            child: ErrorContainer(
                errorMessageColor: Theme.of(context).primaryColor,
                errorMessage: AppLocalization.of(context)!.getTranslatedValues(
                    convertErrorCodeToLanguageKey(state.errorMessage)),
                onTapRetry: () {
                  getExams();
                },
                showErrorImage: true),
          );
        } */
        return /* ListView.builder(
          padding: EdgeInsets.only(
            right: MediaQuery.of(context).size.width * (0.05),
            left: MediaQuery.of(context).size.width * (0.05),
            top: MediaQuery.of(context).size.height *
                    UiUtils.appBarHeightPercentage +
                10,
            bottom: MediaQuery.of(context).size.height * 0.075,
          ),
          itemCount: (state as ExamsFetchSuccess).exams.length,
          itemBuilder: (context, index) {
            return _buildTodayExamContainer(state.exams[index]);
          },
        ); */
            Padding(
          padding: EdgeInsets.only(
            right: MediaQuery.of(context).size.width * (0.05),
            left: MediaQuery.of(context).size.width * (0.05),
            top: MediaQuery.of(context).size.height *
                UiUtils.appBarHeightPercentage *
                (0.1),
            bottom: 5,
          ),
          child: Column(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 5),
                  child: FutureBuilder<List<Qlist>>(
                    future: futurePost,
                    builder: (BuildContext context, AsyncSnapshot snapshot) {
                      //print(snapshot.data);
                      if (snapshot.hasData) {
                        if (snapshot.data?.isEmpty ?? true) {
                          return Center(
                            child: ErrorContainer(
                                errorMessageColor:
                                    Theme.of(context).primaryColor,
                                errorMessage: AppLocalization.of(context)!
                                    .getTranslatedValues("noData"),
                                onTapRetry: () {
                                  setState(() {
                                    fetchPost();
                                  });
                                },
                                showErrorImage: true),
                          );
                        } else {
                          return RefreshIndicator(
                            onRefresh: refreshList,
                            child: ListView.builder(
                              itemCount: snapshot.data.length,
                              itemBuilder: (BuildContext context, int index) {
                                return _buildTodayExamContainer(
                                    snapshot.data[index]);
                              },
                            ),
                          );
                        }
                      } else if (snapshot.hasError) {
                        if (internet == false) {
                          return Center(
                            child: ErrorContainer(
                                errorMessageColor:
                                    Theme.of(context).primaryColor,
                                errorMessage: AppLocalization.of(context)!
                                    .getTranslatedValues("noInternet"),
                                onTapRetry: () {
                                  getCompletedExams();
                                },
                                showErrorImage: true),
                          );
                        } else {
                          return Center(
                            child: ErrorContainer(
                                errorMessageColor:
                                    Theme.of(context).primaryColor,
                                errorMessage: AppLocalization.of(context)!
                                    .getTranslatedValues("defaultErrorMessage"),
                                onTapRetry: () {
                                  getCompletedExams();
                                },
                                showErrorImage: true),
                          );
                        }
                      } else {
                        return Scaffold(
                            body: Center(child: CircularProgressIndicator()));
                      }
                    },
                  ),
                ),
              ),
              // if (!Common.premium &&
              //     Common.registeredFromApp &&
              //     !Common.inPremiumGroup)
              //   Container(
              //     margin: EdgeInsets.symmetric(vertical: 10),
              //     width: double.infinity,
              //     // height: 40,
              //     child: ElevatedButton(
              //         style: ElevatedButton.styleFrom(
              //             // minimumSize: Size.fromHeight(40),
              //             primary: Theme.of(context).primaryColor),
              //         child: Padding(
              //           padding: const EdgeInsets.all(10),
              //           child: Text(
              //             AppLocalization.of(context)!
              //                 .getTranslatedValues("unlockAllExams")!,
              //             textAlign: TextAlign.center,
              //             style: TextStyle(
              //                 color: Colors.white,
              //                 fontSize: 16.0,
              //                 fontWeight: FontWeight.bold),
              //           ),
              //         ),
              //         onPressed: () {
              //           Navigator.push(
              //             context,
              //             MaterialPageRoute(
              //                 builder: (context) => PaymentPage()),
              //           );
              //         }),
              //   ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTodayExamContainer(Qlist exam) {
    return GestureDetector(
      onTap: () {
        showExamKeyBottomSheet(context, exam);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: 8.0),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(10.0)),
        height: MediaQuery.of(context).size.height * (0.12),
        margin: EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width * (0.5),
                  child: Text(
                    exam.quiz_name,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                      fontSize: 17.25,
                    ),
                  ),
                ),
                Spacer(),
                Container(
                  alignment: Alignment.centerRight,
                  width: MediaQuery.of(context).size.width * (0.3),
                  child: Text(
                    "${exam.noq} ${AppLocalization.of(context)!.getTranslatedValues("questions")!}",
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                      fontSize: 17.25,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width * (0.5),
                  child: Text(
                    "${AppLocalization.of(context)!.getTranslatedValues('examDuration')!}:",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .surface
                          .withOpacity(0.8),
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  /* UiUtils.convertMinuteIntoHHMM(int.parse(exam.duration))!="noLimit"?UiUtils.convertMinuteIntoHHMM(int.parse(exam.duration)):AppLocalization.of(context)!.getTranslatedValues("noLimit")! */
                  exam.duration == "0"
                      ? AppLocalization.of(context)!
                          .getTranslatedValues("noLimit")!
                      : exam.duration +
                          " ${AppLocalization.of(context)!.getTranslatedValues("minute")!}",
                  style: TextStyle(
                    color:
                        Theme.of(context).colorScheme.surface.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultContainer(Result examResult) {
    /* if (index == totalExamResults - 1) {
      //check if hasMore
      if (hasMore) {
        if (hasMoreResultFetchError) {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: 8.0),
              child: IconButton(
                  onPressed: () {
                    context.read<CompletedExamsCubit>().getMoreResult(
                        userId: context.read<UserDetailsCubit>().getUserId(),
                        languageId:
                            UiUtils.getCurrentQuestionLanguageId(context));
                  },
                  icon: Icon(
                    Icons.error,
                    color: Theme.of(context).primaryColor,
                  )),
            ),
          );
        } else {
          return Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: 8.0),
              child: CircularProgressContainer(
                useWhiteLoader: false,
                heightAndWidth: 40,
              ),
            ),
          );
        }
      }
    } */
    final endTime = DateTime.fromMillisecondsSinceEpoch(
        int.parse(examResult.endTime ?? "0") * 1000);
    print(examResult.quiz_name);
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(Routes.resultSummary, arguments: {
          "rid": examResult.rid,
          "quid": examResult.quid,
          "quiz_name": examResult.quiz_name,
          "pass_percentage": examResult.pass_percentage,
          "duration": examResult.duration,
          "retry": true,
          "backButtonStatus": true,
          "examTheme": ExamTheme.Elite
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: 15.0),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(10.0)),
        // height: MediaQuery.of(context).size.height * (0.1),
        //height: MediaQuery.of(context).size.height * (0.12),
        margin: EdgeInsets.symmetric(vertical: 10.0),
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      width: MediaQuery.of(context).size.width * (0.6),
                      child: Text(
                        examResult.quiz_name == null
                            ? "${AppLocalization.of(context)!.getTranslatedValues("deletedQuiz")!}"
                            : "${examResult.quiz_name}",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.surface,
                          fontSize: 17.25,
                        ),
                      ),
                    ),
                    Spacer(),
                    Text(
                      "${UiUtils.removeDecimalZeroFormat(double.parse(examResult.percentage_obtained ?? "0"))}% ",
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.surface,
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 5),
                  child: Row(
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        width: MediaQuery.of(context).size.width * (0.6),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${AppLocalization.of(context)!.getTranslatedValues("completedIn")!}: ${(int.parse(examResult.total_time ?? "0") / 60).round()} ${AppLocalization.of(context)!.getTranslatedValues("minute")!}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: Theme.of(context)
                                    .colorScheme.surface
                                    .withOpacity(0.8),
                              ),
                            ),
                            Text(
                              "${DateFormat('dd-MM-yyyy HH:mm').format(endTime)}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: Theme.of(context)
                                    .colorScheme.surface
                                    .withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      Text(
                        "${examResult.result_status} ",
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.surface,
                          fontSize: 25,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /*  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageBackgroundGradientContainer(),
          Align(
            alignment: Alignment.topCenter,
            child: _currentSelectedTab == 1
                ? _buildTodayExams()
                : _buildExamResults(),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: _buildAppBar(),
          ),
          Align(alignment: Alignment.bottomCenter, child: BannerAdContainer()),
        ],
      ),
    );
  } */
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 2,
        child: Builder(builder: (BuildContext context) {
          return Scaffold(
            appBar: AppBar(
                backgroundColor: Theme.of(context).colorScheme.surface,
                leading: CustomBackButton(
                  iconColor: Theme.of(context).primaryColor,
                  onTap: () {
                    /* Navigator.of(context).pushNamedAndRemoveUntil(
                        Routes.home, (Route<dynamic> route) => false); */
                    //Navigator.popUntil(context, (route) => route.isFirst);
                    Navigator.pop(context, "refresh");
                  },
                ),
                centerTitle: true,
                title: Padding(
                  padding: EdgeInsets.only(top: 5),
                  child: Text(
                    AppLocalization.of(context)!
                        .getTranslatedValues("practiceMode")!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 22.0),
                  ),
                ),
                bottom: TabBar(
                    labelPadding: EdgeInsetsDirectional.only(
                        top: MediaQuery.of(context).size.height * .0),
                    labelColor: Theme.of(context).primaryColor,
                    unselectedLabelColor: Theme.of(context)
                        .colorScheme
                        .secondary
                        .withOpacity(0.7),
                    labelStyle: Theme.of(context).textTheme.titleMedium,
                    indicatorColor: Theme.of(context).primaryColor,
                    indicatorSize: TabBarIndicatorSize.tab,
                    indicatorWeight: 5,
                    tabs: [
                      Tab(
                          text: AppLocalization.of(context)!
                              .getTranslatedValues("examList")!),
                      Tab(
                          text: AppLocalization.of(context)!
                              .getTranslatedValues("completed")!),
                    ])),
            body: Stack(
              children: [
                PageBackgroundGradientContainer(),
                TabBarView(children: [_buildTodayExams(), _buildExamResults()])
              ],
            ),
          );
        }));
  }
}
