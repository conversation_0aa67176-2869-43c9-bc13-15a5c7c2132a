<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="248.69" height="251.221" viewBox="0 0 248.69 251.221">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b38746"/>
      <stop offset="0.389" stop-color="#d9973b"/>
      <stop offset="0.75" stop-color="#ad6c10"/>
      <stop offset="0.858" stop-color="#d3953d"/>
      <stop offset="1" stop-color="#b07f35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffee76"/>
      <stop offset="1" stop-color="#1d1d1b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#e6a729"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f0cc62"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e0c0a8"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-13" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-16" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#e0c0a8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-22" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-40" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-57" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-64" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1d1d1b"/>
      <stop offset="1" stop-color="#ffee76"/>
    </linearGradient>
    <linearGradient id="linear-gradient-66" x1="0.179" y1="1.022" x2="1.022" y2="0.179" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-67" x1="0.789" y1="0.789" x2="0.211" y2="0.211" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e6a729"/>
      <stop offset="1" stop-color="#ffe878"/>
    </linearGradient>
    <linearGradient id="linear-gradient-68" x1="0.92" y1="0.906" x2="0.28" y2="0.093" xlink:href="#linear-gradient-64"/>
    <linearGradient id="linear-gradient-69" x1="0.789" y1="0.789" x2="0.211" y2="0.211" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ed9e1f"/>
      <stop offset="1" stop-color="#ffe878"/>
    </linearGradient>
    <linearGradient id="linear-gradient-70" x1="-0.05" y1="0.837" x2="0.837" y2="-0.05" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#bf8f2e"/>
      <stop offset="1" stop-color="#fccd67"/>
    </linearGradient>
    <linearGradient id="linear-gradient-71" x1="0.927" y1="0.903" x2="0.287" y2="0.082" xlink:href="#linear-gradient-64"/>
    <linearGradient id="linear-gradient-72" x1="0.052" y1="0.901" x2="0.976" y2="0.123" xlink:href="#linear-gradient-64"/>
    <linearGradient id="linear-gradient-73" x1="0.337" y1="0.897" x2="0.663" y2="0.029" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-74" x1="0.223" y1="0.631" x2="0.774" y2="0.369" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-75" x1="0.077" y1="0.834" x2="0.921" y2="0.166" xlink:href="#linear-gradient-64"/>
  </defs>
  <g id="_05_coins" data-name="05_coins" transform="translate(-33.714 -159.496)">
    <g id="Group_3359" data-name="Group 3359" transform="translate(114.94 159.496)">
      <path id="Path_11942" data-name="Path 11942" d="M69.087,221.058c0,20.388,33.159,36.914,74.057,36.914S217.2,241.446,217.2,221.058l0-16.129H69.087v16.129Z" transform="translate(-60.794 -78.544)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11943" data-name="Path 11943" d="M110.916,221.415q4.74.309,9.653.312a144.977,144.977,0,0,0,19.442-1.288v16.1a110.4,110.4,0,0,1-19.442,1.285c-10.479,0-27.526-2-38.812-5.469V216.238a127.779,127.779,0,0,0,29.159,5.177Z" transform="translate(-38.219 -58.393)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11944" data-name="Path 11944" d="M113.851,231.809c14.371-6.7,23.412-16.223,23.587-26.805l0,15.92c0,10.668-9.077,20.276-23.59,27.017V231.809Z" transform="translate(18.966 -78.41)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11945" data-name="Path 11945" d="M84.3,227.322v16.148c-9.539-6.214-15.208-13.984-15.208-22.413V204.929c.047,8.42,5.708,16.182,15.208,22.393Z" transform="translate(-60.794 -78.544)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11946" data-name="Path 11946" d="M106.132,220.123q4.74.309,9.653.312v16.1a138.739,138.739,0,0,1-31.342-3.463v-16.1a133.5,133.5,0,0,0,21.69,3.16Z" transform="translate(-33.435 -57.102)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11947" data-name="Path 11947" d="M152.2,241.671a33.675,33.675,0,0,1-.431,5.53.373.373,0,0,1-.734,0,35.7,35.7,0,0,1,0-11.063.373.373,0,0,1,.734,0,33.709,33.709,0,0,1,.431,5.533Zm9.75-.988a35.11,35.11,0,0,1-.414,5.533.354.354,0,0,1-.7,0,37.119,37.119,0,0,1,0-11.063.354.354,0,0,1,.7,0,35.047,35.047,0,0,1,.414,5.53Zm9.65-1.691a36.613,36.613,0,0,1-.4,5.53.337.337,0,0,1-.67,0,38.928,38.928,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.652,36.652,0,0,1,.4,5.533Zm9.483-2.473a38.31,38.31,0,0,1-.378,5.53.319.319,0,0,1-.637,0,40.63,40.63,0,0,1,0-11.063.319.319,0,0,1,.637,0,38.321,38.321,0,0,1,.378,5.533Zm9.2-3.369a40.171,40.171,0,0,1-.359,5.533.3.3,0,1,1-.606,0,42.816,42.816,0,0,1,0-11.063.3.3,0,1,1,.606,0,40.159,40.159,0,0,1,.359,5.53Zm8.743-4.42a42.192,42.192,0,0,1-.342,5.533.316.316,0,0,1-.287.306.32.32,0,0,1-.289-.306,45.251,45.251,0,0,1,0-11.063.321.321,0,0,1,.289-.309.316.316,0,0,1,.287.309,42.117,42.117,0,0,1,.342,5.53Zm7.906-5.775a44.384,44.384,0,0,1-.323,5.53c-.022.181-.136.309-.27.309s-.25-.128-.273-.309a47.571,47.571,0,0,1,0-11.063.273.273,0,1,1,.542,0,44.466,44.466,0,0,1,.323,5.533Zm-75.987,18.716a33.673,33.673,0,0,0,.431,5.53.373.373,0,0,0,.734,0,35.7,35.7,0,0,0,0-11.063.373.373,0,0,0-.734,0,33.708,33.708,0,0,0-.431,5.533Zm-9.75-.988a35.108,35.108,0,0,0,.414,5.533.354.354,0,0,0,.7,0,37.121,37.121,0,0,0,0-11.063.354.354,0,0,0-.7,0,35.045,35.045,0,0,0-.414,5.53Zm-9.65-1.691a36.61,36.61,0,0,0,.4,5.53.337.337,0,0,0,.67,0,38.926,38.926,0,0,0,0-11.063.337.337,0,0,0-.67,0,36.649,36.649,0,0,0-.4,5.533Zm-9.483-2.473a38.3,38.3,0,0,0,.378,5.53.334.334,0,0,0,.32.306.339.339,0,0,0,.32-.306,40.926,40.926,0,0,0,0-11.063.339.339,0,0,0-.32-.306.335.335,0,0,0-.32.306,38.314,38.314,0,0,0-.378,5.533Zm-9.2-3.369a40.615,40.615,0,0,0,.359,5.533.3.3,0,1,0,.609,0,42.813,42.813,0,0,0,0-11.063.3.3,0,1,0-.609,0,40.6,40.6,0,0,0-.359,5.53Zm-8.74-4.42a42.222,42.222,0,0,0,.339,5.533.288.288,0,1,0,.576,0,45.249,45.249,0,0,0,0-11.063.289.289,0,1,0-.576,0,42.147,42.147,0,0,0-.339,5.53Zm-7.909-5.775a44.385,44.385,0,0,0,.323,5.53c.022.181.136.309.273.309s.25-.128.273-.309a47.985,47.985,0,0,0,0-11.063.274.274,0,1,0-.545,0,44.467,44.467,0,0,0-.323,5.533ZM140.746,242a32.094,32.094,0,0,0,.451,5.53.392.392,0,0,0,.765,0,34.173,34.173,0,0,0,0-11.063.392.392,0,0,0-.765,0,32.126,32.126,0,0,0-.451,5.533Zm-70.78-26.561a47.655,47.655,0,0,0,.3,5.533c.022.178.131.306.259.306s.236-.128.256-.306a50.612,50.612,0,0,0,0-11.063c-.02-.181-.128-.309-.256-.309s-.236.128-.259.309a47.57,47.57,0,0,0-.3,5.53Zm143.226,0a47.612,47.612,0,0,1-.3,5.533c-.022.178-.128.306-.259.306s-.234-.128-.256-.306a50.612,50.612,0,0,1,0-11.063c.022-.181.128-.309.256-.309s.236.128.259.309a47.527,47.527,0,0,1,.3,5.53Z" transform="translate(-59.228 -70.212)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11948" data-name="Path 11948" d="M69.308,229.221c0,20.688,32.625,37.462,72.866,37.462s72.869-16.774,72.869-37.462-32.625-37.462-72.869-37.462S69.308,208.53,69.308,229.221Z" transform="translate(-60.401 -102.01)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11949" data-name="Path 11949" d="M69.087,228.625c0,20.463,33.156,37.051,74.057,37.051S217.2,249.088,217.2,228.625s-33.156-37.054-74.057-37.054-74.057,16.591-74.057,37.054Z" transform="translate(-60.794 -102.343)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11950" data-name="Path 11950" d="M71.6,224.349c-.478,18.263,29.159,33.857,66.2,34.825s67.453-13.049,67.931-31.315-29.162-33.857-66.2-34.825S72.083,206.084,71.6,224.349Z" transform="translate(-56.319 -99.822)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11951" data-name="Path 11951" d="M71.769,226.234c0,18.4,29.815,33.32,66.6,33.32s66.6-14.919,66.6-33.32-29.815-33.32-66.6-33.32-66.6,14.919-66.6,33.32Z" transform="translate(-56.016 -99.952)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11952" data-name="Path 11952" d="M71.49,226.328c0,18.2,29.479,32.945,65.845,32.945s65.845-14.749,65.845-32.945-29.481-32.942-65.845-32.942S71.49,208.135,71.49,226.328Z" transform="translate(-56.513 -99.111)" fill-rule="evenodd" fill="url(#linear-gradient-8)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11953" data-name="Path 11953" d="M204.836,228.29c-2.122-17.442-31.07-31.262-66.471-31.262s-64.348,13.82-66.471,31.262a16.85,16.85,0,0,1-.125-2.056c0-18.4,29.815-33.32,66.6-33.32s66.6,14.919,66.6,33.32a16.85,16.85,0,0,1-.125,2.056Z" transform="translate(-56.016 -99.952)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11954" data-name="Path 11954" d="M73.063,214.778c0,20.388,33.153,36.914,74.054,36.914s74.057-16.527,74.057-36.914l0-16.132H73.063v16.132Z" transform="translate(-53.71 -89.739)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11955" data-name="Path 11955" d="M114.891,215.133q4.74.309,9.653.312a144.509,144.509,0,0,0,19.442-1.291v16.1a110.4,110.4,0,0,1-19.442,1.285c-10.479,0-27.526-2-38.812-5.472V209.956a127.929,127.929,0,0,0,29.159,5.177Z" transform="translate(-31.137 -69.587)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-11)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11956" data-name="Path 11956" d="M117.826,225.527c14.371-6.7,23.412-16.223,23.587-26.805l0,15.92c0,10.668-9.077,20.277-23.59,27.014V225.527Z" transform="translate(26.048 -89.603)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11957" data-name="Path 11957" d="M88.268,221.042v16.148c-9.539-6.215-15.205-13.984-15.205-22.413V198.646c.045,8.423,5.705,16.182,15.205,22.4Z" transform="translate(-53.71 -89.739)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-13)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11958" data-name="Path 11958" d="M110.107,213.841q4.74.309,9.653.312v16.1a138.894,138.894,0,0,1-31.342-3.463v-16.1a133.542,133.542,0,0,0,21.69,3.16Z" transform="translate(-26.352 -68.295)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11959" data-name="Path 11959" d="M156.181,235.389a33.7,33.7,0,0,1-.431,5.53.373.373,0,0,1-.734,0,35.7,35.7,0,0,1,0-11.063.373.373,0,0,1,.734,0,33.71,33.71,0,0,1,.431,5.533Zm9.75-.988a35.1,35.1,0,0,1-.415,5.533.354.354,0,0,1-.7,0,37.137,37.137,0,0,1,0-11.066.354.354,0,0,1,.7,0,35.1,35.1,0,0,1,.415,5.533Zm9.65-1.691a36.281,36.281,0,0,1-.4,5.53.337.337,0,0,1-.67,0,38.927,38.927,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.292,36.292,0,0,1,.4,5.533Zm9.483-2.473a38.3,38.3,0,0,1-.378,5.53.319.319,0,0,1-.637,0,40.628,40.628,0,0,1,0-11.063.319.319,0,0,1,.637,0,38.315,38.315,0,0,1,.378,5.533Zm9.2-3.369a40.172,40.172,0,0,1-.359,5.533.3.3,0,1,1-.609,0,43.146,43.146,0,0,1,0-11.063.3.3,0,1,1,.609,0,40.13,40.13,0,0,1,.359,5.53Zm8.743-4.42a42.194,42.194,0,0,1-.342,5.533.316.316,0,0,1-.287.306.32.32,0,0,1-.289-.306,45.27,45.27,0,0,1,0-11.066.32.32,0,0,1,.289-.306.316.316,0,0,1,.287.306,42.192,42.192,0,0,1,.342,5.533Zm7.906-5.775a44.416,44.416,0,0,1-.323,5.53c-.022.178-.134.306-.27.306a.311.311,0,0,1-.273-.306,47.572,47.572,0,0,1,0-11.063.311.311,0,0,1,.273-.306c.136,0,.248.128.27.306a44.464,44.464,0,0,1,.323,5.533Zm-75.988,18.716a33.376,33.376,0,0,0,.434,5.53.363.363,0,0,0,.364.306.368.368,0,0,0,.367-.306,35.469,35.469,0,0,0,0-11.063.368.368,0,0,0-.367-.306.363.363,0,0,0-.364.306,33.387,33.387,0,0,0-.434,5.533Zm-9.75-.988a35.1,35.1,0,0,0,.414,5.533.355.355,0,0,0,.7,0,37.385,37.385,0,0,0,0-11.066.355.355,0,0,0-.7,0,35.1,35.1,0,0,0-.414,5.533Zm-9.65-1.691a36.639,36.639,0,0,0,.395,5.53.337.337,0,0,0,.67,0,38.929,38.929,0,0,0,0-11.063.337.337,0,0,0-.67,0,36.651,36.651,0,0,0-.395,5.533Zm-9.483-2.473a38.3,38.3,0,0,0,.378,5.53.319.319,0,0,0,.637,0,40.63,40.63,0,0,0,0-11.063.319.319,0,0,0-.637,0,38.316,38.316,0,0,0-.378,5.533Zm-9.2-3.369A40.615,40.615,0,0,0,97.2,232.4a.3.3,0,1,0,.609,0,42.814,42.814,0,0,0,0-11.063.3.3,0,1,0-.609,0,40.572,40.572,0,0,0-.359,5.53Zm-8.743-4.42a42.194,42.194,0,0,0,.342,5.533.288.288,0,1,0,.576,0,45.273,45.273,0,0,0,0-11.066.288.288,0,1,0-.576,0,42.192,42.192,0,0,0-.342,5.533Zm-7.906-5.775a44.422,44.422,0,0,0,.323,5.53.274.274,0,1,0,.545,0,47.986,47.986,0,0,0,0-11.063.274.274,0,1,0-.545,0,44.47,44.47,0,0,0-.323,5.533Zm64.529,19.05a32.117,32.117,0,0,0,.451,5.53.392.392,0,0,0,.765,0,34.174,34.174,0,0,0,0-11.063.392.392,0,0,0-.765,0,32.127,32.127,0,0,0-.451,5.533ZM73.94,209.162a46.966,46.966,0,0,0,.306,5.533c.019.178.128.306.256.306s.236-.128.256-.306a50.633,50.633,0,0,0,0-11.066c-.019-.178-.128-.306-.256-.306s-.236.128-.256.306a46.968,46.968,0,0,0-.306,5.533Zm143.229,0a47.612,47.612,0,0,1-.3,5.533c-.022.178-.128.306-.259.306s-.234-.128-.256-.306a50.633,50.633,0,0,1,0-11.066c.022-.178.128-.306.256-.306s.236.128.259.306a47.613,47.613,0,0,1,.3,5.533Z" transform="translate(-52.147 -81.405)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11960" data-name="Path 11960" d="M73.283,222.941c0,20.688,32.625,37.46,72.866,37.46s72.869-16.771,72.869-37.46-32.625-37.465-72.869-37.465S73.283,202.25,73.283,222.941Z" transform="translate(-53.318 -113.205)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11961" data-name="Path 11961" d="M73.063,222.344c0,20.463,33.153,37.051,74.054,37.051s74.057-16.588,74.057-37.051-33.156-37.054-74.057-37.054-74.054,16.588-74.054,37.054Z" transform="translate(-53.71 -113.536)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11962" data-name="Path 11962" d="M75.58,218.066c-.478,18.265,29.159,33.857,66.2,34.828s67.453-13.049,67.931-31.315-29.162-33.857-66.2-34.828S76.058,199.8,75.58,218.066Z" transform="translate(-49.236 -111.017)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11963" data-name="Path 11963" d="M75.744,219.952c0,18.4,29.815,33.32,66.6,33.32s66.6-14.919,66.6-33.32-29.815-33.32-66.6-33.32-66.6,14.916-66.6,33.32Z" transform="translate(-48.933 -111.145)" fill-rule="evenodd" fill="url(#linear-gradient-16)"/>
      <path id="Path_11964" data-name="Path 11964" d="M208.811,222.008c-2.123-17.445-31.07-31.262-66.471-31.262s-64.348,13.817-66.471,31.262a16.85,16.85,0,0,1-.125-2.056c0-18.4,29.815-33.32,66.6-33.32s66.6,14.916,66.6,33.32a16.857,16.857,0,0,1-.125,2.056Z" transform="translate(-48.933 -111.145)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11965" data-name="Path 11965" d="M74.534,220.406c0,18.193,29.481,32.945,65.845,32.945s65.845-14.752,65.845-32.945-29.479-32.945-65.845-32.945S74.534,202.21,74.534,220.406Z" transform="translate(-51.089 -109.668)" fill-rule="evenodd" fill="url(#linear-gradient-18)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11966" data-name="Path 11966" d="M67.1,208.5c0,20.385,33.156,36.912,74.057,36.912s74.054-16.527,74.054-36.912l0-16.132H67.1V208.5Z" transform="translate(-64.335 -100.932)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11967" data-name="Path 11967" d="M108.928,208.853c3.16.206,6.384.312,9.656.312a144.541,144.541,0,0,0,19.442-1.291v16.1a110.147,110.147,0,0,1-19.442,1.285c-10.482,0-27.529-2-38.814-5.469V203.673a127.8,127.8,0,0,0,29.159,5.18Z" transform="translate(-41.761 -80.782)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11968" data-name="Path 11968" d="M111.863,219.245c14.374-6.7,23.414-16.223,23.587-26.805l0,15.92c0,10.665-9.074,20.276-23.59,27.014V219.245Z" transform="translate(15.424 -100.797)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-21)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11969" data-name="Path 11969" d="M82.305,214.757v16.148C72.767,224.694,67.1,216.924,67.1,208.5V192.364c.045,8.421,5.705,16.182,15.205,22.393Z" transform="translate(-64.335 -100.932)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-22)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11970" data-name="Path 11970" d="M104.142,207.559c3.16.206,6.384.312,9.656.312v16.093A138.867,138.867,0,0,1,82.455,220.5V204.4a133.463,133.463,0,0,0,21.687,3.16Z" transform="translate(-36.975 -79.488)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11971" data-name="Path 11971" d="M150.219,229.1a33.388,33.388,0,0,1-.434,5.533.372.372,0,0,1-.732,0,35.472,35.472,0,0,1,0-11.063.372.372,0,0,1,.732,0,33.378,33.378,0,0,1,.434,5.53Zm9.747-.985a34.769,34.769,0,0,1-.414,5.53.354.354,0,0,1-.7,0,37.12,37.12,0,0,1,0-11.063.354.354,0,0,1,.7,0,34.779,34.779,0,0,1,.414,5.533Zm9.653-1.694a36.652,36.652,0,0,1-.4,5.533.337.337,0,0,1-.67,0,38.927,38.927,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.642,36.642,0,0,1,.4,5.53Zm9.48-2.473a38.347,38.347,0,0,1-.376,5.533.32.32,0,0,1-.64,0,40.929,40.929,0,0,1,0-11.063.32.32,0,0,1,.64,0,38.336,38.336,0,0,1,.376,5.53Zm9.2-3.366a40.173,40.173,0,0,1-.359,5.533.3.3,0,1,1-.606,0,42.813,42.813,0,0,1,0-11.063.3.3,0,1,1,.606,0,40.1,40.1,0,0,1,.359,5.53Zm8.743-4.42a42.633,42.633,0,0,1-.339,5.53.288.288,0,1,1-.576,0,44.885,44.885,0,0,1,0-11.063.288.288,0,1,1,.576,0,42.71,42.71,0,0,1,.339,5.533Zm7.909-5.775a45,45,0,0,1-.323,5.53.274.274,0,1,1-.545,0,47.571,47.571,0,0,1,0-11.063.274.274,0,1,1,.545,0,45.009,45.009,0,0,1,.323,5.533ZM128.963,229.1a33.385,33.385,0,0,0,.434,5.533.368.368,0,0,0,.367.306.363.363,0,0,0,.364-.306,35.472,35.472,0,0,0,0-11.063.363.363,0,0,0-.364-.306.368.368,0,0,0-.367.306,33.375,33.375,0,0,0-.434,5.53Zm-9.747-.985a34.766,34.766,0,0,0,.414,5.53.354.354,0,0,0,.7,0,37.118,37.118,0,0,0,0-11.063.354.354,0,0,0-.7,0,34.775,34.775,0,0,0-.414,5.533Zm-9.653-1.694a36.646,36.646,0,0,0,.4,5.533.348.348,0,0,0,.337.306.343.343,0,0,0,.334-.306,38.657,38.657,0,0,0,0-11.063.343.343,0,0,0-.334-.306.348.348,0,0,0-.337.306,36.636,36.636,0,0,0-.4,5.53Zm-9.48-2.473a38.341,38.341,0,0,0,.376,5.533.32.32,0,0,0,.64,0,40.63,40.63,0,0,0,0-11.063.32.32,0,0,0-.64,0,38.33,38.33,0,0,0-.376,5.53Zm-9.2-3.366a40.177,40.177,0,0,0,.359,5.533.3.3,0,1,0,.606,0,42.814,42.814,0,0,0,0-11.063.3.3,0,1,0-.606,0,40.1,40.1,0,0,0-.359,5.53Zm-8.743-4.42a42.633,42.633,0,0,0,.339,5.53.29.29,0,1,0,.579,0,45.252,45.252,0,0,0,0-11.063.29.29,0,1,0-.579,0,42.71,42.71,0,0,0-.339,5.533Zm-7.906-5.775a45.034,45.034,0,0,0,.32,5.53.274.274,0,1,0,.545,0,47.573,47.573,0,0,0,0-11.063.274.274,0,1,0-.545,0,45.047,45.047,0,0,0-.32,5.533Zm64.526,19.05a32.114,32.114,0,0,0,.451,5.53.392.392,0,0,0,.765,0,34.173,34.173,0,0,0,0-11.063.392.392,0,0,0-.765,0,32.123,32.123,0,0,0-.451,5.533ZM67.978,202.88a47.6,47.6,0,0,0,.3,5.53c.022.178.128.306.256.306s.236-.128.259-.306a50.609,50.609,0,0,0,0-11.063c-.022-.178-.131-.306-.259-.306s-.234.128-.256.306a47.614,47.614,0,0,0-.3,5.533Zm143.226,0a47.6,47.6,0,0,1-.3,5.53c-.019.178-.128.306-.256.306s-.236-.128-.256-.306a50.607,50.607,0,0,1,0-11.063c.019-.178.128-.306.256-.306s.236.128.256.306a47.616,47.616,0,0,1,.3,5.533Z" transform="translate(-62.77 -92.599)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11972" data-name="Path 11972" d="M67.32,216.656c0,20.691,32.625,37.462,72.869,37.462s72.866-16.771,72.866-37.462-32.622-37.462-72.866-37.462S67.32,195.968,67.32,216.656Z" transform="translate(-63.943 -124.398)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11973" data-name="Path 11973" d="M67.1,216.061c0,20.463,33.156,37.051,74.057,37.051s74.054-16.588,74.054-37.051-33.156-37.054-74.054-37.054S67.1,195.6,67.1,216.061Z" transform="translate(-64.335 -124.73)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11974" data-name="Path 11974" d="M69.617,211.784c-.478,18.265,29.162,33.857,66.2,34.828s67.453-13.049,67.931-31.315-29.159-33.857-66.2-34.828S70.1,193.519,69.617,211.784Z" transform="translate(-59.861 -122.21)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11975" data-name="Path 11975" d="M69.782,213.67c0,18.4,29.815,33.318,66.6,33.318s66.6-14.916,66.6-33.318-29.815-33.32-66.6-33.32-66.6,14.916-66.6,33.32Z" transform="translate(-59.556 -122.338)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11976" data-name="Path 11976" d="M202.849,215.726c-2.122-17.445-31.073-31.262-66.471-31.262S72.03,198.281,69.9,215.726a17.461,17.461,0,0,1-.122-2.056c0-18.4,29.815-33.32,66.6-33.32s66.6,14.916,66.6,33.32a16.877,16.877,0,0,1-.125,2.056Z" transform="translate(-59.556 -122.338)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11977" data-name="Path 11977" d="M70.009,213.906c0,18.193,29.479,32.942,65.845,32.942S201.7,232.1,201.7,213.906s-29.479-32.945-65.842-32.945S70.009,195.71,70.009,213.906Z" transform="translate(-59.152 -121.25)" fill-rule="evenodd" fill="url(#linear-gradient-8)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11978" data-name="Path 11978" d="M71.075,202.214c0,20.385,33.156,36.912,74.057,36.912s74.054-16.527,74.054-36.912V186.082H71.075v16.132Z" transform="translate(-57.252 -112.125)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11979" data-name="Path 11979" d="M112.9,202.571c3.16.206,6.384.312,9.656.312A145,145,0,0,0,142,201.592v16.1a110.421,110.421,0,0,1-19.442,1.285c-10.482,0-27.529-2-38.814-5.469V197.391a127.808,127.808,0,0,0,29.159,5.18Z" transform="translate(-34.679 -91.975)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11980" data-name="Path 11980" d="M115.838,212.961c14.374-6.7,23.414-16.221,23.59-26.8v15.92c0,10.665-9.074,20.277-23.59,27.014V212.961Z" transform="translate(22.506 -111.99)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11981" data-name="Path 11981" d="M86.28,208.475v16.148c-9.539-6.215-15.205-13.981-15.205-22.41V186.082c.045,8.42,5.705,16.182,15.205,22.393Z" transform="translate(-57.252 -112.125)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-22)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11982" data-name="Path 11982" d="M108.117,201.277c3.16.206,6.384.312,9.656.312v16.093a138.864,138.864,0,0,1-31.342-3.461v-16.1a133.772,133.772,0,0,0,21.687,3.16Z" transform="translate(-29.893 -90.681)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11983" data-name="Path 11983" d="M154.194,222.822a33.39,33.39,0,0,1-.434,5.533.372.372,0,0,1-.732,0,35.473,35.473,0,0,1,0-11.063.372.372,0,0,1,.732,0,33.381,33.381,0,0,1,.434,5.53Zm9.747-.985a35.117,35.117,0,0,1-.412,5.53.355.355,0,0,1-.7,0,37.118,37.118,0,0,1,0-11.063.355.355,0,0,1,.7,0,35.126,35.126,0,0,1,.412,5.533Zm9.653-1.694a36.649,36.649,0,0,1-.4,5.533.337.337,0,0,1-.67,0,38.93,38.93,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.637,36.637,0,0,1,.4,5.53Zm9.48-2.473a38.346,38.346,0,0,1-.376,5.533.32.32,0,0,1-.64,0,40.929,40.929,0,0,1,0-11.063.32.32,0,0,1,.64,0,38.3,38.3,0,0,1,.376,5.53Zm9.2-3.366a40.17,40.17,0,0,1-.359,5.533.3.3,0,1,1-.606,0,42.835,42.835,0,0,1,0-11.066.3.3,0,1,1,.606,0,40.169,40.169,0,0,1,.359,5.533Zm8.743-4.42a42.7,42.7,0,0,1-.339,5.53.288.288,0,1,1-.576,0,44.882,44.882,0,0,1,0-11.063.288.288,0,1,1,.576,0,42.713,42.713,0,0,1,.339,5.533Zm7.909-5.778a45.014,45.014,0,0,1-.323,5.533.274.274,0,1,1-.545,0,47.575,47.575,0,0,1,0-11.063.274.274,0,1,1,.545,0,45,45,0,0,1,.323,5.53Zm-75.988,18.716a33.4,33.4,0,0,0,.431,5.533.373.373,0,0,0,.734,0,35.7,35.7,0,0,0,0-11.063.373.373,0,0,0-.734,0,33.394,33.394,0,0,0-.431,5.53Zm-9.75-.985a34.766,34.766,0,0,0,.414,5.53.354.354,0,0,0,.7,0,37.122,37.122,0,0,0,0-11.063.354.354,0,0,0-.7,0,34.775,34.775,0,0,0-.414,5.533Zm-9.653-1.694a36.652,36.652,0,0,0,.4,5.533.35.35,0,0,0,.337.306.346.346,0,0,0,.334-.306,38.661,38.661,0,0,0,0-11.063.346.346,0,0,0-.334-.306.35.35,0,0,0-.337.306,36.639,36.639,0,0,0-.4,5.53Zm-9.48-2.473a38.342,38.342,0,0,0,.376,5.533.32.32,0,0,0,.64,0,40.628,40.628,0,0,0,0-11.063.32.32,0,0,0-.64,0,38.3,38.3,0,0,0-.376,5.53Zm-9.2-3.366a40.175,40.175,0,0,0,.359,5.533.3.3,0,1,0,.606,0,42.832,42.832,0,0,0,0-11.066.3.3,0,1,0-.606,0,40.174,40.174,0,0,0-.359,5.533Zm-8.743-4.42a42.7,42.7,0,0,0,.339,5.53.29.29,0,1,0,.579,0,45.252,45.252,0,0,0,0-11.063.29.29,0,1,0-.579,0,42.715,42.715,0,0,0-.339,5.533Zm-7.906-5.778a45.044,45.044,0,0,0,.32,5.533.274.274,0,1,0,.545,0,47.574,47.574,0,0,0,0-11.063.274.274,0,1,0-.545,0,45.031,45.031,0,0,0-.32,5.53Zm64.527,19.05a32.127,32.127,0,0,0,.451,5.533.392.392,0,0,0,.765,0,34.172,34.172,0,0,0,0-11.063.392.392,0,0,0-.765,0,32.117,32.117,0,0,0-.451,5.53ZM71.953,196.6a47.6,47.6,0,0,0,.3,5.53c.022.178.128.306.256.306s.236-.128.259-.306a50.608,50.608,0,0,0,0-11.063c-.022-.178-.131-.306-.259-.306s-.234.128-.256.306a47.615,47.615,0,0,0-.3,5.533Zm143.226,0a47.6,47.6,0,0,1-.3,5.53c-.019.178-.128.306-.256.306s-.236-.128-.256-.306a50.609,50.609,0,0,1,0-11.063c.019-.178.128-.306.256-.306s.236.128.256.306a47.62,47.62,0,0,1,.3,5.533Z" transform="translate(-55.688 -103.792)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11984" data-name="Path 11984" d="M71.295,210.374c0,20.691,32.625,37.462,72.869,37.462s72.866-16.771,72.866-37.462-32.622-37.462-72.866-37.462-72.869,16.774-72.869,37.462Z" transform="translate(-56.86 -135.591)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11985" data-name="Path 11985" d="M71.075,209.777c0,20.463,33.156,37.053,74.057,37.053s74.054-16.591,74.054-37.053-33.153-37.051-74.054-37.051-74.057,16.588-74.057,37.051Z" transform="translate(-57.252 -135.923)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11986" data-name="Path 11986" d="M73.592,205.5c-.478,18.265,29.162,33.857,66.2,34.828s67.45-13.052,67.929-31.315-29.159-33.857-66.2-34.828S74.07,187.237,73.592,205.5Z" transform="translate(-52.778 -133.404)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11987" data-name="Path 11987" d="M73.757,207.387c0,18.4,29.815,33.32,66.6,33.32s66.6-14.919,66.6-33.32-29.818-33.32-66.6-33.32-66.6,14.919-66.6,33.32Z" transform="translate(-52.473 -133.533)" fill-rule="evenodd" fill="url(#linear-gradient-16)"/>
      <path id="Path_11988" data-name="Path 11988" d="M72.56,207.57c0,18.193,29.479,32.945,65.845,32.945s65.842-14.752,65.842-32.945-29.479-32.945-65.842-32.945S72.56,189.374,72.56,207.57Z" transform="translate(-54.606 -132.539)" fill-rule="evenodd" fill="url(#linear-gradient-18)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11989" data-name="Path 11989" d="M206.824,209.446C204.7,192,175.751,178.184,140.353,178.184S76,192,73.879,209.446a17.475,17.475,0,0,1-.122-2.059c0-18.4,29.815-33.32,66.6-33.32s66.6,14.919,66.6,33.32a16.9,16.9,0,0,1-.125,2.059Z" transform="translate(-52.473 -133.533)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11990" data-name="Path 11990" d="M66.106,195.932c0,20.385,33.156,36.912,74.057,36.912s74.057-16.527,74.057-36.912l0-16.132H66.106v16.132Z" transform="translate(-66.106 -123.318)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11991" data-name="Path 11991" d="M107.937,196.289q4.74.309,9.653.312a144.971,144.971,0,0,0,19.442-1.291v16.1a110.422,110.422,0,0,1-19.442,1.285c-10.479,0-27.526-2-38.814-5.469V191.109a127.824,127.824,0,0,0,29.162,5.18Z" transform="translate(-43.532 -103.168)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11992" data-name="Path 11992" d="M110.87,206.679c14.371-6.7,23.412-16.221,23.587-26.8l0,15.92c0,10.665-9.077,20.274-23.59,27.014V206.679Z" transform="translate(13.654 -123.183)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11993" data-name="Path 11993" d="M81.314,202.193v16.148c-9.539-6.215-15.208-13.984-15.208-22.41V179.8c.047,8.42,5.705,16.182,15.208,22.393Z" transform="translate(-66.106 -123.318)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-40)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11994" data-name="Path 11994" d="M103.151,195q4.74.309,9.653.312V211.4a138.884,138.884,0,0,1-31.342-3.461V191.834A133.75,133.75,0,0,0,103.151,195Z" transform="translate(-38.747 -101.876)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11995" data-name="Path 11995" d="M149.225,216.54a33.386,33.386,0,0,1-.434,5.533.363.363,0,0,1-.364.306.368.368,0,0,1-.367-.306,35.7,35.7,0,0,1,0-11.063.368.368,0,0,1,.367-.309.363.363,0,0,1,.364.309,33.325,33.325,0,0,1,.434,5.53Zm9.75-.988a35.107,35.107,0,0,1-.415,5.533.355.355,0,0,1-.7,0,37.363,37.363,0,0,1,0-11.063.355.355,0,0,1,.7,0,35.1,35.1,0,0,1,.415,5.53Zm9.65-1.691a36.651,36.651,0,0,1-.4,5.533.337.337,0,0,1-.67,0,38.93,38.93,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.612,36.612,0,0,1,.4,5.53Zm9.483-2.473a38.316,38.316,0,0,1-.378,5.533.335.335,0,0,1-.32.306.339.339,0,0,1-.32-.306,40.928,40.928,0,0,1,0-11.063.339.339,0,0,1,.32-.309.335.335,0,0,1,.32.309,38.248,38.248,0,0,1,.378,5.53Zm9.2-3.366a40.6,40.6,0,0,1-.359,5.53.3.3,0,1,1-.609,0,42.813,42.813,0,0,1,0-11.063.3.3,0,1,1,.609,0,40.617,40.617,0,0,1,.359,5.533Zm8.74-4.423a42.227,42.227,0,0,1-.339,5.533.288.288,0,1,1-.576,0,45.251,45.251,0,0,1,0-11.063.288.288,0,1,1,.576,0,42.216,42.216,0,0,1,.339,5.53Zm7.909-5.775a44.472,44.472,0,0,1-.323,5.533.274.274,0,1,1-.545,0,47.985,47.985,0,0,1,0-11.063.274.274,0,1,1,.545,0,44.39,44.39,0,0,1,.323,5.53ZM127.972,216.54a33.709,33.709,0,0,0,.431,5.533.373.373,0,0,0,.734,0,35.7,35.7,0,0,0,0-11.063.373.373,0,0,0-.734,0,33.647,33.647,0,0,0-.431,5.53Zm-9.75-.988a35.1,35.1,0,0,0,.415,5.533.354.354,0,0,0,.7,0,37.118,37.118,0,0,0,0-11.063.354.354,0,0,0-.7,0,35.094,35.094,0,0,0-.415,5.53Zm-9.65-1.691a36.293,36.293,0,0,0,.4,5.533.337.337,0,0,0,.67,0,38.93,38.93,0,0,0,0-11.063.337.337,0,0,0-.67,0,36.254,36.254,0,0,0-.4,5.53Zm-9.483-2.473a37.921,37.921,0,0,0,.378,5.533.319.319,0,0,0,.637,0,40.628,40.628,0,0,0,0-11.063.319.319,0,0,0-.637,0,37.854,37.854,0,0,0-.378,5.53Zm-9.2-3.366a40.16,40.16,0,0,0,.359,5.53.3.3,0,1,0,.606,0,42.815,42.815,0,0,0,0-11.063.3.3,0,1,0-.606,0,40.173,40.173,0,0,0-.359,5.533ZM81.143,203.6a42.193,42.193,0,0,0,.342,5.533.316.316,0,0,0,.287.306.32.32,0,0,0,.289-.306,45.249,45.249,0,0,0,0-11.063.32.32,0,0,0-.289-.306.316.316,0,0,0-.287.306,42.182,42.182,0,0,0-.342,5.53Zm-7.906-5.775a45.052,45.052,0,0,0,.32,5.533.274.274,0,1,0,.545,0,47.574,47.574,0,0,0,0-11.063.274.274,0,1,0-.545,0,44.968,44.968,0,0,0-.32,5.53Zm64.527,19.05a32.111,32.111,0,0,0,.453,5.533.39.39,0,0,0,.762,0,34.176,34.176,0,0,0,0-11.063.39.39,0,0,0-.762,0,32.054,32.054,0,0,0-.453,5.53Zm-70.78-26.561a47.616,47.616,0,0,0,.3,5.533c.022.178.128.306.259.306s.234-.128.256-.306a50.607,50.607,0,0,0,0-11.063c-.022-.178-.128-.306-.256-.306s-.236.128-.259.306a47.6,47.6,0,0,0-.3,5.53Zm143.226,0a47,47,0,0,1-.3,5.533c-.019.178-.128.306-.256.306s-.236-.128-.256-.306a50.609,50.609,0,0,1,0-11.063c.019-.178.128-.306.256-.306s.236.128.256.306a46.987,46.987,0,0,1,.3,5.53Z" transform="translate(-64.542 -114.985)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11996" data-name="Path 11996" d="M66.327,204.092c0,20.691,32.622,37.462,72.866,37.462s72.869-16.771,72.869-37.462-32.625-37.462-72.869-37.462S66.327,183.4,66.327,204.092Z" transform="translate(-65.712 -146.785)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11997" data-name="Path 11997" d="M66.106,203.495c0,20.463,33.156,37.054,74.057,37.054s74.057-16.591,74.057-37.054-33.156-37.051-74.057-37.051-74.057,16.588-74.057,37.051Z" transform="translate(-66.106 -147.116)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11998" data-name="Path 11998" d="M68.623,199.22c-.476,18.265,29.162,33.857,66.2,34.825S202.277,221,202.752,202.734s-29.159-33.857-66.2-34.828S69.1,180.955,68.623,199.22Z" transform="translate(-61.632 -144.597)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11999" data-name="Path 11999" d="M68.788,201.105c0,18.4,29.815,33.32,66.6,33.32s66.6-14.919,66.6-33.32-29.815-33.32-66.6-33.32-66.6,14.919-66.6,33.32Z" transform="translate(-61.327 -144.727)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_12000" data-name="Path 12000" d="M201.855,203.164c-2.123-17.445-31.073-31.265-66.471-31.265s-64.348,13.82-66.471,31.265a16.933,16.933,0,0,1-.125-2.059c0-18.4,29.815-33.32,66.6-33.32s66.6,14.919,66.6,33.32a16.93,16.93,0,0,1-.125,2.059Z" transform="translate(-61.327 -144.727)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12001" data-name="Path 12001" d="M68.939,200.656c0,18.2,29.481,32.945,65.845,32.945s65.845-14.749,65.845-32.945-29.479-32.942-65.845-32.942-65.845,14.749-65.845,32.942Z" transform="translate(-61.058 -144.853)" fill-rule="evenodd" fill="url(#linear-gradient-8)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12002" data-name="Path 12002" d="M70.081,188.984c0,20.385,33.156,36.912,74.057,36.912s74.057-16.527,74.057-36.912l0-16.132H70.081v16.132Z" transform="translate(-59.023 -135.698)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12003" data-name="Path 12003" d="M111.912,189.341c3.157.206,6.381.312,9.653.312a144.519,144.519,0,0,0,19.442-1.291v16.1a110.413,110.413,0,0,1-19.442,1.285c-10.479,0-27.529-2-38.814-5.469V184.161a127.8,127.8,0,0,0,29.162,5.18Z" transform="translate(-36.45 -115.548)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12004" data-name="Path 12004" d="M114.845,199.731c14.371-6.7,23.412-16.221,23.587-26.8l0,15.92c0,10.665-9.077,20.276-23.59,27.014V199.731Z" transform="translate(20.737 -135.563)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12005" data-name="Path 12005" d="M85.289,195.245v16.148c-9.539-6.215-15.208-13.981-15.208-22.41V172.852c.047,8.421,5.705,16.182,15.208,22.393Z" transform="translate(-59.023 -135.698)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-40)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12006" data-name="Path 12006" d="M107.126,188.047c3.157.206,6.381.312,9.653.312v16.093a138.9,138.9,0,0,1-31.342-3.461v-16.1a133.752,133.752,0,0,0,21.69,3.16Z" transform="translate(-31.664 -114.254)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12007" data-name="Path 12007" d="M153.2,209.592a33.384,33.384,0,0,1-.434,5.533.363.363,0,0,1-.364.306.368.368,0,0,1-.367-.306,35.7,35.7,0,0,1,0-11.063.368.368,0,0,1,.367-.306.363.363,0,0,1,.364.306,33.373,33.373,0,0,1,.434,5.53Zm9.747-.988a35.128,35.128,0,0,1-.412,5.533.354.354,0,0,1-.7,0,37.118,37.118,0,0,1,0-11.063.354.354,0,0,1,.7,0,35.118,35.118,0,0,1,.412,5.53Zm9.653-1.691a36.649,36.649,0,0,1-.4,5.533.337.337,0,0,1-.67,0,38.929,38.929,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.639,36.639,0,0,1,.4,5.53Zm9.483-2.473a38.312,38.312,0,0,1-.378,5.533.334.334,0,0,1-.32.306.339.339,0,0,1-.32-.306,40.928,40.928,0,0,1,0-11.063.339.339,0,0,1,.32-.306.334.334,0,0,1,.32.306,38.273,38.273,0,0,1,.378,5.53Zm9.2-3.366a40.615,40.615,0,0,1-.359,5.533.3.3,0,1,1-.609,0,42.833,42.833,0,0,1,0-11.066.3.3,0,1,1,.609,0,40.614,40.614,0,0,1,.359,5.533Zm8.74-4.42a42.216,42.216,0,0,1-.339,5.53.288.288,0,1,1-.576,0,45.251,45.251,0,0,1,0-11.063.288.288,0,1,1,.576,0,42.232,42.232,0,0,1,.339,5.533Zm7.909-5.778a44.47,44.47,0,0,1-.323,5.533.274.274,0,1,1-.545,0,47.987,47.987,0,0,1,0-11.063.274.274,0,1,1,.545,0,44.457,44.457,0,0,1,.323,5.53Zm-75.987,18.716a33.711,33.711,0,0,0,.431,5.533.373.373,0,0,0,.734,0,35.7,35.7,0,0,0,0-11.063.373.373,0,0,0-.734,0,33.7,33.7,0,0,0-.431,5.53Zm-9.75-.988a35.106,35.106,0,0,0,.415,5.533.354.354,0,0,0,.7,0,37.119,37.119,0,0,0,0-11.063.354.354,0,0,0-.7,0,35.1,35.1,0,0,0-.415,5.53Zm-9.653-1.691a36.623,36.623,0,0,0,.4,5.533.337.337,0,0,0,.67,0,38.929,38.929,0,0,0,0-11.063.337.337,0,0,0-.67,0,36.614,36.614,0,0,0-.4,5.53Zm-9.48-2.473a38.316,38.316,0,0,0,.378,5.533.319.319,0,0,0,.637,0,40.628,40.628,0,0,0,0-11.063.319.319,0,0,0-.637,0,38.277,38.277,0,0,0-.378,5.53Zm-9.2-3.366a40.175,40.175,0,0,0,.359,5.533.3.3,0,1,0,.606,0,42.835,42.835,0,0,0,0-11.066.3.3,0,1,0-.606,0,40.175,40.175,0,0,0-.359,5.533Zm-8.743-4.42a42.18,42.18,0,0,0,.342,5.53.288.288,0,1,0,.576,0,45.248,45.248,0,0,0,0-11.063.288.288,0,1,0-.576,0,42.2,42.2,0,0,0-.342,5.533Zm-7.906-5.778a44.47,44.47,0,0,0,.323,5.533.273.273,0,1,0,.542,0,47.571,47.571,0,0,0,0-11.063.273.273,0,1,0-.542,0,44.457,44.457,0,0,0-.323,5.53Zm64.527,19.05a32.4,32.4,0,0,0,.451,5.533.392.392,0,0,0,.765,0,34.174,34.174,0,0,0,0-11.063.392.392,0,0,0-.765,0,32.394,32.394,0,0,0-.451,5.53Zm-70.78-26.558a47.6,47.6,0,0,0,.3,5.53c.022.178.128.306.259.306s.234-.128.256-.306a50.61,50.61,0,0,0,0-11.063c-.022-.178-.128-.306-.256-.306s-.236.128-.259.306a47.613,47.613,0,0,0-.3,5.533Zm143.226,0a46.992,46.992,0,0,1-.3,5.53c-.019.178-.128.306-.256.306s-.236-.128-.256-.306a50.607,50.607,0,0,1,0-11.063c.019-.178.128-.306.256-.306s.236.128.256.306a47.007,47.007,0,0,1,.3,5.533Z" transform="translate(-57.459 -127.365)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12008" data-name="Path 12008" d="M70.3,197.144c0,20.691,32.622,37.462,72.866,37.462s72.869-16.771,72.869-37.462-32.625-37.462-72.869-37.462S70.3,176.456,70.3,197.144Z" transform="translate(-58.63 -159.165)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12009" data-name="Path 12009" d="M70.081,196.547c0,20.466,33.156,37.053,74.057,37.053s74.057-16.588,74.057-37.053S185.039,159.5,144.138,159.5s-74.057,16.588-74.057,37.051Z" transform="translate(-59.023 -159.496)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12010" data-name="Path 12010" d="M72.6,192.272c-.478,18.265,29.159,33.857,66.2,34.828s67.453-13.052,67.929-31.315-29.159-33.857-66.2-34.828S73.074,174.007,72.6,192.272Z" transform="translate(-54.548 -156.977)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12011" data-name="Path 12011" d="M72.763,194.157c0,18.4,29.815,33.32,66.6,33.32s66.6-14.916,66.6-33.32-29.815-33.32-66.6-33.32-66.6,14.919-66.6,33.32Z" transform="translate(-54.245 -157.107)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_12012" data-name="Path 12012" d="M205.83,196.216c-2.123-17.445-31.073-31.265-66.471-31.265s-64.348,13.82-66.471,31.265a16.9,16.9,0,0,1-.125-2.058c0-18.4,29.815-33.32,66.6-33.32s66.6,14.919,66.6,33.32a16.9,16.9,0,0,1-.125,2.058Z" transform="translate(-54.245 -157.107)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12013" data-name="Path 12013" d="M139.279,162.316c-34.066,0-62.159,12.8-66.118,29.309a16.758,16.758,0,0,0-.353,1.956,17.509,17.509,0,0,0,.631,2.982,21.9,21.9,0,0,0,3.856,7.169l.587-.086,82.605-12.02,42.236-6.145c-8.585-13.436-33.743-23.164-63.444-23.164Z" transform="translate(-54.164 -154.471)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12014" data-name="Path 12014" d="M77.074,194.791c.426-16.26,24.572-29.153,56-31.106,3.6-.3,7.3-.459,11.066-.459,33.354,0,60.983,12.27,65.84,28.28.175.445.331.9.467,1.346l7.149-1.04c-4.648-18.226-35.763-32.316-73.456-32.316-40.9,0-74.057,16.588-74.057,37.051v.1c.031,5.58,2.526,10.871,6.971,15.614l7.263-1.057a30.166,30.166,0,0,1-2.161-2.451,21.9,21.9,0,0,1-3.856-7.169,17.688,17.688,0,0,1-1.171-4.938,16.524,16.524,0,0,1-.053-1.858Z" transform="translate(-59.023 -159.496)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
    <g id="Group_3360" data-name="Group 3360" transform="translate(125.965 320.52)">
      <path id="Path_12015" data-name="Path 12015" d="M70.069,250.094c0,20.388,33.153,36.912,74.054,36.912s74.057-16.524,74.057-36.912l0-16.132H70.069v16.132Z" transform="translate(-70.069 -196.808)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12016" data-name="Path 12016" d="M111.9,250.451q4.74.309,9.653.312a144.516,144.516,0,0,0,19.442-1.291v16.1a110.127,110.127,0,0,1-19.442,1.285c-10.479,0-27.526-2-38.812-5.469V245.271a127.778,127.778,0,0,0,29.159,5.18Z" transform="translate(-47.495 -176.658)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-57)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12017" data-name="Path 12017" d="M114.832,260.843c14.371-6.7,23.412-16.223,23.587-26.805l0,15.92c0,10.665-9.077,20.277-23.59,27.014V260.843Z" transform="translate(9.689 -196.673)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12018" data-name="Path 12018" d="M85.274,256.355V272.5c-9.539-6.212-15.205-13.981-15.205-22.41V233.962c.045,8.42,5.7,16.182,15.205,22.393Z" transform="translate(-70.069 -196.808)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-13)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12019" data-name="Path 12019" d="M107.113,249.157q4.74.309,9.653.312v16.093A138.9,138.9,0,0,1,85.423,262.1V246a133.541,133.541,0,0,0,21.69,3.16Z" transform="translate(-42.711 -175.364)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12020" data-name="Path 12020" d="M153.185,270.7a33.71,33.71,0,0,1-.431,5.533.373.373,0,0,1-.734,0,35.7,35.7,0,0,1,0-11.063.373.373,0,0,1,.734,0,33.7,33.7,0,0,1,.431,5.53Zm9.75-.985a35.068,35.068,0,0,1-.414,5.53.354.354,0,0,1-.7,0,37.12,37.12,0,0,1,0-11.063.354.354,0,0,1,.7,0,35.106,35.106,0,0,1,.414,5.533Zm9.65-1.691a36.282,36.282,0,0,1-.4,5.53.337.337,0,0,1-.67,0,38.926,38.926,0,0,1,0-11.063.337.337,0,0,1,.67,0,36.292,36.292,0,0,1,.4,5.533Zm9.483-2.476a38.321,38.321,0,0,1-.378,5.533.319.319,0,0,1-.637,0,40.627,40.627,0,0,1,0-11.063.319.319,0,0,1,.637,0,38.311,38.311,0,0,1,.378,5.53Zm9.2-3.366a40.172,40.172,0,0,1-.359,5.533.328.328,0,0,1-.3.306.324.324,0,0,1-.3-.306,42.811,42.811,0,0,1,0-11.063.321.321,0,0,1,.3-.306.326.326,0,0,1,.3.306,40.1,40.1,0,0,1,.359,5.53Zm8.743-4.42a42.193,42.193,0,0,1-.342,5.533.313.313,0,0,1-.287.3.317.317,0,0,1-.289-.3,45.27,45.27,0,0,1,0-11.066.32.32,0,0,1,.289-.306.316.316,0,0,1,.287.306,42.193,42.193,0,0,1,.342,5.533Zm7.906-5.775a44.451,44.451,0,0,1-.323,5.53.273.273,0,1,1-.542,0,47.57,47.57,0,0,1,0-11.063.273.273,0,1,1,.542,0,44.467,44.467,0,0,1,.323,5.533ZM131.932,270.7a33.709,33.709,0,0,0,.431,5.533.373.373,0,0,0,.734,0,35.7,35.7,0,0,0,0-11.063.373.373,0,0,0-.734,0,33.7,33.7,0,0,0-.431,5.53Zm-9.75-.985a35.067,35.067,0,0,0,.414,5.53.354.354,0,0,0,.7,0,37.12,37.12,0,0,0,0-11.063.354.354,0,0,0-.7,0,35.1,35.1,0,0,0-.414,5.533Zm-9.65-1.691a36.634,36.634,0,0,0,.4,5.53.337.337,0,0,0,.67,0,38.929,38.929,0,0,0,0-11.063.337.337,0,0,0-.67,0,36.644,36.644,0,0,0-.4,5.533Zm-9.483-2.476a38.315,38.315,0,0,0,.378,5.533.319.319,0,0,0,.637,0,40.632,40.632,0,0,0,0-11.063.319.319,0,0,0-.637,0,38.3,38.3,0,0,0-.378,5.53Zm-9.2-3.366a40.174,40.174,0,0,0,.359,5.533.3.3,0,1,0,.609,0,42.812,42.812,0,0,0,0-11.063.3.3,0,1,0-.609,0,40.1,40.1,0,0,0-.359,5.53Zm-8.74-4.42a42.231,42.231,0,0,0,.339,5.533.288.288,0,1,0,.576,0,45.273,45.273,0,0,0,0-11.066.288.288,0,1,0-.576,0,42.232,42.232,0,0,0-.339,5.533ZM77.2,251.989a44.455,44.455,0,0,0,.323,5.53.274.274,0,1,0,.545,0,47.982,47.982,0,0,0,0-11.063.274.274,0,1,0-.545,0,44.47,44.47,0,0,0-.323,5.533Zm64.529,19.047a32.124,32.124,0,0,0,.451,5.533.377.377,0,0,0,.381.306.382.382,0,0,0,.384-.306,34.175,34.175,0,0,0,0-11.063.382.382,0,0,0-.384-.306.377.377,0,0,0-.381.306,32.115,32.115,0,0,0-.451,5.53Zm-70.78-26.558a47.606,47.606,0,0,0,.3,5.53c.022.178.131.306.259.306s.236-.128.256-.306a50.609,50.609,0,0,0,0-11.063c-.019-.178-.128-.306-.256-.306s-.236.128-.259.306a47.659,47.659,0,0,0-.3,5.533Zm143.226,0a47.569,47.569,0,0,1-.3,5.53c-.022.178-.128.306-.259.306s-.234-.128-.256-.306a50.61,50.61,0,0,1,0-11.063c.022-.178.128-.306.256-.306s.236.128.259.306a47.622,47.622,0,0,1,.3,5.533Z" transform="translate(-68.505 -188.475)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12021" data-name="Path 12021" d="M70.289,258.254c0,20.691,32.625,37.462,72.866,37.462s72.869-16.771,72.869-37.462-32.625-37.462-72.869-37.462-72.866,16.774-72.866,37.462Z" transform="translate(-69.677 -220.275)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12022" data-name="Path 12022" d="M70.069,257.659c0,20.463,33.153,37.051,74.054,37.051s74.057-16.588,74.057-37.051-33.156-37.053-74.057-37.053-74.054,16.588-74.054,37.053Z" transform="translate(-70.069 -220.606)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12023" data-name="Path 12023" d="M72.586,253.382c-.478,18.265,29.159,33.857,66.2,34.828s67.453-13.049,67.931-31.315-29.162-33.857-66.2-34.828-67.45,13.049-67.929,31.315Z" transform="translate(-65.595 -218.086)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12024" data-name="Path 12024" d="M72.75,255.268c0,18.4,29.815,33.318,66.6,33.318s66.6-14.916,66.6-33.318-29.815-33.32-66.6-33.32-66.6,14.916-66.6,33.32Z" transform="translate(-65.292 -218.215)" fill-rule="evenodd" fill="url(#linear-gradient-16)"/>
      <path id="Path_12025" data-name="Path 12025" d="M205.817,257.324c-2.123-17.445-31.07-31.262-66.471-31.262S75,239.879,72.875,257.324a16.852,16.852,0,0,1-.125-2.056c0-18.4,29.815-33.32,66.6-33.32s66.6,14.916,66.6,33.32a16.86,16.86,0,0,1-.125,2.056Z" transform="translate(-65.292 -218.215)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12026" data-name="Path 12026" d="M139.266,223.427c-34.066,0-62.159,12.8-66.118,29.306a17.473,17.473,0,0,0-.353,1.956,17.509,17.509,0,0,0,.631,2.982,21.927,21.927,0,0,0,3.856,7.169l.587-.086,82.608-12.02,42.236-6.145c-8.587-13.433-33.746-23.161-63.447-23.161Z" transform="translate(-65.212 -215.58)" fill-rule="evenodd" fill="url(#linear-gradient-64)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12027" data-name="Path 12027" d="M77.06,255.9c.426-16.26,24.572-29.153,56-31.106,3.6-.3,7.294-.456,11.063-.456,33.356,0,60.982,12.268,65.839,28.277q.263.672.47,1.346l7.146-1.04c-4.646-18.226-35.763-32.316-73.456-32.316-40.9,0-74.054,16.588-74.054,37.053v.1c.028,5.58,2.526,10.871,6.968,15.614l7.263-1.057a29.713,29.713,0,0,1-2.161-2.451,21.926,21.926,0,0,1-3.856-7.169,17.66,17.66,0,0,1-1.168-4.938,16.527,16.527,0,0,1-.056-1.858Z" transform="translate(-70.069 -220.606)" fill-rule="evenodd" fill="url(#linear-gradient-64)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
    <g id="Group_3361" data-name="Group 3361" transform="translate(33.714 253.809)">
      <path id="Path_12028" data-name="Path 12028" d="M149.514,208.769c19.993,20,13,59.405-15.628,88.03s-68.034,35.618-88.027,15.625l-7.945-7.948L141.566,200.824l7.948,7.945Z" transform="translate(-26.232 -189.142)" fill-rule="evenodd" fill="url(#linear-gradient-66)"/>
      <path id="Path_12029" data-name="Path 12029" d="M149.049,208.306c20.265,20.265,13.492,59.9-15.133,88.522s-68.254,35.4-88.522,15.13-13.489-59.9,15.133-88.519,68.257-35.4,88.522-15.133Z" transform="translate(-33.714 -196.625)" fill-rule="evenodd" fill="url(#linear-gradient-67)"/>
      <path id="Path_12030" data-name="Path 12030" d="M140.43,208.306c-20.265-20.265-59.9-13.492-88.522,15.133a101.54,101.54,0,0,0-13.355,16.432q2.474,1.085,4.949,2.184a91.3,91.3,0,0,1,12.1-14.921c25.74-25.74,61.38-31.832,79.6-13.606q.709.705,1.366,1.449c14.527,16.354,11.322,44.954-6.679,68.663q2.37,1.235,4.737,2.481c20.524-27.134,23.637-59.975,5.8-77.815Z" transform="translate(-25.095 -196.624)" fill-rule="evenodd" fill="url(#linear-gradient-68)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12031" data-name="Path 12031" d="M138.921,208.253c18.641,17.689,13.208,53.68-12.137,80.388s-61,34.016-79.64,16.326-13.205-53.68,12.137-80.386,61-34.019,79.64-16.329Z" transform="translate(-29.526 -193.103)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12032" data-name="Path 12032" d="M139.73,209.43c18.226,18.224,12.134,53.864-13.606,79.6s-61.38,31.832-79.607,13.609-12.131-53.867,13.609-79.607,61.38-31.832,79.6-13.606Z" transform="translate(-29.617 -192.527)" fill-rule="evenodd" fill="url(#linear-gradient-69)"/>
      <path id="Path_12033" data-name="Path 12033" d="M47.967,304.008c-16.529-18.613-10.1-53.082,14.974-78.157s59.542-31.5,78.155-14.974c-.437-.492-.893-.976-1.366-1.446-18.223-18.226-53.864-12.134-79.6,13.606s-31.832,61.38-13.609,79.607c.473.47.954.926,1.449,1.366Z" transform="translate(-29.617 -192.527)" fill-rule="evenodd" fill="url(#linear-gradient-70)"/>
      <path id="Path_12034" data-name="Path 12034" d="M89.506,202.161c-14.137,3.5-27.189,11.739-37.423,21.976a91.35,91.35,0,0,0-9.43,11.055q19.466,8.7,38.812,18,20.851,10.023,41.554,20.78A76.956,76.956,0,0,0,135.3,247.961c2.81-11.336,2.609-24.01-3.63-34.227a32.569,32.569,0,0,0-3.372-4.568q-.689-.609-1.41-1.177l-.128-.1a32.426,32.426,0,0,0-3.032-2.095c-10.215-6.24-22.889-6.44-34.224-3.633Z" transform="translate(-17.789 -189.846)" fill-rule="evenodd" fill="url(#linear-gradient-71)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12035" data-name="Path 12035" d="M79.268,253.527a102.249,102.249,0,0,1-14.883,12.329l7.469,7.469A103.406,103.406,0,0,0,86.72,260.979a97.548,97.548,0,0,0,21.8-32.527l-7.525-7.525a96.458,96.458,0,0,1-21.726,32.6Z" transform="translate(20.934 -153.323)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-72)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12036" data-name="Path 12036" d="M81.081,248.237c-16.694,3.424-32.455.317-43.057-10.042l7.639,7.642c10.46,10.46,26.238,13.533,43,9.984l-7.586-7.583Z" transform="translate(-26.036 -122.555)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-73)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12037" data-name="Path 12037" d="M94.458,241.391c.581-12.768-3.071-24.355-11.336-32.622l-7.948-7.945c8.379,8.379,12.134,20.068,11.636,32.92l7.647,7.647Z" transform="translate(40.16 -189.142)" fill-rule="evenodd" opacity="0.4" fill="url(#linear-gradient-74)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12038" data-name="Path 12038" d="M88.231,223.525a95.293,95.293,0,0,1-9.16,14.666A102.987,102.987,0,0,1,69.735,248.9l7.452,7.452a100.217,100.217,0,0,0,18.543-25.328l-7.5-7.5Z" transform="translate(30.467 -148.694)" fill-rule="evenodd" fill="url(#linear-gradient-75)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12039" data-name="Path 12039" d="M129.577,276.533a23.707,23.707,0,0,1,2.974,3.577.385.385,0,0,1-.512.515,25.258,25.258,0,0,1-6.554-6.554.385.385,0,0,1,.512-.515,23.888,23.888,0,0,1,3.58,2.977Zm6.187-8.448a24.428,24.428,0,0,1,2.965,3.547.337.337,0,0,1-.064.426.341.341,0,0,1-.426.067,26.14,26.14,0,0,1-6.515-6.515.364.364,0,0,1,.49-.492,24.647,24.647,0,0,1,3.55,2.968ZM141,259.016a25.639,25.639,0,0,1,2.96,3.516.345.345,0,0,1-.47.47,25.652,25.652,0,0,1-3.513-2.963,25.292,25.292,0,0,1-2.963-3.513.326.326,0,0,1,.058-.414.322.322,0,0,1,.412-.056,25.639,25.639,0,0,1,3.516,2.96Zm4.142-9.617a26.6,26.6,0,0,1,2.954,3.483.315.315,0,0,1-.047.4.308.308,0,0,1-.4.047,27.9,27.9,0,0,1-6.437-6.437.311.311,0,0,1,.045-.4.316.316,0,0,1,.4-.047,26.389,26.389,0,0,1,3.483,2.954Zm2.835-10.078a27.164,27.164,0,0,1,2.946,3.449.306.306,0,0,1-.426.426,28.989,28.989,0,0,1-6.4-6.4.3.3,0,0,1,.423-.426,27.405,27.405,0,0,1,3.452,2.949Zm1.294-10.39a28.582,28.582,0,0,1,2.943,3.419.288.288,0,0,1-.4.4,28.575,28.575,0,0,1-3.419-2.943,28.271,28.271,0,0,1-2.94-3.416.288.288,0,0,1,.4-.4,28.279,28.279,0,0,1,3.416,2.94Zm-.593-10.451a29.839,29.839,0,0,1,2.938,3.385.272.272,0,0,1-.384.381,31.471,31.471,0,0,1-6.317-6.317.282.282,0,0,1,.014-.367.285.285,0,0,1,.367-.017,29.831,29.831,0,0,1,3.383,2.935ZM113.62,292.486a23.853,23.853,0,0,0,3.58,2.974.353.353,0,0,0,.44-.075.348.348,0,0,0,.072-.437,25.3,25.3,0,0,0-6.551-6.554.385.385,0,0,0-.515.512,24.022,24.022,0,0,0,2.974,3.58Zm-8.446,6.187a24.633,24.633,0,0,0,3.547,2.968.341.341,0,0,0,.426-.067.337.337,0,0,0,.064-.426,24.419,24.419,0,0,0-2.965-3.547,24.8,24.8,0,0,0-3.55-2.968.364.364,0,0,0-.49.492,24.633,24.633,0,0,0,2.968,3.547Zm-9.069,5.235a25.439,25.439,0,0,0,3.516,2.96.343.343,0,0,0,.467-.467,26.848,26.848,0,0,0-6.476-6.476.343.343,0,0,0-.467.467,25.442,25.442,0,0,0,2.96,3.516Zm-9.617,4.142A26.386,26.386,0,0,0,89.972,311a.311.311,0,0,0,.4-.044.319.319,0,0,0,.047-.4,27.9,27.9,0,0,0-6.437-6.437.319.319,0,0,0-.4.047.311.311,0,0,0-.044.4,26.387,26.387,0,0,0,2.954,3.483Zm-10.078,2.835a27.158,27.158,0,0,0,3.449,2.946.3.3,0,0,0,.389-.033.308.308,0,0,0,.036-.392,27.632,27.632,0,0,0-2.949-3.449,27.165,27.165,0,0,0-3.449-2.946.3.3,0,0,0-.389.036.3.3,0,0,0-.036.389,27.4,27.4,0,0,0,2.949,3.449Zm-10.39,1.3a28.78,28.78,0,0,0,3.416,2.94.288.288,0,0,0,.4-.4A28.29,28.29,0,0,0,66.9,311.3a28.779,28.779,0,0,0-3.416-2.94.288.288,0,0,0-.4.4,28.282,28.282,0,0,0,2.94,3.419Zm-10.451-.592a29.844,29.844,0,0,0,3.385,2.935.271.271,0,0,0,.381-.381,31.5,31.5,0,0,0-6.32-6.32.275.275,0,0,0-.364.017.278.278,0,0,0-.017.364,30.088,30.088,0,0,0,2.935,3.385ZM45.5,308.749a30.716,30.716,0,0,0,3.355,2.929.254.254,0,0,0,.359-.359,33.141,33.141,0,0,0-6.281-6.281.254.254,0,0,0-.359.359,30.961,30.961,0,0,0,2.926,3.352ZM122.558,284.3a23.174,23.174,0,0,1,2.982,3.614.369.369,0,0,1-.086.451.361.361,0,0,1-.448.083,24.453,24.453,0,0,1-6.593-6.593.364.364,0,0,1,.083-.451.368.368,0,0,1,.451-.083,23.164,23.164,0,0,1,3.611,2.979Zm23.281-75.893a31.01,31.01,0,0,1,2.929,3.352.254.254,0,0,1-.359.359,32.925,32.925,0,0,1-6.281-6.281.254.254,0,0,1,.359-.359,30.965,30.965,0,0,1,3.352,2.929Z" transform="translate(-18.047 -180.958)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
    </g>
  </g>
</svg>
