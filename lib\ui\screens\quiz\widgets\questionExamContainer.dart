import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/result_detail.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class QuestionExamContainer extends StatelessWidget {
  final Question? question;
  final Color? questionColor;
  final int? questionNumber;
  final bool isMathQuestion;

  const QuestionExamContainer({
    Key? key,
    this.question,
    required this.isMathQuestion,
    this.questionColor,
    this.questionNumber,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: MediaQuery.of(context).size.width -
                  MediaQuery.of(context).size.width * (0.16),
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width * (0.1)),
              child: isMathQuestion
                  ? TeXView(
                      child: TeXViewDocument(
                        question!.question!,
                      ),
                      style: TeXViewStyle(
                          contentColor:
                              questionColor ?? Theme.of(context).primaryColor,
                          backgroundColor: Colors.transparent,
                          sizeUnit: TeXViewSizeUnit.pixels,
                          textAlign: TeXViewTextAlign.center,
                          fontStyle: TeXViewFontStyle(fontSize: 23)),
                    )
                  : Text(
                      questionNumber == null
                          ? UiUtils.parseHtmlString(question!.question ?? "")
                          : UiUtils.parseHtmlString(
                              "$questionNumber. " + "${question!.question}"),
                      style: TextStyle(
                          fontSize: 18.0,
                          fontWeight: FontWeight.w400,
                          color:
                              questionColor ?? Theme.of(context).primaryColor),
                    ),
            ),
          ],
        ),
        SizedBox(
          height: 15.0,
        ),
      ],
    );
  }
}
