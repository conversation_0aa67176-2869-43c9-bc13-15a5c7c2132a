import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/auth/cubits/signUpCubit.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/auth/signInScreen.dart';
import 'package:flutterquiz/ui/screens/auth/widgets/termsAndCondition.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:flutterquiz/utils/validators.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:http/http.dart' as http;

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({Key? key}) : super(key: key);
  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  DateTime now = DateTime.now();
  bool _obscureText = true, _obscureTextCn = true, isLoading = false;
  TextEditingController edtEmail = TextEditingController();
  TextEditingController edtPwd = TextEditingController();
  TextEditingController edtCPwd = TextEditingController();
  TextEditingController dateInput = TextEditingController();
  String group = '';
  bool enableField = true;
  List<String> certificationList = [];

  Future<void> deleteBookmarkApi() async {
    //Common.appid = packageInfo.packageName;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      try {
        final response = await http.post(
            Uri.parse(apiUrlBase + "/login/getAccountGroupCreateApi"),
            body: null);
        if (response.statusCode == 200) {
          //listGroupCreate = jsonDecode(response.body);
          Map<String, dynamic> jsonMap = jsonDecode(response.body);
          certificationList = jsonMap.keys.toList();
          certificateFullName = jsonMap;
          certificates = certificationList;
          setState(() {});
        }
      } catch (e) {
        print(e);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    deleteBookmarkApi();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SignUpCubit>(
      create: (_) => SignUpCubit(AuthRepository()),
      child: Builder(
          builder: (context) => Scaffold(
                body: Stack(
                  children: [
                    PageBackgroundGradientContainer(),
                    SingleChildScrollView(
                      controller: _scrollController,
                      child: form(),
                    ),
                  ],
                ),
              )),
    );
  }

  Widget form() {
    return Form(
      key: _formKey,
      child: Padding(
        padding: EdgeInsetsDirectional.only(
            start: MediaQuery.of(context).size.width * .08,
            end: MediaQuery.of(context).size.width * .08),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height * .05,
            ),
            signUpText(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .03,
            ),
            showTopImage(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .03,
            ),
            showEmail(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .02,
            ),
            showPassword(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .02,
            ),
            showCnfPassword(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .02,
            ),
            showSelectGroup(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .02,
            ),
            showDatePick(),
            SizedBox(
              height: MediaQuery.of(context).size.height * .02,
            ),
            showSignup(),
            showGoSignIn(),
            TermsAndCondition(),
            SizedBox(
              height: 20,
            )
          ],
        ),
      ),
    );
  }

  Widget signUpText() {
    return Text(
      AppLocalization.of(context)!.getTranslatedValues("signUpLbl")!,
      style: TextStyle(
          color: Theme.of(context).primaryColor,
          fontSize: 22,
          fontWeight: FontWeight.bold),
    );
  }

  Widget showTopImage() {
    return Container(
      transformAlignment: Alignment.topCenter,
      child: Lottie.asset("assets/animations/login.json",
          height: MediaQuery.of(context).size.height * .25,
          width: MediaQuery.of(context).size.width * 3),
    );
  }

  Widget showEmail() {
    return TextFormField(
      onTap: () async {
        Future.delayed(Duration(milliseconds: 500), () {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 500),
            curve: Curves.ease,
          );
        });
      },
      controller: edtEmail,
      keyboardType: TextInputType.emailAddress,
      validator: (val) => Validators.validateEmail(val!, context),
      style: TextStyle(color: Theme.of(context).colorScheme.secondary),
      decoration: InputDecoration(
        fillColor: Theme.of(context).colorScheme.surface,
        filled: true,
        border: InputBorder.none,
        hintText:
            AppLocalization.of(context)!.getTranslatedValues('emailLbl')! + "*",
        contentPadding: EdgeInsets.all(15),
        errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Colors.white, width: 0.0),
            borderRadius: BorderRadius.circular(10.0)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: new BorderSide(
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
        enabledBorder: UnderlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: new BorderSide(
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
      ),
    );
  }

  Widget showPassword() {
    return TextFormField(
      onTap: () async {
        Future.delayed(Duration(milliseconds: 500), () {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 500),
            curve: Curves.ease,
          );
        });
      },
      enabled: enableField,
      controller: edtPwd,
      style: TextStyle(color: Theme.of(context).colorScheme.secondary),
      obscureText: _obscureText,
      obscuringCharacter: "*",
      validator: (val) => val!.isEmpty
          ? '${AppLocalization.of(context)!.getTranslatedValues('pwdLengthMsg')}'
          : null,
      decoration: InputDecoration(
        fillColor: Theme.of(context).colorScheme.surface,
        filled: true,
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(15),
        hintText:
            AppLocalization.of(context)!.getTranslatedValues('pwdLbl')! + "*",
        errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Colors.white, width: 0.0),
            borderRadius: BorderRadius.circular(10.0)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: new BorderSide(
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
        enabledBorder: UnderlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: new BorderSide(
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
        suffixIcon: GestureDetector(
          child: Icon(
            _obscureText ? Icons.visibility : Icons.visibility_off,
            color: Theme.of(context).colorScheme.secondary,
          ),
          onTap: () {
            setState(() {
              _obscureText = !_obscureText;
            });
          },
        ),
      ),
    );
  }

  Widget showSelectGroup() {
    return FormField<String>(
        // enabled: enableField,
        builder: (FormFieldState<String> state) {
      return DropdownButtonFormField<String>(
        isExpanded: true,
        menuMaxHeight: MediaQuery.sizeOf(context).height * 0.4,
        decoration: InputDecoration(
            contentPadding: EdgeInsets.all(15),
            fillColor: Theme.of(context).colorScheme.surface,
            filled: true,
            labelStyle: TextStyle(
              color: Theme.of(context).colorScheme.secondary,
              overflow: TextOverflow.ellipsis,
            ),
            // errorStyle: TextStyle(color: Colors.redAccent, fontSize: 16.0),
            hintText: AppLocalization.of(context)!
                    .getTranslatedValues('selectCertificate')! +
                "*",
            focusedErrorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.white, width: 0.0),
                borderRadius: BorderRadius.circular(10.0)),
            focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(width: 0.0),
                borderRadius: BorderRadius.circular(10.0)),
            errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.white, width: 0.0),
                borderRadius: BorderRadius.circular(10.0)),
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.transparent, width: 0.0),
                borderRadius: BorderRadius.circular(10.0))),
        validator: (value) => value == null
            ? AppLocalization.of(context)!
                .getTranslatedValues('selectCertificateMsg')!
            : null,
        value: group.isNotEmpty ? group : null,
        items: certificates.map((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(
              certificateFullName[value],
              style: TextStyle(color: Theme.of(context).colorScheme.secondary),
            ),
          );
        }).toList(),
        selectedItemBuilder: (context) => certificateFullName.values
            .map((value) => Text(
                  value,
                  maxLines: 1,
                  overflow: TextOverflow.fade,
                  softWrap: false,
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.secondary),
                ))
            .toList(),
        isDense: true,
        onChanged: (newValue) {
          setState(() {
            group = newValue ?? '';
          });
        },
      );
    });
  }

  Widget showDatePick() {
    return TextField(
      style: TextStyle(color: Theme.of(context).colorScheme.secondary),
      enabled: enableField,
      controller: dateInput,
      decoration: InputDecoration(
          fillColor: Theme.of(context).colorScheme.surface,
          filled: true,
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(15),
          hintText: AppLocalization.of(context)!
              .getTranslatedValues('selectExamDate')!,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: new BorderSide(
              color: Theme.of(context).colorScheme.surface,
            ),
          ),
          enabledBorder: UnderlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
            borderSide: new BorderSide(
              color: Theme.of(context).colorScheme.surface,
            ),
          )),
      readOnly: true,
      onTap: () async {
        DateTime? pickedDate = await showDatePicker(
          context: context,
          initialDate: now,
          firstDate: DateTime.now(),
          //DateTime.now() - not to allow to choose before today.
          lastDate: DateTime(2100),
          builder: (BuildContext context, Widget? child) {
            return Theme(
              // Thiết lập nền trắng cho DatePicker
              data: ThemeData.light().copyWith(
                colorScheme: ColorScheme.light(
                  surface: Colors.white, // Nền trắng
                ),
              ),
              child: child!,
            );
          },
        );

        if (pickedDate != null) {
          print(
              pickedDate); //pickedDate output format => 2021-03-10 00:00:00.000
          String formattedDate = DateFormat('dd-MM-yyyy').format(pickedDate);
          print(
              formattedDate); //formatted date output using intl package =>  2021-03-16
          setState(() {
            dateInput.text = formattedDate;
            now = pickedDate; //set output date to TextField value.
          });
        } else {}
      },
    );
  }

  Widget showCnfPassword() {
    return TextFormField(
      onTap: () async {
        Future.delayed(Duration(milliseconds: 500), () {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 500),
            curve: Curves.ease,
          );
        });
      },
      enabled: enableField,
      controller: edtCPwd,
      style: TextStyle(color: Theme.of(context).colorScheme.secondary),
      obscureText: _obscureTextCn,
      obscuringCharacter: "*",
      validator: (val) => val != edtPwd.text
          ? '${AppLocalization.of(context)!.getTranslatedValues('cnPwdNotMatchMsg')}'
          : null,
      decoration: InputDecoration(
        fillColor: Theme.of(context).colorScheme.surface,
        filled: true,
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(15),
        hintText:
            AppLocalization.of(context)!.getTranslatedValues('cnPwdLbl')! + "*",
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: new BorderSide(
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
        enabledBorder: UnderlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: new BorderSide(
            color: Theme.of(context).colorScheme.surface,
          ),
        ),
        suffixIcon: GestureDetector(
          child: Icon(
            _obscureTextCn ? Icons.visibility : Icons.visibility_off,
            color: Theme.of(context).colorScheme.secondary,
          ),
          onTap: () {
            setState(() {
              _obscureTextCn = !_obscureTextCn;
            });
          },
        ),
      ),
    );
  }

  Widget showGoSignIn() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Text(
          AppLocalization.of(context)!
              .getTranslatedValues('alreadyAccountLbl')!,
          style: TextStyle(
            color: Theme.of(context).colorScheme.secondary.withOpacity(0.4),
          ),
        ),
        SizedBox(width: 2),
        CupertinoButton(
          onPressed: () {
            Navigator.of(context).pushNamed(Routes.login);
          },
          padding: EdgeInsets.all(0),
          child: Text(
            AppLocalization.of(context)!.getTranslatedValues('loginLbl')!,
            style: TextStyle(color: Theme.of(context).primaryColor),
          ),
        ),
      ],
    );
  }

  String getDeviceType() {
    final data = MediaQueryData.fromWindow(WidgetsBinding.instance.window);
    return data.size.shortestSide < 600 ? 'phone' : 'tablet';
  }

  Widget showSignup() {
    return Container(
      child: Column(
        children: <Widget>[
          SizedBox(
            width: getDeviceType() == "tablet"
                ? 300
                : MediaQuery.of(context).size.width,
            child: BlocConsumer<SignUpCubit, SignUpState>(
              listener: (context, state) async {
                if (state is SignUpSuccess) {
                  //on signup success navigate user to sign in screen
                  UiUtils.setSnackbar(
                      "${AppLocalization.of(context)!.getTranslatedValues('emailVerify')} ${edtEmail.text.trim()}",
                      context,
                      false);
                  Navigator.of(context).push(CupertinoPageRoute(
                      builder: (context) => SignInScreen(
                            username: edtEmail.text,
                          )));
                } else if (state is SignUpFailure) {
                  //show error message
                  Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (BuildContext context) => super.widget));
                  UiUtils.setSnackbar(
                      AppLocalization.of(context)!.getTranslatedValues(
                          convertErrorCodeToLanguageKey(state.errorMessage))!,
                      context,
                      false);
                }
              },
              builder: (context, state) {
                return CupertinoButton(
                  child: state is SignUpProgress
                      ? Center(
                          child: CircularProgressContainer(
                          heightAndWidth: 40,
                          useWhiteLoader: true,
                        ))
                      : Text(
                          AppLocalization.of(context)!
                              .getTranslatedValues('signUpLbl')!,
                          style: TextStyle(
                              color: Theme.of(context).colorScheme.surface),
                        ),
                  color: Theme.of(context).primaryColor,
                  onPressed: () async {
                    setState(() {
                      _obscureText = true;
                      _obscureTextCn = true;
                    });
                    if (_formKey.currentState!.validate()) {
                      //calling signup user
                      context.read<SignUpCubit>().signUpUser(
                          AuthProvider.email,
                          edtEmail.text.trim(),
                          edtPwd.text.trim(),
                          group,
                          dateInput.text);
                      resetForm();
                    }
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  resetForm() {
    setState(() {
      enableField = false;
      isLoading = false;
      edtEmail.text = "";
      edtPwd.text = "";
      edtCPwd.text = "";
      _formKey.currentState!.reset();
    });
  }
}
