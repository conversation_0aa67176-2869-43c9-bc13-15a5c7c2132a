import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:firebase_performance_dio/firebase_performance_dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../ui/screens/exam/api_servies.dart';

class NewDio {
  Dio getDio() {
    final dio = Dio();
    final performanceInterceptor = DioFirebasePerformanceInterceptor();
    dio.interceptors.add(performanceInterceptor);
    dio.interceptors.add(PrettyDioLogger());
    dio.interceptors.add(CustomInterceptors());
    return dio;
  }
}

class CustomInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    log('REQUEST[${options.method}] => PATH: ${options.path}');
    return super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    return super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    final data = {
      'type': 'API Error',
      'code': err.response?.statusCode,
      'path': err.requestOptions.path,
      'response': err.response?.data
    };
    ApiServices().sendErrorReport(jsonEncode(data));
    return super.onError(err, handler);
  }
}
