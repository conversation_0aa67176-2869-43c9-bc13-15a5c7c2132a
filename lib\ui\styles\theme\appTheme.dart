import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/styles/colors.dart';

enum AppTheme { Light, Dark }

enum ExamTheme { Scrum, Elite }

final appThemeData = {
  AppTheme.Light: ThemeData(
      shadowColor: primaryColor.withOpacity(0.25),
      brightness: Brightness.light,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: pageBackgroundColor,
      canvasColor: canvasColor,
      colorScheme: ThemeData()
          .colorScheme
          .copyWith(
            secondary: secondaryColor,
          )
          .copyWith(surface: backgroundColor)),
  AppTheme.Dark: ThemeData(
      shadowColor: darkPrimaryColor.withOpacity(0.25),
      brightness: Brightness.dark,
      primaryColor: darkPrimaryColor,
      scaffoldBackgroundColor: darkPageBackgroundColor,
      canvasColor: darkCanvasColor,
      colorScheme: ThemeData()
          .colorScheme
          .copyWith(
            brightness: Brightness.dark,
            secondary: darkSecondaryColor,
          )
          .copyWith(surface: darkBackgroundColor)),
};
