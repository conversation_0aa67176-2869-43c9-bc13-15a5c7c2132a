// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCYAFJl-5jPgorREKhkt6RmjpQrx_X55-U',
    appId: '1:196932976779:android:540fed68686b17cf70548e',
    messagingSenderId: '196932976779',
    projectId: 'scrumpass-exam-tool',
    storageBucket: 'scrumpass-exam-tool.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBAdKMrWqNsDTr-kl26qdrBar22BGHtNCg',
    appId: '1:196932976779:ios:857c9f3c907252e670548e',
    messagingSenderId: '196932976779',
    projectId: 'scrumpass-exam-tool',
    storageBucket: 'scrumpass-exam-tool.firebasestorage.app',
    androidClientId: '196932976779-tifcq8alng5ia8gd5ijorjgp64rfenqt.apps.googleusercontent.com',
    iosClientId: '196932976779-43c04ejc9tg1ha6djj5l4bgikkc0brn0.apps.googleusercontent.com',
    iosBundleId: 'com.scrumpass.exam',
  );
}
