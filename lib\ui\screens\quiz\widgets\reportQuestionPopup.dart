import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/ui/styles/app_colors.dart';
import 'package:flutterquiz/ui/styles/app_styles.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:html/parser.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class ReportQuestionPopup extends StatefulWidget {
  const ReportQuestionPopup({
    Key? key,
    required this.question,
    required this.questionId,
    required this.quizId,
  }) : super(key: key);
  final String question;
  final String questionId;
  final String quizId;
  @override
  _ReportQuestionPopupState createState() => _ReportQuestionPopupState();
}

class _ReportQuestionPopupState extends State<ReportQuestionPopup> {
  int dropdownvalue = 1;
  late TextEditingController controltext;
  List<int> arr = [];
  @override
  void initState() {
    super.initState();
    controltext = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    controltext.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                AppLocalization.of(context)!
                    .getTranslatedValues("reportQuestion")!,
                style: AppStyles.reportDialogTitle.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
              SizedBox(height: 10),
              Text(
                AppLocalization.of(context)!
                        .getTranslatedValues("questionLbl")! +
                    ":",
                style: AppStyles.reportDialogTitle.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
              const SizedBox(
                height: 4,
              ),
              Container(
                // constraints: BoxConstraints(maxHeight: 90),
                child: Column(
                  children: <Widget>[
                    Container(
                      width: double.infinity,
                      //height: 200.0,
                      color: /* Colors.white.withOpacity(0.7) */
                          Colors.transparent,
                      child: Text(
                        parse(widget.question).documentElement!.text,
                        style: AppStyles.reportDialogText.copyWith(
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                      ),
                    )
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Text(
                AppLocalization.of(context)!.getTranslatedValues("reportLbl")! +
                    ":",
                style: AppStyles.reportDialogTitle.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
              const SizedBox(
                height: 4,
              ),
              TextField(
                style:
                    TextStyle(color: Theme.of(context).colorScheme.secondary),
                onTap: () {
                  setState(() {
                    isEmpty = false;
                  });
                },
                cursorColor: Theme.of(context).primaryColor,
                decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isEmpty ? Colors.red : Color(0xFFACB7C5),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: isEmpty ? Colors.red : Color(0xFFACB7C5),
                        width: 1.0),
                  ),
                ),
                autofocus: false,
                maxLines: 3,
                // expands: true,
                keyboardType: TextInputType.text,
                controller: controltext,
                textAlignVertical: TextAlignVertical.top,
              ),
              isEmpty == true
                  ? Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Text(
                        AppLocalization.of(context)!
                            .getTranslatedValues("emptyField")!,
                        style: AppStyles.reportDialogText
                            .copyWith(color: Colors.red),
                      ),
                    )
                  : Container(),
              const SizedBox(
                height: 24,
              ),
              Container(
                child: Row(
                  children: [
                    Expanded(
                        child: Padding(
                      padding: const EdgeInsets.only(right: 6),
                      child: ElevatedButton(
                        onPressed: close,
                        child: Text(
                          AppLocalization.of(context)!
                              .getTranslatedValues("cancel")!,
                          style: AppStyles.secondaryButton
                              .copyWith(color: Theme.of(context).primaryColor),
                          textAlign: TextAlign.center,
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Theme.of(context).primaryColor,
                          backgroundColor: Colors.white,
                          shadowColor: Colors.white,
                          elevation: 0,
                          minimumSize: Size(20, 44),
                          side: BorderSide(
                              color: Theme.of(context).primaryColor,
                              width: 1.0,
                              style: BorderStyle.solid),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                      ),
                    )),
                    Expanded(
                        child: Padding(
                      padding: const EdgeInsets.only(left: 6),
                      child: ElevatedButton(
                        onPressed: submit,
                        child: Text(
                          AppLocalization.of(context)!
                              .getTranslatedValues("send")!,
                          style: AppStyles.primaryButton,
                        ),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: AppColors.white, backgroundColor: Theme.of(context).primaryColor,
                          shadowColor: Color.fromARGB(92, 0, 166, 144),
                          elevation: 0,
                          minimumSize: Size(20, 44),
                          side: BorderSide(
                              color: Theme.of(context).primaryColor,
                              width: 1.0,
                              style: BorderStyle.solid),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0)),
                        ),
                      ),
                    ))
                  ],
                ),
              )
            ],
          ),
        ),
      ),
      // actions: [
      //   TextButton(
      //       onPressed: submit,
      //       child: Text(AppLocalizations.of(context).send)),
      //   TextButton(
      //       onPressed: close,
      //       child: Text(AppLocalizations.of(context).cancel))
      // ]
    );
  }

  Future<void> sendReport() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    String url = apiUrl + "send_report";
    String parsedstring = '';
    var doc = parse(widget.question);
    if (doc.documentElement != null) {
      parsedstring = doc.documentElement!.text;
    }
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {
        'appid': Common.appid,
        'qid': widget.questionId,
        'quid': widget.quizId,
        'email': preferences.get('username'),
        'message': controltext.text,
        'questionReport': parsedstring,
      };
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data)
            }));
        if (response.statusCode == 200) {
          // inspect(response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        UiUtils.setSnackbar(
            AppLocalization.of(context)!
                .getTranslatedValues("defaultErrorMessage")!,
            context,
            false);
      }
    }
  }

  bool isEmpty = false;
  void submit() {
    //Navigator.of(context).pop(controltext);
    if (controltext.text.isEmpty) {
      //error();
      setState(() {
        isEmpty = true;
      });
    } else {
      Navigator.of(context).pop(controltext);
      sendReport();
      success();
    }
    //Navigator.of(context).pop();
    //inspect(widget.quizId);
  }

  void close() {
    Navigator.of(context).pop();
  }

  void success() => UiUtils.setSnackbar(
      AppLocalization.of(context)!.getTranslatedValues("reportSuccess")!,
      context,
      false);
  void error() => UiUtils.setSnackbar(
      AppLocalization.of(context)!.getTranslatedValues("defaultErrorMessage")!,
      context,
      false);
}
