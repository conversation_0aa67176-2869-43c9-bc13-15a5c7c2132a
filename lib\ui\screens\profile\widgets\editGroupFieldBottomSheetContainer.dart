import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/utils/apiUtils.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class EditGroupFieldBottomSheetContainer extends StatefulWidget {
  final String fieldValue; //
  final UpdateUserDetailCubit updateUserDetailCubit;
  //To determine if to close bottom sheet without updating name or not
  final bool canCloseBottomSheet;
  EditGroupFieldBottomSheetContainer(
      {Key? key,
      required this.fieldValue,
      required this.canCloseBottomSheet,
      required this.updateUserDetailCubit})
      : super(key: key);

  @override
  _EditGroupFieldBottomSheetContainerState createState() =>
      _EditGroupFieldBottomSheetContainerState();
}

class _EditGroupFieldBottomSheetContainerState
    extends State<EditGroupFieldBottomSheetContainer> {
  late TextEditingController textEditingController =
      TextEditingController(text: widget.fieldValue);

  late String errorMessage = "";
  int selectedIndex = 0;

  void initState() {
    super.initState();
    setValue();
  }

  void setValue() {
    final String input = widget.fieldValue;
    final value = groupCertificate[input];
    selectedIndex = certificates.indexWhere((element) => element == value);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UpdateUserDetailCubit, UpdateUserDetailState>(
      bloc: widget.updateUserDetailCubit,
      listener: (context, state) {
        if (state is UpdateUserDetailSuccess) {
          // context.read<UserDetailsCubit>().updateUserProfile(
          //       email: widget.fieldTitle == emailLbl
          //           ? textEditingController.text.trim()
          //           : null,
          //       mobile: widget.fieldTitle == mobileNumberLbl
          //           ? textEditingController.text.trim()
          //           : null,
          //       name: widget.fieldTitle == nameLbl
          //           ? textEditingController.text.trim()
          //           : null,
          //     );
          Navigator.of(context).pop();
        } else if (state is UpdateUserDetailFailure) {
          if (state.errorMessage == unauthorizedAccessCode) {
            UiUtils.showAlreadyLoggedInDialog(context: context);
            return;
          }
          setState(() {
            errorMessage = AppLocalization.of(context)!.getTranslatedValues(
                convertErrorCodeToLanguageKey(state.errorMessage))!;
          });
        }
      },
      child: WillPopScope(
        onWillPop: () {
          if (widget.canCloseBottomSheet) {
            if (widget.updateUserDetailCubit.state
                is UpdateUserDetailInProgress) {
              return Future.value(false);
            }
            return Future.value(true);
          } else {
            return Future.value(false);
          }
        },
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              gradient: UiUtils.buildLinerGradient([
                Theme.of(context).scaffoldBackgroundColor,
                Theme.of(context).canvasColor
              ], Alignment.topCenter, Alignment.bottomCenter)),
          child: Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      margin: EdgeInsets.all(10.0),
                      alignment: Alignment.centerRight,
                      child: InkWell(
                          onTap: () {
                            //
                            if (!widget.canCloseBottomSheet) {
                              return;
                            }
                            if (widget.updateUserDetailCubit.state
                                is! UpdateUserDetailInProgress) {
                              widget.updateUserDetailCubit.updateGroup(
                                  userId: context
                                      .read<UserDetailsCubit>()
                                      .getUserId(),
                                  group: certificates[selectedIndex]);
                              context.read<UserDetailsCubit>().fetchUserDetails(
                                  context
                                      .read<AuthCubit>()
                                      .getUserFirebaseId());
                              ApiServices().getListQuiz();
                              ApiServices().getListQuizExam();
                              ApiServices().getListResult();
                              ApiServices().getListResultExam();
                              ApiUtils().getListWrongAns(context);
                              ApiUtils().getListMarkedAns(context);
                              Navigator.of(context).pop();
                            }
                          },
                          child: Text(
                            AppLocalization.of(context)!
                                .getTranslatedValues("save")!,
                            style: TextStyle(
                                fontSize: 18,
                                color: Theme.of(context).primaryColor),
                          )),
                    ),
                  ],
                ),
                SingleChildScrollView(
                  child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: certificates.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          trailing: index == selectedIndex
                              ? Icon(
                                  Icons.check,
                                  color: Colors.green,
                                )
                              : null,
                          title: Text(certificates[index]),
                          onTap: () {
                            setState(() {
                              selectedIndex = index;
                            });
                          },
                        );
                      }),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
