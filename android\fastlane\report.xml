<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000394">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: Switch to android build_apk lane" time="0.000384">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: Switch to android sh_on_root lane" time="0.000197">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: cd /Users/<USER>/Work/Project/scrumpass-exam-tool &amp;&amp; flutter build apk" time="195.829939">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: firebase_app_distribution" time="15.590531">
        
      </testcase>
    
  </testsuite>
</testsuites>
