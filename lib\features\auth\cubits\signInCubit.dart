import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/ui/screens/model/user_login_model.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'authCubit.dart';

//State
@immutable
abstract class SignInState {}

class SignInInitial extends SignInState {}

class SignInProgress extends SignInState {
  final AuthProvider authProvider;
  SignInProgress(this.authProvider);
}

class SignInSuccess extends SignInState {
  final dynamic user;
  final AuthProvider authProvider;
  final bool isNewUser;
  final bool isLocal;

  SignInSuccess(
      {required this.authProvider,
      required this.user,
      required this.isNewUser,
      this.isLocal = false});
}

class SignInFailure extends SignInState {
  final String errorMessage;
  final AuthProvider authProvider;
  SignInFailure(this.errorMessage, this.authProvider);
}

class SendMailFailure extends SignInState {}

class SignInCubit extends Cubit<SignInState> {
  final AuthRepository _authRepository;
  SignInCubit(this._authRepository) : super(SignInInitial());

  Future<UserLoginModel> _handleLogin(
      {required String username, required String password}) async {
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      String url = apiUrlBase + "login/verify_app_login";
      Map data = {'username': username, 'password': password};
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "data": jsonEncode(data),
            }));
        if (response.statusCode == 200) {
          return UserLoginModel.fromJson(response.data);
        } else {
          return (UserLoginModel(status: "0"));
        }
      } catch (e) {
        return (UserLoginModel(status: "0"));
      }
    } else {
      return (UserLoginModel());
    }
  }

  //to signIn user
  void signInUser(
    AuthProvider authProvider, {
    String? email,
    String? verificationId,
    String? smsCode,
    String? password,
  }) async {
    emit(SignInProgress(authProvider));
    //signIn user with given provider and also add user detials in api
    if (email != null) {
      UserLoginModel localVerify =
          await _handleLogin(username: email, password: password ?? "");
      if (localVerify.status == "1" && localVerify.user?["firebase_id"] != null) {
        final name = localVerify.user?['name'] ?? "";
        SharedPreferences preferences = await SharedPreferences.getInstance();
        
        preferences.setString('username', email);
        emit(SignInSuccess(
          user: localVerify.user,
          authProvider: authProvider,
          isNewUser: name.isEmpty,
          isLocal: true,
        ));
        Common.username = localVerify.user!['email'];
        Common.totalQuiz = "No Data";
        Common.average = "No Data";
        Common.recent = "No Data";
      } else {
        if (localVerify.status != "") {
          emit(SignInFailure(localVerify.status, authProvider));
        } else {
          handleFirebaseLogin(
            authProvider,
            email: email,
            verificationId: verificationId,
            smsCode: smsCode,
            password: password,
          );
        }
      }
    } else {
      handleFirebaseLogin(
        authProvider,
        email: email,
        verificationId: verificationId,
        smsCode: smsCode,
        password: password,
      );
    }
  }

  void handleFirebaseLogin(
    AuthProvider authProvider, {
    String? email,
    String? verificationId,
    String? smsCode,
    String? password,
  }) async {
    User? userInfo;
    await _authRepository
        .signInUser(
      authProvider,
      email: email ?? "",
      password: password ?? "",
      smsCode: smsCode ?? "",
      verificationId: verificationId ?? "",
    )
        .then((result) {
      userInfo = result['user'];
      //success
      emit(SignInSuccess(
        user: result['user'],
        authProvider: authProvider,
        isNewUser: result['isNewUser'],
        isLocal: false,
      ));
      Common.username = result['user'].email;
      Common.totalQuiz = "No Data";
      Common.average = "No Data";
      Common.recent = "No Data";
    }).catchError((e) {
      //failure
      emit(SignInFailure(e.toString(), authProvider));
    });
  }

  void resendVerifyMail(BuildContext context) async {
    await _authRepository.sendVerifyMail().catchError((e) {
      //failure
      emit(SendMailFailure());
    });
    UiUtils.setSnackbar(
        AppLocalization.of(context)!.getTranslatedValues("resendMailSuccess")!,
        context,
        false);
  }
}
