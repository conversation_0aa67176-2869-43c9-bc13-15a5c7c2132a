import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/reportQuestion/model/report.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionCubit.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/ui/widgets/roundedAppbar.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({Key? key}) : super(key: key);

  @override
  State<ReportScreen> createState() => _ReportScreenState();

  static Route<ReportScreen> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
        builder: (_) => BlocProvider<ReportQuestionCubit>(
            child: ReportScreen(),
            create: (_) => ReportQuestionCubit(ReportQuestionRepository())));
  }
}

class _ReportScreenState extends State<ReportScreen> {
  List<Report> reports = [];
  void getListReport() {
    Future.delayed(Duration.zero, () {
      context
          .read<ReportQuestionCubit>()
          .getReportList(userId: context.read<UserDetailsCubit>().getUserId());
    });
  }

  @override
  void initState() {
    getListReport();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageBackgroundGradientContainer(),
          BlocConsumer<ReportQuestionCubit, ReportQuestionState>(
              listener: (context, state) {},
              builder: (context, state) {
                if (state is ReportQuestionSuccess) {
                  reports = state.reports ?? [];
                  return Align(
                    alignment: Alignment.topCenter,
                    child: _buildReportListContainer(state),
                  );
                }
                if (state is ReportQuestionFailure) {
                  return Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("noData"),
                        onTapRetry: () {
                          setState(() {
                            getListReport();
                          });
                        },
                        showErrorImage: true),
                  );
                }
                return Center(
                  child: CircularProgressContainer(
                    useWhiteLoader: false,
                  ),
                );
              }),
          Align(
            alignment: Alignment.topCenter,
            child: RoundedAppbar(
                title: AppLocalization.of(context)!
                    .getTranslatedValues('reportQuestion')!),
          ),
        ],
      ),
    );
  }

  Widget _buildReportListContainer(state) {
    return Padding(
        padding: EdgeInsets.only(
          right: MediaQuery.of(context).size.width * (0.05),
          left: MediaQuery.of(context).size.width * (0.05),
          top: MediaQuery.of(context).size.height *
              UiUtils.appBarHeightPercentage *
              0.8,
          bottom: MediaQuery.of(context).size.height * 0.075,
        ),
        child: ListView.builder(
          itemCount: reports.length,
          itemBuilder: (BuildContext context, int index) {
            return _buildReportItemContainer(reports[index]);
          },
        ));
  }

  Widget _buildReportItemContainer(Report report) {
    final updatedDate = report.updatedDate ?? "";
    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .pushNamed(Routes.reportDetail, arguments: {'id': report.reportId});
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: 8.0),
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(10.0)),
        height: MediaQuery.of(context).size.height * (0.125),
        margin: EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    (report.reportQuestion ?? "")
                        .toString()
                        .trim()
                        .replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ''),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                      fontSize: 17.25,
                    ),
                  ),
                ),
                SizedBox(
                  width: 20,
                ),
                Text(
                  report.status == "0"
                      ? "${AppLocalization.of(context)!.getTranslatedValues('processing')!}"
                      : report.status == "1"
                          ? "${AppLocalization.of(context)!.getTranslatedValues('answered')!}"
                          : "${AppLocalization.of(context)!.getTranslatedValues('closed')!}",
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                    fontSize: 17.25,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  width: MediaQuery.of(context).size.width * (0.5),
                  child: Text(
                    "${AppLocalization.of(context)!.getTranslatedValues('updatedDate')!}:",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .surface
                          .withOpacity(0.8),
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  updatedDate.substring(0, updatedDate.length - 3),
                  style: TextStyle(
                    color:
                        Theme.of(context).colorScheme.surface.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
