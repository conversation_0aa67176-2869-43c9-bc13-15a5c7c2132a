<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="195.418" height="154.293" viewBox="0 0 195.418 154.293">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b38746"/>
      <stop offset="0.389" stop-color="#d9973b"/>
      <stop offset="0.75" stop-color="#ad6c10"/>
      <stop offset="0.858" stop-color="#d3953d"/>
      <stop offset="1" stop-color="#b07f35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffee76"/>
      <stop offset="1" stop-color="#1d1d1b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#e6a729"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f0cc62"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#e0c0a8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e0c0a8"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
  </defs>
  <g id="_02_coins" data-name="02_coins" transform="translate(-217.509 -316.06)">
    <g id="Group_3346" data-name="Group 3346" transform="translate(232.991 360.771)">
      <path id="Path_11795" data-name="Path 11795" d="M222.09,362.242c0,24.768,40.281,44.846,89.967,44.846s89.97-20.078,89.97-44.846l0-19.595H222.09v19.595Z" transform="translate(-222.09 -297.507)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11796" data-name="Path 11796" d="M270.183,360.245q5.759.375,11.727.379a175.651,175.651,0,0,0,23.623-1.568v19.561a134.185,134.185,0,0,1-23.623,1.561c-12.731,0-33.44-2.433-47.151-6.648V353.956a155.415,155.415,0,0,0,35.424,6.289Z" transform="translate(-191.944 -270.597)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11797" data-name="Path 11797" d="M266.853,375.287c17.459-8.138,28.442-19.709,28.655-32.565l0,19.341c0,12.961-11.027,24.633-28.658,32.819V375.287Z" transform="translate(-115.575 -297.328)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11798" data-name="Path 11798" d="M240.563,369.852v19.618c-11.588-7.55-18.473-16.989-18.473-27.229V342.647c.054,10.23,6.931,19.655,18.473,27.205Z" transform="translate(-222.09 -297.507)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11799" data-name="Path 11799" d="M263.794,358.52q5.759.375,11.727.379v19.554a168.74,168.74,0,0,1-38.077-4.208V354.681a162.237,162.237,0,0,0,26.35,3.839Z" transform="translate(-185.555 -268.871)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11800" data-name="Path 11800" d="M322.877,386.279a40.944,40.944,0,0,1-.524,6.719.454.454,0,0,1-.892,0,43.374,43.374,0,0,1,0-13.441.454.454,0,0,1,.892,0,40.957,40.957,0,0,1,.524,6.722Zm11.845-1.2a42.256,42.256,0,0,1-.5,6.722.43.43,0,0,1-.852,0,45.121,45.121,0,0,1,0-13.444.43.43,0,0,1,.852,0,42.254,42.254,0,0,1,.5,6.722Zm11.727-2.055a44.039,44.039,0,0,1-.483,6.719.409.409,0,0,1-.814,0,47.293,47.293,0,0,1,0-13.44.409.409,0,0,1,.814,0,44.05,44.05,0,0,1,.483,6.722Zm11.517-3a46.559,46.559,0,0,1-.456,6.719.412.412,0,0,1-.389.372.406.406,0,0,1-.389-.372,49.354,49.354,0,0,1,0-13.44.406.406,0,0,1,.389-.372.412.412,0,0,1,.389.372,46.575,46.575,0,0,1,.456,6.722Zm11.179-4.093a48.811,48.811,0,0,1-.436,6.722.368.368,0,1,1-.737,0,52.011,52.011,0,0,1,0-13.44.368.368,0,1,1,.737,0,48.792,48.792,0,0,1,.436,6.718Zm10.622-5.37a51.271,51.271,0,0,1-.416,6.722.384.384,0,0,1-.348.372.389.389,0,0,1-.351-.372,55,55,0,0,1,0-13.444.389.389,0,0,1,.351-.372.384.384,0,0,1,.348.372,51.267,51.267,0,0,1,.416,6.722Zm9.6-7.016a54.683,54.683,0,0,1-.389,6.718.333.333,0,1,1-.662,0,57.8,57.8,0,0,1,0-13.44.333.333,0,1,1,.662,0,54.744,54.744,0,0,1,.389,6.722Zm-92.315,22.738a40.555,40.555,0,0,0,.527,6.719.452.452,0,0,0,.889,0,43.094,43.094,0,0,0,0-13.441.452.452,0,0,0-.889,0,40.568,40.568,0,0,0-.527,6.722Zm-11.842-1.2a42.675,42.675,0,0,0,.5,6.722.432.432,0,0,0,.855,0,45.415,45.415,0,0,0,0-13.444.432.432,0,0,0-.855,0,42.674,42.674,0,0,0-.5,6.722Zm-11.727-2.055a44.516,44.516,0,0,0,.48,6.719.409.409,0,0,0,.815,0,47.3,47.3,0,0,0,0-13.44.409.409,0,0,0-.815,0,44.527,44.527,0,0,0-.48,6.722Zm-11.521-3a46.531,46.531,0,0,0,.46,6.719.406.406,0,0,0,.389.372.412.412,0,0,0,.389-.372,49.714,49.714,0,0,0,0-13.44.412.412,0,0,0-.389-.372.406.406,0,0,0-.389.372,46.547,46.547,0,0,0-.46,6.722Zm-11.176-4.093a49.378,49.378,0,0,0,.433,6.722.37.37,0,1,0,.74,0,52.015,52.015,0,0,0,0-13.44.37.37,0,1,0-.74,0,49.359,49.359,0,0,0-.433,6.718Zm-10.622-5.37a51.3,51.3,0,0,0,.412,6.722.35.35,0,1,0,.7,0,54.992,54.992,0,0,0,0-13.444.35.35,0,1,0-.7,0,51.3,51.3,0,0,0-.412,6.722Zm-9.608-7.016a54.63,54.63,0,0,0,.392,6.718.333.333,0,1,0,.662,0,58.3,58.3,0,0,0,0-13.44.333.333,0,1,0-.662,0,54.691,54.691,0,0,0-.392,6.722Zm78.4,23.143a39.012,39.012,0,0,0,.548,6.719.476.476,0,0,0,.929,0,41.516,41.516,0,0,0,0-13.44.476.476,0,0,0-.929,0,39.024,39.024,0,0,0-.548,6.722Zm-85.989-32.268a57.105,57.105,0,0,0,.368,6.722c.024.216.156.372.311.372s.287-.155.311-.372a61.514,61.514,0,0,0,0-13.444c-.024-.216-.156-.372-.311-.372s-.287.155-.311.372a57.1,57.1,0,0,0-.368,6.722Zm174,0a57.843,57.843,0,0,1-.368,6.722c-.027.216-.156.372-.311.372s-.287-.155-.314-.372a61.517,61.517,0,0,1,0-13.444c.027-.216.159-.372.314-.372s.284.155.311.372a57.84,57.84,0,0,1,.368,6.722Z" transform="translate(-220.001 -286.38)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11801" data-name="Path 11801" d="M222.31,374.986c0,25.137,39.635,45.512,88.524,45.512s88.527-20.375,88.527-45.512-39.635-45.509-88.527-45.509-88.524,20.375-88.524,45.509Z" transform="translate(-221.567 -328.845)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11802" data-name="Path 11802" d="M222.09,374.305c0,24.86,40.281,45.012,89.967,45.012s89.97-20.152,89.97-45.012-40.281-45.015-89.97-45.015-89.967,20.152-89.967,45.015Z" transform="translate(-222.09 -329.29)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11803" data-name="Path 11803" d="M224.608,368.8c-.581,22.19,35.424,41.132,80.423,42.312s81.947-15.853,82.528-38.043-35.428-41.132-80.423-42.312-81.947,15.857-82.528,38.043Z" transform="translate(-216.115 -325.925)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11804" data-name="Path 11804" d="M224.771,371.112c0,22.356,36.225,40.48,80.906,40.48s80.91-18.124,80.91-40.48-36.225-40.48-80.91-40.48-80.906,18.121-80.906,40.48Z" transform="translate(-215.711 -326.097)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11805" data-name="Path 11805" d="M225.87,371.211c0,22.105,35.816,40.024,79.994,40.024s79.994-17.918,79.994-40.024-35.816-40.021-79.994-40.021-79.994,17.918-79.994,40.021Z" transform="translate(-213.095 -324.769)" fill-rule="evenodd" fill="url(#linear-gradient-8)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11806" data-name="Path 11806" d="M386.435,373.61c-2.582-21.193-37.749-37.979-80.757-37.979S227.5,352.417,224.923,373.61a20.481,20.481,0,0,1-.152-2.5c0-22.356,36.222-40.48,80.906-40.48s80.91,18.125,80.91,40.48a21.169,21.169,0,0,1-.152,2.5Z" transform="translate(-215.711 -326.097)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
    </g>
    <g id="Group_3347" data-name="Group 3347" transform="translate(217.509 339.541)">
      <path id="Path_11807" data-name="Path 11807" d="M217.509,355.962c0,24.765,40.281,44.843,89.967,44.843s89.97-20.078,89.97-44.843l0-19.6H217.509v19.6Z" transform="translate(-217.509 -291.227)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11808" data-name="Path 11808" d="M265.6,353.966q5.759.375,11.727.378a175.656,175.656,0,0,0,23.623-1.568v19.557a133.857,133.857,0,0,1-23.623,1.561c-12.731,0-33.44-2.43-47.151-6.644V347.673a155.24,155.24,0,0,0,35.424,6.293Z" transform="translate(-187.363 -264.317)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11809" data-name="Path 11809" d="M262.272,369.005c17.459-8.138,28.442-19.709,28.655-32.565l0,19.341c0,12.957-11.027,24.633-28.658,32.819V369.005Z" transform="translate(-110.994 -291.046)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11810" data-name="Path 11810" d="M235.982,363.569v19.618c-11.588-7.546-18.473-16.986-18.473-27.226v-19.6c.054,10.23,6.931,19.659,18.473,27.205Z" transform="translate(-217.509 -291.227)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11811" data-name="Path 11811" d="M259.213,352.238q5.759.375,11.727.378v19.551a168.745,168.745,0,0,1-38.077-4.2V348.4a162.238,162.238,0,0,0,26.35,3.839Z" transform="translate(-180.974 -262.589)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11812" data-name="Path 11812" d="M318.3,379.994a40.95,40.95,0,0,1-.524,6.722.454.454,0,0,1-.892,0,43.373,43.373,0,0,1,0-13.44.454.454,0,0,1,.892,0,40.934,40.934,0,0,1,.524,6.718Zm11.845-1.2a42.19,42.19,0,0,1-.5,6.718.43.43,0,0,1-.852,0,45.09,45.09,0,0,1,0-13.44.43.43,0,0,1,.852,0,42.236,42.236,0,0,1,.5,6.722Zm11.727-2.055a44.04,44.04,0,0,1-.483,6.718.409.409,0,0,1-.814,0,47.294,47.294,0,0,1,0-13.44.409.409,0,0,1,.814,0,44.056,44.056,0,0,1,.483,6.722Zm11.517-3.008a46.584,46.584,0,0,1-.456,6.722.412.412,0,0,1-.389.372.406.406,0,0,1-.389-.372,49.368,49.368,0,0,1,0-13.44.406.406,0,0,1,.389-.372.412.412,0,0,1,.389.372,46.571,46.571,0,0,1,.456,6.719Zm11.179-4.089a48.812,48.812,0,0,1-.436,6.722.368.368,0,1,1-.737,0,52.041,52.041,0,0,1,0-13.444.368.368,0,0,1,.737,0,48.815,48.815,0,0,1,.436,6.722Zm10.622-5.37a51.785,51.785,0,0,1-.412,6.719.352.352,0,1,1-.7,0,54.967,54.967,0,0,1,0-13.44.352.352,0,1,1,.7,0,51.877,51.877,0,0,1,.412,6.722Zm9.6-7.016a54.687,54.687,0,0,1-.389,6.719.333.333,0,1,1-.662,0,57.8,57.8,0,0,1,0-13.44.333.333,0,1,1,.662,0,54.7,54.7,0,0,1,.389,6.722Zm-92.315,22.734a40.561,40.561,0,0,0,.527,6.722.452.452,0,0,0,.889,0,43.1,43.1,0,0,0,0-13.44.452.452,0,0,0-.889,0,40.545,40.545,0,0,0-.527,6.718Zm-11.842-1.2a42.628,42.628,0,0,0,.5,6.718.432.432,0,0,0,.855,0,45.389,45.389,0,0,0,0-13.44.432.432,0,0,0-.855,0,42.675,42.675,0,0,0-.5,6.722Zm-11.727-2.055a44.509,44.509,0,0,0,.48,6.718.409.409,0,0,0,.814,0,47.3,47.3,0,0,0,0-13.44.409.409,0,0,0-.814,0,44.525,44.525,0,0,0-.48,6.722Zm-11.521-3.008a46.553,46.553,0,0,0,.46,6.722.406.406,0,0,0,.389.372.412.412,0,0,0,.389-.372,49.722,49.722,0,0,0,0-13.44.412.412,0,0,0-.389-.372.406.406,0,0,0-.389.372,46.54,46.54,0,0,0-.46,6.719Zm-11.179-4.089a49.34,49.34,0,0,0,.436,6.722.37.37,0,1,0,.74,0,52.044,52.044,0,0,0,0-13.444.37.37,0,0,0-.74,0,49.342,49.342,0,0,0-.436,6.722Zm-10.618-5.37a51.219,51.219,0,0,0,.412,6.719.35.35,0,1,0,.7,0,54.971,54.971,0,0,0,0-13.44.35.35,0,1,0-.7,0,51.31,51.31,0,0,0-.412,6.722Zm-9.608-7.016a54.668,54.668,0,0,0,.392,6.719.333.333,0,1,0,.662,0,58.291,58.291,0,0,0,0-13.44.333.333,0,1,0-.662,0,54.682,54.682,0,0,0-.392,6.722Zm78.4,23.143a39.022,39.022,0,0,0,.547,6.719.476.476,0,0,0,.929,0,41.522,41.522,0,0,0,0-13.44.476.476,0,0,0-.929,0,39.034,39.034,0,0,0-.547,6.722Zm-85.989-32.268a57.033,57.033,0,0,0,.368,6.718c.024.216.155.372.311.372s.287-.156.311-.372a61.477,61.477,0,0,0,0-13.44c-.024-.216-.155-.372-.311-.372s-.287.156-.311.372a57.1,57.1,0,0,0-.368,6.722Zm174,0a57.788,57.788,0,0,1-.368,6.718c-.027.216-.156.372-.311.372s-.287-.156-.314-.372a61.492,61.492,0,0,1,0-13.44c.027-.216.155-.372.314-.372s.284.156.311.372a57.852,57.852,0,0,1,.368,6.722Z" transform="translate(-215.42 -280.098)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11813" data-name="Path 11813" d="M217.729,368.706c0,25.137,39.635,45.512,88.527,45.512s88.524-20.375,88.524-45.512-39.635-45.512-88.524-45.512-88.527,20.379-88.527,45.512Z" transform="translate(-216.986 -322.565)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11814" data-name="Path 11814" d="M217.509,368.02c0,24.863,40.277,45.015,89.967,45.015s89.97-20.152,89.97-45.015-40.281-45.012-89.97-45.012-89.967,20.152-89.967,45.012Z" transform="translate(-217.509 -323.008)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11815" data-name="Path 11815" d="M220.027,362.523c-.581,22.19,35.424,41.132,80.423,42.312s81.947-15.853,82.528-38.043-35.428-41.132-80.423-42.312-81.947,15.853-82.528,38.043Z" transform="translate(-211.534 -319.643)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11816" data-name="Path 11816" d="M220.19,364.827c0,22.359,36.225,40.48,80.906,40.48s80.909-18.121,80.909-40.48S345.78,324.35,301.1,324.35s-80.906,18.121-80.906,40.477Z" transform="translate(-211.13 -319.815)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11817" data-name="Path 11817" d="M381.853,367.328c-2.582-21.193-37.749-37.979-80.757-37.979s-78.175,16.786-80.754,37.979a20.536,20.536,0,0,1-.152-2.5c0-22.356,36.225-40.477,80.906-40.477s80.909,18.121,80.909,40.477a21.239,21.239,0,0,1-.152,2.5Z" transform="translate(-211.13 -319.815)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11818" data-name="Path 11818" d="M220.342,364.3c0,22.1,35.813,40.02,79.99,40.02s79.994-17.918,79.994-40.02-35.813-40.024-79.994-40.024-79.99,17.918-79.99,40.024Z" transform="translate(-210.768 -319.986)" fill-rule="evenodd" fill="url(#linear-gradient-18)" style="mix-blend-mode: multiply;isolation: isolate"/>
    </g>
    <g id="Group_3348" data-name="Group 3348" transform="translate(229.043 316.06)">
      <path id="Path_11819" data-name="Path 11819" d="M220.922,349.014c0,24.769,40.281,44.846,89.97,44.846s89.97-20.078,89.97-44.846l0-19.6H220.922v19.6Z" transform="translate(-220.922 -284.279)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11820" data-name="Path 11820" d="M269.016,347.015q5.759.375,11.727.378a176.142,176.142,0,0,0,23.62-1.565v19.554a133.716,133.716,0,0,1-23.62,1.565c-12.731,0-33.441-2.433-47.151-6.648V340.726a155.432,155.432,0,0,0,35.424,6.289Z" transform="translate(-190.773 -257.367)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11821" data-name="Path 11821" d="M265.686,362.057c17.459-8.138,28.442-19.709,28.655-32.565l0,19.341c0,12.957-11.027,24.633-28.658,32.819V362.057Z" transform="translate(-114.405 -284.098)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11822" data-name="Path 11822" d="M239.4,356.621v19.622c-11.588-7.55-18.476-16.989-18.476-27.229v-19.6c.057,10.233,6.935,19.659,18.476,27.205Z" transform="translate(-220.922 -284.279)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-22)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11823" data-name="Path 11823" d="M262.627,345.29q5.759.375,11.727.378v19.554a168.738,168.738,0,0,1-38.077-4.208V341.451a162.182,162.182,0,0,0,26.35,3.839Z" transform="translate(-184.384 -255.641)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11824" data-name="Path 11824" d="M321.713,373.049a40.937,40.937,0,0,1-.524,6.719.454.454,0,0,1-.892,0,43.371,43.371,0,0,1,0-13.44.454.454,0,0,1,.892,0,40.947,40.947,0,0,1,.524,6.722Zm11.845-1.2a42.656,42.656,0,0,1-.5,6.722.43.43,0,0,1-.852,0,45.118,45.118,0,0,1,0-13.444.43.43,0,0,1,.852,0,42.656,42.656,0,0,1,.5,6.722Zm11.724-2.055a44.522,44.522,0,0,1-.48,6.718.409.409,0,0,1-.815,0,47.3,47.3,0,0,1,0-13.44.409.409,0,0,1,.815,0,44.538,44.538,0,0,1,.48,6.722Zm11.521-3.008a46.539,46.539,0,0,1-.46,6.722.387.387,0,0,1-.774,0,49.362,49.362,0,0,1,0-13.44.387.387,0,0,1,.774,0,46.525,46.525,0,0,1,.46,6.719Zm11.179-4.089a48.8,48.8,0,0,1-.436,6.722.37.37,0,1,1-.74,0,52.419,52.419,0,0,1,0-13.44.37.37,0,1,1,.74,0,48.785,48.785,0,0,1,.436,6.719Zm10.622-5.37a51.248,51.248,0,0,1-.416,6.722.35.35,0,1,1-.7,0,54.994,54.994,0,0,1,0-13.444.35.35,0,1,1,.7,0,51.25,51.25,0,0,1,.416,6.722Zm9.6-7.016a53.983,53.983,0,0,1-.392,6.719.332.332,0,1,1-.659,0,57.786,57.786,0,0,1,0-13.44.332.332,0,1,1,.659,0,54.038,54.038,0,0,1,.392,6.722Zm-92.315,22.738a40.943,40.943,0,0,0,.524,6.719.454.454,0,0,0,.892,0,43.366,43.366,0,0,0,0-13.44.454.454,0,0,0-.892,0,40.954,40.954,0,0,0-.524,6.722Zm-11.845-1.2a42.656,42.656,0,0,0,.5,6.722.43.43,0,0,0,.852,0,45.118,45.118,0,0,0,0-13.444.43.43,0,0,0-.852,0,42.656,42.656,0,0,0-.5,6.722Zm-11.724-2.055a44.51,44.51,0,0,0,.48,6.718.409.409,0,0,0,.814,0,47.3,47.3,0,0,0,0-13.44.409.409,0,0,0-.814,0,44.526,44.526,0,0,0-.48,6.722ZM260.8,366.787a46.549,46.549,0,0,0,.46,6.722.387.387,0,0,0,.774,0,49.362,49.362,0,0,0,0-13.44.387.387,0,0,0-.774,0,46.535,46.535,0,0,0-.46,6.719Zm-11.18-4.089a48.811,48.811,0,0,0,.436,6.722.37.37,0,1,0,.74,0,52.013,52.013,0,0,0,0-13.44.37.37,0,1,0-.74,0,48.8,48.8,0,0,0-.436,6.719Zm-10.619-5.37a51.3,51.3,0,0,0,.412,6.722.35.35,0,1,0,.7,0,55,55,0,0,0,0-13.444.35.35,0,1,0-.7,0,51.3,51.3,0,0,0-.412,6.722Zm-9.608-7.016a53.966,53.966,0,0,0,.392,6.719.378.378,0,0,0,.331.372c.166,0,.3-.155.331-.372a58.3,58.3,0,0,0,0-13.44c-.03-.216-.166-.372-.331-.372a.378.378,0,0,0-.331.372,54.02,54.02,0,0,0-.392,6.722Zm78.4,23.143a39.018,39.018,0,0,0,.547,6.719.474.474,0,0,0,.926,0,41.264,41.264,0,0,0,0-13.44.474.474,0,0,0-.926,0,39.029,39.029,0,0,0-.547,6.722ZM221.8,341.187a57.839,57.839,0,0,0,.368,6.722c.027.216.159.368.314.368s.284-.152.311-.368a61.5,61.5,0,0,0,0-13.444c-.027-.216-.155-.372-.311-.372s-.287.156-.314.372a57.839,57.839,0,0,0-.368,6.722Zm174.005,0a57.841,57.841,0,0,1-.368,6.722c-.027.216-.159.368-.314.368s-.284-.152-.311-.368a61.527,61.527,0,0,1,0-13.444c.027-.216.156-.372.311-.372s.287.156.314.372a57.841,57.841,0,0,1,.368,6.722Z" transform="translate(-218.833 -273.15)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11825" data-name="Path 11825" d="M221.143,361.758c0,25.137,39.632,45.516,88.524,45.516s88.527-20.379,88.527-45.516-39.635-45.512-88.527-45.512-88.524,20.379-88.524,45.512Z" transform="translate(-220.396 -315.617)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11826" data-name="Path 11826" d="M220.922,361.076c0,24.86,40.281,45.012,89.97,45.012s89.97-20.152,89.97-45.012-40.281-45.016-89.97-45.016-89.97,20.152-89.97,45.016Z" transform="translate(-220.922 -316.06)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11827" data-name="Path 11827" d="M223.441,355.575c-.581,22.19,35.424,41.132,80.423,42.312s81.947-15.853,82.528-38.043-35.428-41.132-80.426-42.312-81.944,15.857-82.525,38.043Z" transform="translate(-214.945 -312.695)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11828" data-name="Path 11828" d="M223.6,357.882c0,22.356,36.222,40.48,80.906,40.48s80.906-18.124,80.906-40.48S349.194,317.4,304.51,317.4,223.6,335.523,223.6,357.882Z" transform="translate(-214.54 -312.867)" fill-rule="evenodd" fill="url(#linear-gradient-7)"/>
      <path id="Path_11829" data-name="Path 11829" d="M385.264,360.38c-2.579-21.193-37.746-37.979-80.754-37.979s-78.175,16.786-80.754,37.979a20.461,20.461,0,0,1-.152-2.5c0-22.359,36.222-40.48,80.906-40.48s80.906,18.121,80.906,40.48a20.479,20.479,0,0,1-.152,2.5Z" transform="translate(-214.54 -312.867)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11830" data-name="Path 11830" d="M304.4,318.881c-41.386,0-75.516,15.546-80.325,35.6a21.209,21.209,0,0,0-.429,2.376,21.367,21.367,0,0,0,.767,3.626,26.621,26.621,0,0,0,4.684,8.706l.713-.1,100.359-14.606,51.308-7.465c-10.429-16.32-40.994-28.138-77.077-28.138Z" transform="translate(-214.433 -309.347)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11831" data-name="Path 11831" d="M229.418,358.94c.517-19.75,29.851-35.417,68.033-37.79,4.37-.365,8.861-.554,13.44-.554,40.524,0,74.086,14.9,79.987,34.353q.319.816.571,1.636l8.685-1.264c-5.647-22.143-43.451-39.26-89.243-39.26-49.689,0-89.97,20.152-89.97,45.016v.122c.037,6.783,3.069,13.207,8.469,18.969l8.824-1.284a36.1,36.1,0,0,1-2.626-2.977,26.621,26.621,0,0,1-4.684-8.706,21.358,21.358,0,0,1-1.419-6,19.3,19.3,0,0,1-.068-2.258Z" transform="translate(-220.922 -316.06)" fill-rule="evenodd" fill="url(#linear-gradient-5)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
  </g>
</svg>
