#!/bin/bash

PS3='Nhập lựa chọn: '
options=("Build Android" "Build iOS" "Build Android và iOS" "Thoát")
releasenote=""
fastlane=""

enter_release()
{
    echo 'Nhập Release note: (Ấn Enter để xong)'
    echo 'LƯU Ý TẮT UNIKEY LÚC NHẬP'
    read inputtext
    releasenote=${inputtext}
}

run_fastlane()
{
while :
do
    enter_release
    case $1 in
        1)
            cd android
            echo "==================================="
            echo "==    Running Fastlane Android   =="
            echo "==================================="
            fastlane deploy_firebase release_note:"${releasenote}"
            break
            ;;
        2)
            cd ios
            echo "==================================="
            echo "==      Running Fastlane iOS     =="
            echo "==================================="
            fastlane deploy_firebase release_note:"${releasenote}"
            break
            ;;
        3)
            cd ios
            echo "==================================="
            echo "==      Running Fastlane iOS     =="
            echo "==================================="
            fastlane deploy_firebase release_note:"${releasenote}"
            cd ../android
            echo "==================================="
            echo "==    Running Fastlane Android   =="
            echo "==================================="
            fastlane deploy_firebase release_note:"${releasenote}"
            break
            ;;
    esac
done
}

select opt in "${options[@]}"
do
    case $opt in
        "Build Android")
            run_fastlane 1
            break
            ;;
        "Build iOS")
            run_fastlane 2
            break
            ;;
        "Build Android và iOS")
            run_fastlane 3
            break
            ;;
        "Thoát")
            break
            ;;
        *) echo "Lựa chọn không đúng";;
    esac
done