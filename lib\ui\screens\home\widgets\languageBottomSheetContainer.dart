import 'package:flutter/material.dart';
import 'package:flutterquiz/features/localization/appLocalizationCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageDailogContainer extends StatelessWidget {
  LanguageDailogContainer({Key? key}) : super(key: key);
  Future<void> saveLang(String languageCode) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setString('language', languageCode);
  }

  @override
  Widget build(BuildContext context) {
    final supportedLanguages =
        context.read<SystemConfigCubit>().getSupportedLanguages();
    return BlocBuilder<AppLocalizationCubit, AppLocalizationState>(
      bloc: context.read<AppLocalizationCubit>(),
      builder: (context, state) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: supportedLanguages.map((language) {
                return Container(
                  margin: EdgeInsets.symmetric(vertical: 10.0),
                  decoration: BoxDecoration(
                    color: state.language ==
                            UiUtils.getLocaleFromLanguageCode(
                                language.languageCode)
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.secondary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: ListTile(
                    trailing: state.language ==
                            UiUtils.getLocaleFromLanguageCode(
                                language.languageCode)
                        ? Icon(
                            Icons.check,
                            color: Theme.of(context).colorScheme.surface,
                          )
                        : SizedBox(),
                    onTap: () {
                      if (state.language !=
                          UiUtils.getLocaleFromLanguageCode(
                              language.languageCode)) {
                        context
                            .read<AppLocalizationCubit>()
                            .changeLanguage(language.languageCode);
                        saveLang(language.languageCode);
                      }
                    },
                    title: Text(
                      language.language,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.surface,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }
}

/*

 */