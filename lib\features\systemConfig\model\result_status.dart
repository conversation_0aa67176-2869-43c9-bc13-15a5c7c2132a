class ResultStatus {
  String? rid;
  String? result_status;
  String? total_time;
  String? percentage_obtained;
  String? score_obtained;
  String? score_individual;
  String? r_qids;

  ResultStatus(
      {this.rid,
      this.result_status,
      this.percentage_obtained,
      this.score_obtained,
      this.score_individual,
      this.r_qids,
      this.total_time});

  ResultStatus.fromJson(Map<String, dynamic> json) {
    this.rid = json["rid"];
    this.result_status = json["result_status"];
    this.total_time = json["total_time"];
    this.percentage_obtained = json["percentage_obtained"];
    this.score_obtained = json["score_obtained"];
    this.score_individual = json["score_individual"];
    this.r_qids = json["r_qids"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["rid"] = this.rid;
    data["result_status"] = this.result_status;
    data["total_time"] = this.total_time;
    data["percentage_obtained"] = this.percentage_obtained;
    data["score_obtained"] = this.score_obtained;
    data["score_individual"] = this.score_individual;
    data["r_qids"] = this.r_qids;
    return data;
  }

  factory ResultStatus.fromMap(Map<String, dynamic> json) => ResultStatus(
        rid: json["rid"],
        result_status: json["result_status"],
        total_time: json["total_time"],
        percentage_obtained: json["percentage_obtained"],
        score_obtained: json["score_obtained"],
        score_individual: json["score_individual"],
        r_qids: json["r_qids"],
      );
}
