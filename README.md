# ScrumPass Exam Tool

## Build app và đẩy lên firebase app distribution bằng fastlane
(Cài fastlane ở máy trước nếu chưa cài https://docs.fastlane.tools/)

Đã có script để chạy ngắn gọn hơn, ch<PERSON><PERSON> lệnh
> sh build.sh

1. Android
 - Từ project root folder chạy lệnh:
> cd android && fastlane deploy_firebase release_note:"Release Note ghi ở đây nè"

2. iOS
 - Từ project root folder chạy lệnh: 
> cd ios && fastlane deploy_firebase release_note:"Release Note vẫn ghi ở đây nè" version:"2.2.2"

3. Combo chạy cả 2

> cd ios && fastlane deploy_firebase release_note:"Release Note vẫn ghi ở đây nè" version:"2.2.2" && cd .. && cd android && fastlane deploy_firebase release_note:"Release Note ghi ở đây nè"