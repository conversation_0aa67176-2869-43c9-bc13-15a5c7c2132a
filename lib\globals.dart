import 'dart:convert';
import 'package:dart_ipify/dart_ipify.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io' show Platform;
import 'package:flutterquiz/utils/constants.dart';

String apiUrl = "$examUrl/api/";
String apiUrlBase = "$examUrl/";
// const String apiUrl = "http://***********/api/";
// const String apiUrlBase = "http://***********/";

String generateMd5(String input) {
  Codec<String, String> stringToBase64 = utf8.fuse(base64);
  return stringToBase64.encode(input);
}

class getUserInfor {
  String url = apiUrl + "get_quiz_list_by_user";
  //String url = "http://***********/api/quiz_list_api";
  String appkey = generateMd5("bXlfYXBwbGljYXRpb25fc2VjcmV0" +
      DateTime.now().millisecondsSinceEpoch.toString());
}

class Common {
  static String username = '';
  static String appName = 'PSM Exam Tool';
  static String appid = 'com.scrumpass.exam';
  static var update;
  static String platform = '';
  static String totalQuiz = 'No Data';
  static String average = 'No Data';
  static String recent = 'No Data';
  static String apiDomain = '$examUrl/';
  static bool premium = false;
  static bool trial = false;
  static bool registeredFromApp = false;
  static bool inPremiumGroup = false;
  static String trialEndDate = '';
  static String premiumType = '';
  static String revenuecatAndroid = 'goog_XQQqjuOIgvsKqPRkBfGFouIOqTK';
  static String revenuecatiOS = 'appl_WNTqEadJDGFVOLeEKfQUiMcBjlD';
  static String appleAppId = "1625067707";
  static String version = '';
}

class CommonFont {
  static String size = '20';
}

class url {
  // final String mainUrl = "http://***********/scrumpass-exam/api/";
  final String mainUrl = apiUrl;
}

Future<String> GetDeviceInfo() async {
  DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  String ipv4 = "";
  try {
    ipv4 = await Ipify.ipv4();
  } catch (e) {}
  String brand = "";
  String model = "";
  //ios
  if (Platform.isIOS) {
    Common.platform = 'iOS';
    IosDeviceInfo androidInfo = await deviceInfoPlugin.iosInfo;
    if (androidInfo.systemName != "") {
      brand = androidInfo.name!;
    } else {
      brand = "";
    }
  }
  //end ios
  //android
  if (Platform.isAndroid) {
    Common.platform = 'Android';
    AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
    if (androidInfo.brand != "") {
      brand = androidInfo.brand!;
    } else {
      brand = "";
    }
    if (androidInfo.model != "") {
      model = androidInfo.model!;
    } else {
      model = "";
    }
  }
  //end android
  String info = brand + "-" + model + "-" + ipv4;
  return info;
}
