import 'package:flutter/material.dart';

class NumberRectangleWidget extends StatelessWidget {
  final int number;
  final Color backgroundColor;

  NumberRectangleWidget({required this.number, required this.backgroundColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 36,
      color: backgroundColor,
      child: Center(
        child: Text(
          number.toString(),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
