import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/reportQuestionPopup.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart';
import 'package:flutterquiz/ui/widgets/timeoutDialog.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/exam/cubits/examCubit.dart';
import 'package:flutterquiz/features/exam/examRepository.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/qlist_model.dart';
import 'package:flutterquiz/ui/screens/exam/widgets/examQuestionStatusBottomSheetContainer.dart';
import 'package:flutterquiz/ui/screens/exam/widgets/examTimerContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/questionExamContainer.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/ui/widgets/confirmSubmit.dart';
import 'package:flutterquiz/ui/widgets/customBackButton.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/exitGameDailog.dart';
import 'package:flutterquiz/ui/widgets/optionContainer.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/ui/widgets/quizPlayAreaBackgroundContainer.dart';
import 'package:flutterquiz/ui/widgets/settingButton.dart';
import 'package:flutterquiz/ui/widgets/settingsDialogContainer.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';

import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:ios_insecure_screen_detector/ios_insecure_screen_detector.dart';
import 'package:rotated_corner_decoration/rotated_corner_decoration.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class ExamScreen extends StatefulWidget {
  ExamScreen(
      {Key? key,
      required this.exam,
      required this.userId,
      required this.quizDuration})
      : super(key: key);
  final Qlist exam;
  final String userId;
  final String quizDuration;
  @override
  _ExamScreenState createState() => _ExamScreenState();

  static Route<ExamScreen> route(RouteSettings routeSettings) {
    Map? arguments = routeSettings.arguments as Map<dynamic, dynamic>?;
    return CupertinoPageRoute(
      builder: (context) => MultiBlocProvider(
          providers: [
            BlocProvider<ExamCubit>(create: (_) => ExamCubit(ExamRepository())),
          ],
          child: ExamScreen(
              exam: arguments!['exam'],
              userId: arguments['userId'],
              quizDuration: arguments['quizDuration'] ?? "")),
    );
  }
}

class _ExamScreenState extends State<ExamScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  final GlobalKey<ExamTimerContainerState> timerKey =
      GlobalKey<ExamTimerContainerState>();

  late PageController pageController = PageController();
  late AnimationController timerAnimationController;
  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;
  late Animation<double> questionSlideAnimation;
  late Animation<double> questionScaleUpAnimation;
  late Animation<double> questionScaleDownAnimation;
  late Animation<double> questionContentAnimation;
  late Animation<Offset> questionSlideDirectionAnimation;
  late AnimationController animationController;
  late AnimationController topContainerAnimationController;

  late List<Question> ques;
  bool isBottomSheetOpen = false;
  bool isSettingDialogOpen = false;

  Timer? canGiveExamAgainTimer;
  bool canGiveExamAgain = true;
  late ValueNotifier<bool> isSubmiting = ValueNotifier(false);

  int canGiveExamAgainTimeInSeconds = 5;

  bool isExitDialogOpen = false;
  bool userLeftTheExam = false;

  bool showYouLeftTheExam = false;
  bool isExamQuestionStatusBottomsheetOpen = false;

  int currentQuestionIndex = 0;

  IosInsecureScreenDetector? _iosInsecureScreenDetector;
  late bool isScreenRecordingInIos = false;

  List<String> iosCapturedScreenshotQuestionIds = [];

  int startTime = 0;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    startTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    //wake lock enable so phone will not lock automatically after sometime

    WakelockPlus.enable();

    WidgetsBinding.instance.addObserver(this);

    if (Platform.isIOS) {
      initScreenshotAndScreenRecordDetectorInIos();
    } else {
      // FlutterWindowManager.addFlags(FlutterWindowManager.FLAG_SECURE);
    }

    //start timer
    // Future.delayed(Duration.zero, () {
    //   timerKey.currentState?.startTimer();
    // });
    _getQuestions();
    initializeAnimation();
    timerAnimationController = AnimationController(
        vsync: this,
        duration: Duration(minutes: int.parse(widget.exam.duration)))
      ..addStatusListener(currentUserTimerAnimationStatusListener);

    animationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 100));
    topContainerAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 100));
  }

  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      navigateToResultScreenTimeOut();
    }
  }

  void _getQuestions() {
    Future.delayed(Duration.zero, () {
      context
          .read<ExamCubit>()
          .startExam(exam: widget.exam, userId: widget.userId);
    });
  }

  void initScreenshotAndScreenRecordDetectorInIos() async {
    _iosInsecureScreenDetector = IosInsecureScreenDetector();
    await _iosInsecureScreenDetector?.initialize();
    _iosInsecureScreenDetector?.addListener(
        iosScreenshotCallback, iosScreenrecordCallback);
  }

  void iosScreenshotCallback() {
    print("User took screenshot");
    iosCapturedScreenshotQuestionIds.add(
        context.read<ExamCubit>().getQuestions()[currentQuestionIndex].id!);
  }

  void iosScreenrecordCallback(bool isRecording) {
    setState(() {
      isScreenRecordingInIos = isRecording;
    });
  }

  void setCanGiveExamTimer() {
    canGiveExamAgainTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (canGiveExamAgainTimeInSeconds == 0) {
        timer.cancel();

        //can give exam again false
        canGiveExamAgain = false;

        //show user left the exam
        setState(() {
          showYouLeftTheExam = true;
        });
        //submit result
        submitResult();
      } else {
        canGiveExamAgainTimeInSeconds--;
      }
    });
  }

  @override
  void didChangeAppLifecycleState(appState) {
    if (appState == AppLifecycleState.paused) {
      // setCanGiveExamTimer();
    } else if (appState == AppLifecycleState.resumed) {
      canGiveExamAgainTimer?.cancel();
      //if user can give exam again
      if (canGiveExamAgain) {
        canGiveExamAgainTimeInSeconds = 5;
      }
    }
  }

  @override
  void dispose() {
    canGiveExamAgainTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    WakelockPlus.disable();
    _iosInsecureScreenDetector?.dispose();
    if (Platform.isAndroid) {
      // FlutterWindowManager.clearFlags(FlutterWindowManager.FLAG_SECURE);
    }
    timerAnimationController
        .removeStatusListener(currentUserTimerAnimationStatusListener);
    timerAnimationController.dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    super.dispose();
  }

  void showExamQuestionStatusBottomSheet() {
    isExamQuestionStatusBottomsheetOpen = true;
    showModalBottomSheet(
        isDismissible: true,
        enableDrag: true,
        isScrollControlled: true,
        elevation: 5.0,
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
        ),
        builder: (context) {
          return ExamQuestionStatusBottomSheetContainer(
            navigateToResultScreen: navigateToResultScreen,
            pageController: pageController,
          );
        }).then((value) => isExamQuestionStatusBottomsheetOpen = false);
  }

  bool hasSubmittedAnswerForCurrentQuestion() {
    return context
        .read<ExamCubit>()
        .getQuestions()[currentQuestionIndex]
        .attempted;
  }

  Future<String> attempPost(String info) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= "";
    final response = await dio.post(getUserInfor().url,
        data: FormData.fromMap(
            {"key": getUserInfor().appkey, "token": token, "info": info}));

    if (response.statusCode == 200) {
      preferences.setString('token', response.data.toString());
      return response.data;
    } else {
      //status = false;
      return "";
    }
  }

  retryFuture(future, delay) {
    Future.delayed(Duration(milliseconds: delay), () {
      future();
    });
  }

  Future<void> getListResult() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user, 'exam': '0'};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_result_list_by_user"), body: {
          "key": getUserInfor().appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('result', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListResult, 200);
      }
    }
  }

  Future submitResult() async {
    return await context.read<ExamCubit>().submitResult(
        capturedQuestionIds: iosCapturedScreenshotQuestionIds,
        rulesViolated: iosCapturedScreenshotQuestionIds.isNotEmpty,
        userId: context.read<UserDetailsCubit>().getUserId(),
        email: context.read<UserDetailsCubit>().getUserEmail() ?? "",
        startTime: startTime,
        totalDuration: timerKey.currentState
                ?.getCompletedExamDurationSecond()
                .toString() ??
            "0");
  }

  void submitAnswer(String submittedAnswerId) {
    context.read<ExamCubit>().updateQuestionWithAnswer(
        context.read<ExamCubit>().getQuestions()[currentQuestionIndex].id!,
        submittedAnswerId);
  }

  void submitBookmark(bool bookmark) {
    context.read<ExamCubit>().updateQuestionBookmark(
        context.read<ExamCubit>().getQuestions()[currentQuestionIndex].id!,
        !bookmark);
  }

  void navigateToResultScreen() async {
    isSubmiting.value = true;
    // timerAnimationController.stop();
    Navigator.of(context).pop();
    if (isExitDialogOpen) {
      Navigator.of(context).pop();
    }

    if (isBottomSheetOpen) {
      Navigator.of(context).pop();
    }

    try {
      setState(() {
        _isLoading = true;
      });
      final response = await submitResult();
      await ApiServices().getUserInforApi();
      getListResult();
      setState(() {
        _isLoading = false;
      });

      // Navigator.of(context).pop();
      Navigator.of(context)
          .pushReplacementNamed(Routes.resultSummary, arguments: {
        "rid": response['rid'].toString(),
        "quid": response['quid'],
        "quiz_name": response['quiz_name'],
        "pass_percentage": response['pass_percentage'],
        "duration": response['duration'],
        "retry": true,
        "backButtonStatus": false,
        "exam": widget.exam,
        "userId": widget.userId,
        "quiz_duration": widget.quizDuration.toString()
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      Navigator.of(context).pop();
      UiUtils.setSnackbar(
          AppLocalization.of(context)!.getTranslatedValues(
              convertErrorCodeToLanguageKey(e.toString()))!,
          context,
          false);
    }

    // Navigator.of(context).pushReplacementNamed(Routes.result, arguments: {
    //   "quizType": QuizTypes.exam,
    //   "exam": context.read<ExamCubit>().getExam(),
    //   "obtainedMarks": context
    //       .read<ExamCubit>()
    //       .obtainedMarks(context.read<UserDetailsCubit>().getUserFirebaseId()),
    //   "examCompletedInMinutes":
    //       timerKey.currentState?.getCompletedExamDuration(),
    //   "correctExamAnswers": context
    //       .read<ExamCubit>()
    //       .correctAnswers(context.read<UserDetailsCubit>().getUserFirebaseId()),
    //   "incorrectExamAnswers": context.read<ExamCubit>().incorrectAnswers(
    //       context.read<UserDetailsCubit>().getUserFirebaseId()),
    //   "numberOfPlayer": 1,
    // });
  }

  void navigateToResultScreenTimeOut() async {
    // isSubmiting.value = true;
    // timerAnimationController.stop();
    showDialog(context: context, builder: (context) => TimeOutDialog())
        .then((value) async {
      try {
        setState(() {
          _isLoading = true;
        });
        final response = await submitResult();

        getListResult();
        if (isExitDialogOpen) {
          Navigator.of(context).pop();
        }
        if (isBottomSheetOpen) {
          Navigator.of(context).pop();
        }
        setState(() {
          _isLoading = false;
        });
        Navigator.of(context)
            .pushReplacementNamed(Routes.resultSummary, arguments: {
          "rid": response['rid'].toString(),
          "quid": response['quid'],
          "quiz_name": response['quiz_name'],
          "pass_percentage": response['pass_percentage'],
          "duration": response['duration'],
          "retry": false,
          "backButtonStatus": false,
          "quiz_duration": widget.exam.duration.toString()
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        Navigator.of(context).pop();
        UiUtils.setSnackbar(
            AppLocalization.of(context)!.getTranslatedValues(
                convertErrorCodeToLanguageKey(e.toString()))!,
            context,
            false);
      }
    });
  }

  // Widget _buildBottomMenu() {
  //   return Container(
  //     decoration: BoxDecoration(
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(20),
  //           topRight: Radius.circular(20),
  //         ),
  //         color: Theme.of(context).colorScheme.surface),
  //     padding: EdgeInsets.only(bottom: 8.0, top: 8.0, left: 20, right: 20),
  //     child: Row(
  //       children: [
  //         Opacity(
  //           opacity: currentQuestionIndex != 0 ? 1.0 : 0.5,
  //           child: IconButton(
  //               onPressed: () {
  //                 if (currentQuestionIndex != 0) {
  //                   pageController.previousPage(
  //                       duration: Duration(milliseconds: 250),
  //                       curve: Curves.easeInOut);
  //                 }
  //               },
  //               icon: Icon(
  //                 Icons.arrow_back_ios,
  //                 color: Theme.of(context).primaryColor,
  //               )),
  //         ),
  //         Spacer(),
  //         GestureDetector(
  //           onTap: () {
  //             showExamQuestionStatusBottomSheet();
  //           },
  //           child: CircleAvatar(
  //             backgroundColor: Theme.of(context).primaryColor,
  //             radius: 20,
  //             child: Padding(
  //               padding: const EdgeInsets.all(5.0),
  //               child:
  //                   SvgPicture.asset(UiUtils.getImagePath("moveto_icon.svg")),
  //             ),
  //           ),
  //         ),
  //         Spacer(),
  //         Opacity(
  //           opacity: (context.read<ExamCubit>().getQuestions().length - 1) !=
  //                   currentQuestionIndex
  //               ? 1.0
  //               : 0.5,
  //           child: IconButton(
  //               onPressed: () {
  //                 if (context.read<ExamCubit>().getQuestions().length - 1 !=
  //                     currentQuestionIndex) {
  //                   pageController.nextPage(
  //                       duration: Duration(milliseconds: 250),
  //                       curve: Curves.easeInOut);
  //                 }
  //               },
  //               icon: Icon(
  //                 Icons.arrow_forward_ios,
  //                 color: Theme.of(context).primaryColor,
  //               )),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  void initializeAnimation([increase = true, changeDirection = false]) {
    if (!changeDirection) {
      questionContentAnimationController = AnimationController(
          vsync: this, duration: Duration(milliseconds: 250))
        ..forward();
    }
    questionAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 525));
    questionSlideAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
            parent: questionAnimationController, curve: Curves.easeInOut));
    questionScaleUpAnimation = Tween<double>(begin: 0.0, end: 0.1).animate(
        CurvedAnimation(
            parent: questionAnimationController,
            curve: Interval(0.0, 0.5, curve: Curves.easeInQuad)));
    questionContentAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
            parent: questionContentAnimationController,
            curve: Curves.easeInQuad));
    questionScaleDownAnimation = Tween<double>(begin: 0.0, end: 0.05).animate(
        CurvedAnimation(
            parent: questionAnimationController,
            curve: Interval(0.5, 1.0, curve: Curves.easeOutQuad)));
    questionSlideDirectionAnimation = Tween<Offset>(
            begin: Offset.zero, end: Offset(increase ? -1.5 : 1.5, 0.0))
        .animate(CurvedAnimation(
            parent: questionAnimationController, curve: Curves.easeInOut));
  }

  void changeQuestion(
      {required bool increaseIndex, required int newQuestionIndex}) {
    questionAnimationController.forward(from: 0.0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation(increaseIndex);
        if (newQuestionIndex != -1) {
          currentQuestionIndex = newQuestionIndex;
        } else {
          if (increaseIndex) {
            currentQuestionIndex++;
          } else {
            currentQuestionIndex--;
          }
        }
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  /// init trước 1 lần mới slide đúng chiều :|
  void changeQuestionDirection({required bool increaseIndex}) {
    setState(() {
      initializeAnimation(increaseIndex, true);
    });
    // questionAnimationController.forward(from: 0.0).then((value) {
    //   //need to dispose the animation controllers
    //   questionAnimationController.dispose();
    //   questionContentAnimationController.dispose();
    //   //initializeAnimation again
    //   setState(() {
    //     initializeAnimation(increaseIndex);
    //   });
    //   //load content(options, image etc) of question
    //   questionContentAnimationController.forward();
    // });
  }

  Widget hasQuestionAttemptedContainer(
      int questionIndex, bool attempted, bool bookmark) {
    return GestureDetector(
      onTap: () {
        if (questionIndex != currentQuestionIndex) {
          changeQuestion(increaseIndex: true, newQuestionIndex: questionIndex);
        }
      },
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
        foregroundDecoration: bookmark
            ? RotatedCornerDecoration.withColor(
                color: Colors.yellow,
                badgeSize: Size(10, 10),
              )
            : null,
        decoration: BoxDecoration(
            color: attempted
                ? Theme.of(context).primaryColor
                : Theme.of(context).colorScheme.secondary),
        height: 30.0,
        width: 30.0,
        child: Text(
          "${questionIndex + 1}",
          style: TextStyle(color: Theme.of(context).colorScheme.surface),
        ),
      ),
    );
  }

  void openBottomSheet(List<Question> questions) {
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      context: context,
      builder: (context) => Container(
          padding: EdgeInsets.symmetric(horizontal: 5.0),
          decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20))),
          constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * (0.6)),
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: 10.0,
                ),
                Container(
                  alignment: Alignment.centerRight,
                  child: IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: Icon(Icons.close),
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
                Wrap(
                  children: List.generate(questions.length, (index) => index)
                      .map((index) => hasQuestionAttemptedContainer(
                          index,
                          questions[index].attempted,
                          questions[index].bookmark))
                      .toList(),
                ),
                SizedBox(
                  height: 20.0,
                ),
                Container(
                  width: MediaQuery.of(context).size.width * 0.25,
                  child: CustomRoundedButton(
                    onTap: () {
                      confirmSubmit();
                    },
                    widthPercentage: MediaQuery.of(context).size.width,
                    backgroundColor: Theme.of(context).primaryColor,
                    buttonTitle: AppLocalization.of(context)!
                        .getTranslatedValues("submitExam")!,
                    radius: 10,
                    showBorder: false,
                    titleColor: Theme.of(context).colorScheme.surface,
                    height: 30.0,
                  ),
                ),
                SizedBox(
                  height: 20.0,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CircleAvatar(
                        backgroundColor: Theme.of(context).primaryColor,
                        radius: 15,
                        child: Center(
                          child: Icon(
                            Icons.check,
                            color: Theme.of(context).colorScheme.surface,
                            size: 22,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.0,
                      ),
                      Text(
                        AppLocalization.of(context)!
                            .getTranslatedValues("attemptedLbl")!,
                        style: TextStyle(
                            fontSize: 12.5,
                            color: Theme.of(context).colorScheme.secondary),
                      ),
                      Spacer(),
                      CircleAvatar(
                        radius: 15,
                        backgroundColor:
                            Theme.of(context).colorScheme.secondary,
                        child: Center(
                          child: Icon(
                            Icons.check,
                            color: Theme.of(context).colorScheme.surface,
                            size: 22,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.0,
                      ),
                      Text(
                        AppLocalization.of(context)!
                            .getTranslatedValues("unAttemptedLbl")!,
                        style: TextStyle(
                            fontSize: 12.5,
                            color: Theme.of(context).colorScheme.secondary),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 15.0,
                ),
              ],
            ),
          )),
    ).then((value) {
      isBottomSheetOpen = false;
    });
  }

  Widget _buildBottomMenu(BuildContext context) {
    return SafeArea(
      child: BlocBuilder<ExamCubit, ExamState>(
        bloc: context.read<ExamCubit>(),
        builder: (context, state) {
          if (state is ExamFetchSuccess) {
            return Padding(
              padding: EdgeInsets.only(top: 5.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Opacity(
                    opacity: currentQuestionIndex != 0 ? 1.0 : 0.5,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.4,
                      child: IconButton(
                          onPressed: () {
                            if (!questionAnimationController.isAnimating) {
                              if (currentQuestionIndex != 0) {
                                changeQuestionDirection(increaseIndex: false);
                                changeQuestion(
                                    increaseIndex: false, newQuestionIndex: -1);
                              }
                            }
                          },
                          icon: Icon(
                            Icons.arrow_back_ios,
                            color: Theme.of(context).colorScheme.secondary,
                          )),
                    ),
                  ),
                  Spacer(),
                  GestureDetector(
                    onTap: () {
                      isBottomSheetOpen = true;
                      openBottomSheet(state.questions);
                    },
                    child: BlocBuilder<ThemeCubit, ThemeState>(
                        bloc: context.read<ThemeCubit>(),
                        builder: (context, state) {
                          print(state.appTheme);
                          return Container(
                            width: MediaQuery.of(context).size.width * 0.2,
                            child: CircleAvatar(
                              backgroundColor:
                                  Theme.of(context).colorScheme.secondary,
                              radius: 20,
                              child: Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: SvgPicture.asset(
                                  UiUtils.getImagePath(
                                    "moveto_icon.svg",
                                  ),
                                  color: state.appTheme.index == 0
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                            ),
                          );
                        }),
                  ),
                  Spacer(),
                  Opacity(
                    opacity: 1.0,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.4,
                      child: IconButton(
                          onPressed: () {
                            if (!questionAnimationController.isAnimating) {
                              if (currentQuestionIndex !=
                                  (state.questions.length - 1)) {
                                changeQuestionDirection(increaseIndex: true);
                                changeQuestion(
                                    increaseIndex: true, newQuestionIndex: -1);
                              } else {
                                isBottomSheetOpen = true;
                                openBottomSheet(state.questions);
                              }
                            }
                          },
                          icon: Icon(
                            Icons.arrow_forward_ios,
                            color: Theme.of(context).colorScheme.secondary,
                          )),
                    ),
                  ),
                ],
              ),
            );
          }
          return SizedBox();
        },
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      child: Stack(
        children: [
          Align(
            alignment: AlignmentDirectional.bottomStart,
            child: Padding(
              padding: EdgeInsetsDirectional.only(start: 20.0, bottom: 15.0),
              child: CustomBackButton(
                removeSnackBars: false,
                iconColor: Theme.of(context).primaryColor,
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.bottomCenter,
            child: Padding(
              padding: EdgeInsetsDirectional.only(bottom: 20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: MediaQuery.of(context).size.width * (0.65),
                    child: Text(
                      "${context.read<ExamCubit>().getExam().quiz_name}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 2.5,
                  ),
                  // Text(
                  //   "${context.read<ExamCubit>().getExam().totalMarks} ${AppLocalization.of(context)!.getTranslatedValues(markKey)!}",
                  //   style: TextStyle(
                  //     color: Theme.of(context).primaryColor,
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.bottomEnd,
            child: Padding(
              padding: EdgeInsetsDirectional.only(end: 20.0, bottom: 24.0),
              child: ExamTimerContainer(
                navigateToResultScreen: navigateToResultScreen,
                examDurationInMinutes:
                    int.parse(context.read<ExamCubit>().getExam().duration),
                key: timerKey,
              ),
            ),
          ),
        ],
      ),
      height:
          MediaQuery.of(context).size.height * (UiUtils.appBarHeightPercentage),
      decoration: BoxDecoration(
          boxShadow: [UiUtils.buildAppbarShadow()],
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.0),
              bottomRight: Radius.circular(20.0))),
    );
  }

  Widget _buildYouLeftTheExam() {
    if (showYouLeftTheExam) {
      return Align(
        alignment: Alignment.center,
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          alignment: Alignment.center,
          color: Theme.of(context).colorScheme.secondary.withOpacity(0.5),
          child: AlertDialog(
            content: Text(
              AppLocalization.of(context)!
                  .getTranslatedValues(youLeftTheExamKey)!,
              style: TextStyle(color: Theme.of(context).colorScheme.secondary),
            ),
            actions: [
              TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    AppLocalization.of(context)!.getTranslatedValues(okayLbl)!,
                    style: TextStyle(color: Theme.of(context).primaryColor),
                  ))
            ],
          ),
        ),
      );
    }
    return Container();
  }

  Widget _buildQuestions() {
    return BlocBuilder<ExamCubit, ExamState>(
      bloc: context.read<ExamCubit>(),
      builder: (context, state) {
        if (state is ExamFetchSuccess) {
          return PageView.builder(
            onPageChanged: (index) {
              currentQuestionIndex = index;
              setState(() {});
            },
            controller: pageController,
            itemCount: state.questions.length,
            itemBuilder: (context, index) {
              return SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        QuestionExamContainer(
                            isMathQuestion: false,
                            questionColor:
                                Theme.of(context).colorScheme.secondary,
                            questionNumber: index + 1,
                            question: state.questions[index]),
                        GestureDetector(
                          onTap: () =>
                              submitBookmark(state.questions[index].bookmark),
                          child: Padding(
                            padding: EdgeInsets.only(left: 10),
                            child: Icon(state.questions[index].bookmark
                                ? Icons.bookmark
                                : Icons.bookmark_outline),
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 25,
                    ),
                    ...state.questions[index].answerOptions!
                        .map((option) => OptionContainer(
                            quizType: QuizTypes.exam,
                            showAnswerCorrectness: false,
                            showAudiencePoll: false,
                            hasSubmittedAnswerForCurrentQuestion:
                                hasSubmittedAnswerForCurrentQuestion,
                            constraints: BoxConstraints(
                              maxWidth:
                                  MediaQuery.of(context).size.width * (0.85),
                              maxHeight: MediaQuery.of(context).size.height *
                                  UiUtils.questionContainerHeightPercentage,
                            ),
                            answerOption: option,
                            correctOptionId: "",
                            // AnswerEncryption.decryptCorrectAnswer(
                            //     rawKey: context
                            //         .read<UserDetailsCubit>()
                            //         .getUserFirebaseId(),
                            //     correctAnswer:
                            //         state.questions[index].correctAnswer),
                            submitAnswer: submitAnswer,
                            submittedAnswerId:
                                state.questions[index].submittedAnswerId))
                        .toList(),
                  ],
                ),
                padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height *
                            (UiUtils.appBarHeightPercentage) +
                        25,
                    bottom: 80),
              );
            },
          );
        }
        return Container();
      },
    );
  }

  Future openDialog(String quid, String title, String qid) => showDialog(
      context: context,
      builder: (context) {
        return ReportQuestionPopup(
          question: title,
          questionId: qid,
          quizId: quid,
        );
      });
  Widget _buildTopMenu(state) {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        margin: EdgeInsets.only(
            right: MediaQuery.of(context).size.width *
                ((1.0 - UiUtils.quesitonContainerWidthPercentage) * 0.5),
            left: MediaQuery.of(context).size.width *
                ((1.0 - UiUtils.quesitonContainerWidthPercentage) * 0.5),
            top: MediaQuery.of(context).padding.top),
        child: Row(
          children: [
            CustomBackButton(
              onTap: () {
                onTapBackButton();
              },
              iconColor: Theme.of(context).colorScheme.surface,
            ),
            Spacer(),
            GestureDetector(
              onTap: () {
                openDialog(
                    widget.exam.quid,
                    state.questions[currentQuestionIndex].question,
                    state.questions[currentQuestionIndex].id);
              },
              child: Padding(
                padding: EdgeInsets.only(right: 0),
                child: Icon(
                  Icons.chat,
                  size: 20,
                  color: Theme.of(context).colorScheme.surface,
                ),
              ),
            ),
            if (state is ExamFetchSuccess)
              GestureDetector(
                onTap: () => submitBookmark(
                    state.questions[currentQuestionIndex].bookmark),
                child: Padding(
                  padding: EdgeInsets.only(left: 8),
                  child: Icon(
                    state.questions[currentQuestionIndex].bookmark
                        ? Icons.bookmark
                        : Icons.bookmark_outline,
                    color: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
            SettingButton(onPressed: () {
              toggleSettingDialog();
              showDialog(
                  context: context,
                  builder: (_) => SettingsDialogContainer()).then((value) {
                toggleSettingDialog();
              });
            }),
          ],
        ),
      ),
    );
  }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  void onTapBackButton() {
    isExitDialogOpen = true;
    showDialog(context: context, builder: (context) => ExitGameDailog())
        .then((value) => isExitDialogOpen = false);
  }

  void confirmSubmit() {
    showDialog(
        context: context,
        builder: (context) => ConfirmSubmitDailog(
              onTapYes: () => navigateToResultScreen(),
            ));
  }

  @override
  Widget build(BuildContext context) {
    final quesCubit = context.read<ExamCubit>();
    return WillPopScope(
      onWillPop: () {
        onTapBackButton();
        return Future.value(false);
      },
      child: Scaffold(
        body: Stack(
          children: [
            PageBackgroundGradientContainer(),
            Align(
              alignment: Alignment.topCenter,
              child: QuizPlayAreaBackgroundContainer(
                heightPercentage: 0.9,
              ),
            ),
            BlocConsumer<ExamCubit, ExamState>(
                bloc: quesCubit,
                listener: (context, state) {
                  if (state is ExamFetchSuccess) {
                    if (!timerAnimationController.isAnimating) {
                      timerAnimationController.forward();
                    }
                  }
                },
                builder: (context, state) {
                  if (state is ExamFetchInProgress) {
                    return Center(
                      child: CircularProgressContainer(
                        useWhiteLoader: true,
                      ),
                    );
                  } else if (state is ExamFetchFailure) {
                    return Center(
                      child: ErrorContainer(
                        showBackButton: true,
                        errorMessageColor:
                            Theme.of(context).scaffoldBackgroundColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues(convertErrorCodeToLanguageKey(
                                state.errorMessage)),
                        onTapRetry: () {
                          _getQuestions();
                        },
                        showErrorImage: true,
                      ),
                    );
                  }
                  if (state is ExamFetchSuccess) {
                    final questions = (state as ExamFetchSuccess).questions;
                    ques = questions;
                    return GestureDetector(
                      onPanUpdate: (details) {
                        // Swiping in right direction.
                        if (details.delta.dx > 5) {
                          if (!questionAnimationController.isAnimating) {
                            if (currentQuestionIndex != 0) {
                              changeQuestionDirection(increaseIndex: false);
                              changeQuestion(
                                  increaseIndex: false, newQuestionIndex: -1);
                            }
                          }
                        }

                        // Swiping in left direction.
                        if (details.delta.dx < -5) {
                          if (state is ExamFetchSuccess) {
                            if (!questionAnimationController.isAnimating) {
                              if (currentQuestionIndex !=
                                  (state.questions.length - 1)) {
                                changeQuestionDirection(increaseIndex: true);
                                changeQuestion(
                                    increaseIndex: true, newQuestionIndex: -1);
                              } else {
                                isBottomSheetOpen = true;
                                openBottomSheet(state.questions);
                              }
                            }
                          }
                        }
                      },
                      child: Align(
                          alignment: Alignment.topCenter,
                          child: QuestionsContainer(
                            questionSlideDirectionAnimation:
                                questionSlideDirectionAnimation,
                            timerAnimationController: timerAnimationController,
                            quizType: QuizTypes.selfChallenge,
                            showAnswerCorrectness: false,
                            lifeLines: {},
                            topPadding: MediaQuery.of(context).size.height *
                                UiUtils
                                    .getQuestionContainerTopPaddingPercentage(
                                        MediaQuery.of(context).size.height),
                            hasSubmittedAnswerForCurrentQuestion:
                                hasSubmittedAnswerForCurrentQuestion,
                            questions: questions,
                            submitAnswer: submitAnswer,
                            questionContentAnimation: questionContentAnimation,
                            questionScaleDownAnimation:
                                questionScaleDownAnimation,
                            questionScaleUpAnimation: questionScaleUpAnimation,
                            questionSlideAnimation: questionSlideAnimation,
                            currentQuestionIndex: currentQuestionIndex,
                            questionAnimationController:
                                questionAnimationController,
                            questionContentAnimationController:
                                questionContentAnimationController,
                            guessTheWordQuestions: [],
                            guessTheWordQuestionContainerKeys: [],
                            timerKey: timerKey,
                            quizTimer: widget.quizDuration,
                            // timerNavigateResult: () =>
                            //     navigateToResultScreenTimeOut(),
                            // quizType: QuizTypes.selfChallenge,
                          )),
                    );
                  }
                  return SizedBox();
                }),
            BlocBuilder<ExamCubit, ExamState>(
              bloc: quesCubit,
              builder: (context, state) {
                if (state is ExamFetchSuccess) {
                  return Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                        height: MediaQuery.of(context).size.height * 0.15,
                        child: _buildBottomMenu(context)),
                  );
                }
                return SizedBox();
              },
            ),
            BlocBuilder<ExamCubit, ExamState>(
              bloc: quesCubit,
              builder: (context, state) {
                return _buildTopMenu(state);
              },
            ),
            if (_isLoading)
              const Opacity(
                opacity: 0.8,
                child: ModalBarrier(dismissible: false, color: Colors.black),
              ),
            if (_isLoading)
              Center(
                child: CircularProgressContainer(
                  useWhiteLoader: false,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Widget build(BuildContext context) {
  //   return WillPopScope(
  //     onWillPop: () {
  //       if (showYouLeftTheExam) {
  //         return Future.value(true);
  //       }
  //       isExitDialogOpen = true;
  //       showDialog(
  //           context: context,
  //           builder: (context) => ExitGameDailog(
  //                 onTapYes: () {
  //                   //
  //                   submitResult();
  //                   Navigator.of(context).pop();
  //                   Navigator.of(context).pop();
  //                   //submit result of exam
  //                 },
  //               )).then((value) {
  //         isExitDialogOpen = false;
  //       });
  //       return Future.value(false);
  //     },
  //     child: Scaffold(
  //       body: Stack(
  //         children: [
  //           PageBackgroundGradientContainer(),
  //           _buildQuestions(),
  //           _buildAppBar(),
  //           Align(
  //             alignment: Alignment.bottomCenter,
  //             child: _buildBottomMenu(),
  //           ),
  //           _buildYouLeftTheExam(),
  //           isScreenRecordingInIos
  //               ? Container(
  //                   color: Colors.black,
  //                   width: MediaQuery.of(context).size.width,
  //                   height: MediaQuery.of(context).size.height,
  //                 )
  //               : Container(),
  //         ],
  //       ),
  //     ),
  //   );
  // }
}
