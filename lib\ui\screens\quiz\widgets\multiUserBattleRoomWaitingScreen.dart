import 'package:flutter/material.dart';

class MultiUserBattleRoomWaitingScreen extends StatefulWidget {
  MultiUserBattleRoomWaitingScreen({Key? key}) : super(key: key);

  @override
  _MultiUserBattleRoomWaitingScreenState createState() => _MultiUserBattleRoomWaitingScreenState();
}

class _MultiUserBattleRoomWaitingScreenState extends State<MultiUserBattleRoomWaitingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold();
  }
}
