import 'package:flutter/material.dart';

import '../../styles/payment/app_styles.dart';

class SuccessDialog extends StatelessWidget {
  final String text;
  const SuccessDialog({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(16),
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.only(top: 30),
              child: Container(
                width: 196,
                height: 139,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage("assets/images/dialog_success.png"),
                  fit: BoxFit.fill,
                )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Container(
                child: Text(
                  text,
                  textAlign: TextAlign.center,
                  style: AppStyles.dialogText,
                ),
              ),
            )
          ]),
        ),
      ),
    );
  }
}
