import 'dart:convert';

import 'package:flutterquiz/ui/screens/model/question_api_model.dart';

class Result2 {
  final String name, status, time, percentage;
  final List<Question> questions;
  final List<dynamic> answers;
  final List<dynamic> answersText;
  final int passPercent;
  final List<dynamic> bookmark;
  final String quizId;
  final String? quizType;
  final int quizDuration;
  final int timeDoQuiz;
  Result2(
      {required this.status,
      required this.time,
      this.quizType = "1",
      required this.percentage,
      required this.name,
      required this.questions,
      required this.answers,
      required this.answersText,
      required this.passPercent,
      required this.bookmark,
      required this.quizId,
      required this.quizDuration,
      required this.timeDoQuiz});
  @override
  String toString() {
    return "Question(name: $name, status: $status, time: $time, percentage: $percentage, questions: $questions, answers: $answers, answersText: $answersText, quizType: $answersText)";
  }

  factory Result2.fromJson(Map<String, dynamic> jsonData) {
    return Result2(
        status: jsonData['status'],
        time: jsonData['time'],
        percentage: jsonData['percentage'],
        name: json<PERSON><PERSON>['name'],
        quizType: jsonData['quizType'],
        questions: (jsonData['questions'] as List)
            .map((e) => Question.fromJson(e))
            .toList(),
        answers: (List<dynamic>.from(jsonData['answers'] ?? [])),
        answersText: (List<dynamic>.from(jsonData['answersText'] ?? [])),
        bookmark: (List<dynamic>.from(jsonData['bookmark'] ?? [])),
        passPercent: jsonData['passPercent'],
        quizId: jsonData['quizId'],
        quizDuration: jsonData['quizDuration'],
        timeDoQuiz: jsonData['timeDoQuiz']);
  }

  static Map<String, dynamic> toMap(Result2 music) => {
        'name': music.name,
        'percentage': music.percentage,
        'status': music.status,
        'time': music.time,
        'quizType': music.quizType,
        'questions': music.questions,
        'answers': music.answers,
        'answersText': music.answersText,
        'bookmark': music.bookmark,
        'passPercent': music.passPercent,
        'quizId': music.quizId,
        'quizDuration': music.quizDuration,
        'timeDoQuiz': music.timeDoQuiz
      };

  static String encode(List<Result2> musics) => json.encode(
        musics
            .map<Map<String, dynamic>>((music) => Result2.toMap(music))
            .toList(),
      );

  static List<Result2> decode(String musics) =>
      (json.decode(musics) as List<dynamic>)
          .map<Result2>((item) => Result2.fromJson(item))
          .toList();
}
