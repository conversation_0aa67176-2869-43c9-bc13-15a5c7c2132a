// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_spinkit/flutter_spinkit.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:flutterquiz/app/appLocalization.dart';
// import 'package:flutterquiz/app/routes.dart';
// import 'package:flutterquiz/features/inAppPurchase/inAppPurchaseCubit.dart';
// import 'package:flutterquiz/ui/screens/home/<USER>';
// import 'package:internet_connection_checker/internet_connection_checker.dart';
// import 'package:intl/intl.dart';
// import 'package:loader_overlay/loader_overlay.dart';
// import 'package:package_info_plus/package_info_plus.dart';
// import 'package:flutterquiz/ui/styles/payment/app_colors.dart';
// import 'package:flutterquiz/ui/styles/payment/app_styles.dart';
// import 'package:flutterquiz/globals.dart';
// import 'package:flutterquiz/ui/widgets/payment/error_dialog.dart';
// import 'package:flutterquiz/ui/widgets/payment/purchase_success.dart';
// import 'package:flutterquiz/ui/widgets/payment/redeem_code.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:http/http.dart' as http;
// import 'package:url_launcher/url_launcher.dart';

// class PaymentPage extends StatefulWidget {
//   @override
//   State<PaymentPage> createState() => _PaymentPageState();
// }

// String url = getUserInfor().url;

// class _PaymentPageState extends State<PaymentPage> {
//   int _currentIndex = 1;
//   bool first_time = true;
//   late Package currentPackage;
//   late Future getOffers;
//   bool premium = Common.premium;
//   String premiumType = Common.premiumType;
//   bool available = false;
//   bool package_available = false;
//   String device = 'phone';
//   String getDeviceType() {
//     final data = MediaQueryData.fromWindow(WidgetsBinding.instance!.window);
//     return data.size.shortestSide < 550 ? 'phone' : 'tablet';
//   }

//   @override
//   void initState() {
//     getOffers = fetchOffer();
//     //currentPackage = getOffers[0];
//     super.initState();
//     CheckUserConnection();
//     device = getDeviceType();
//     //inspect(getOffers);
//   }

//   Future CheckUserConnection() async {
//     bool result = await InternetConnectionChecker().hasConnection;
//     if (result == true) {
//       setState(() {
//         available = true;
//       });
//     } else {
//       available = false;
//     }
//   }

//   Future error() =>
//       showDialog(context: context, builder: (context) => ErrorDialog());
//   Future success(String text) => showDialog(
//       barrierColor: Color(0xff000000).withOpacity(0.5),
//       barrierDismissible: true,
//       barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
//       context: context,
//       builder: (context) => PurchaseSuccessDialog(
//             text: text,
//           ));
//   Future redeemCode() => showDialog(
//       barrierColor: Color(0xff000000).withOpacity(0.5),
//       barrierDismissible: true,
//       barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
//       context: context,
//       builder: (context) => const RedeemCodeDialog());
//   @override
//   Future fetchOffer() async {
//     final offerings = await PurchaseApi.fetchOffer();
//     if (offerings.isEmpty) {
//       return null;
//     } else {
//       final packages = offerings
//           .map((offer) => offer.availablePackages)
//           .expand((pair) => pair)
//           .toList();
//       currentPackage = packages[1];
//       setState(() {
//         package_available = true;
//       });
//       return packages;
//     }
//   }

//   List paymentDuration = [
//     PaymentCard(price: 89, duration: 1),
//     PaymentCard(price: 199, duration: 3),
//     PaymentCard(price: 499, duration: 6),
//     PaymentCard(price: 499, duration: 12),
//   ];
//   _goBack(BuildContext context) {
//     Navigator.of(context)
//         .pushNamedAndRemoveUntil(Routes.home, (Route<dynamic> route) => false);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return LoaderOverlay(
//       useDefaultLoading: false,
//       overlayOpacity: 0.8,
//       overlayWidget: Center(
//         child: SpinKitRing(
//           color: Colors.white,
//           size: 50.0,
//         ),
//       ),
//       overlayColor: Colors.black,
//       child: AnnotatedRegion<SystemUiOverlayStyle>(
//         value: const SystemUiOverlayStyle(
//             statusBarColor: Colors.transparent,
//             statusBarBrightness: Brightness.light,
//             statusBarIconBrightness: Brightness.dark),
//         child: WillPopScope(
//           onWillPop: () {
//             _goBack(context);
//             return Future.value(false);
//           },
//           child: Scaffold(
//             resizeToAvoidBottomInset: false,
//             appBar: AppBar(
//               centerTitle: false,
//               /* iconTheme: IconThemeData(
//                 color: Colors.black, //change your color here
//               ), */
//               automaticallyImplyLeading: false,
//               actions: [
//                 IconButton(
//                   icon: Icon(Icons.close,
//                       color: Theme.of(context).colorScheme.secondary),
//                   onPressed: () => _goBack(context),
//                 ),
//               ],
//               //centerTitle: true,
//               backgroundColor: Colors.transparent,
//               title: Text(
//                 AppLocalization.of(context)!
//                     .getTranslatedValues("accessPremium")!,
//                 style: AppStyles.titleBold.copyWith(
//                   color: Theme.of(context).colorScheme.secondary,
//                   fontSize: 20,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               elevation: 0,
//             ),
//             body: Column(children: <Widget>[
//               Padding(
//                 padding:
//                     const EdgeInsets.only(left: 10.0, right: 10, bottom: 0),
//                 child: Container(
//                   alignment: Alignment.center,
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: [
//                       Container(
//                         width: MediaQuery.of(context).size.width,
//                         height: device == 'phone'
//                             ? MediaQuery.of(context).size.height * 0.25
//                             : MediaQuery.of(context).size.height * 0.4,
//                         child: SvgPicture.asset(
//                           'assets/images/PaymentIcon.svg',
//                           fit: device == 'phone'
//                               ? BoxFit.contain
//                               : BoxFit.fitHeight,
//                           //width: MediaQuery.of(context).size.width * 0.6,
//                         ),
//                       ),
//                       Padding(
//                         padding: EdgeInsets.only(
//                             top: 5, right: 10, bottom: 5, left: 10),
//                         child: Row(
//                           children: [
//                             Padding(
//                               padding: EdgeInsets.only(right: 10),
//                               child: SvgPicture.asset(
//                                 'assets/images/Checked.svg',
//                                 fit: BoxFit.cover,
//                                 //width: MediaQuery.of(context).size.width * 0.6,
//                               ),
//                             ),
//                             Flexible(
//                               child: RichText(
//                                 text: TextSpan(
//                                     style: AppStyles.body.copyWith(
//                                         color: Theme.of(context)
//                                             .colorScheme
//                                             .secondary,
//                                         fontSize: 16),
//                                     children: <TextSpan>[
//                                       TextSpan(
//                                         text: AppLocalization.of(context)!
//                                                 .getTranslatedValues(
//                                                     "unlock")! +
//                                             " ",
//                                       ),
//                                       TextSpan(
//                                           text: AppLocalization.of(context)!
//                                               .getTranslatedValues(
//                                                   "allTheExams")!,
//                                           style: AppStyles.bodyBold.copyWith(
//                                               color: Theme.of(context)
//                                                   .colorScheme
//                                                   .secondary,
//                                               fontSize: 16)),
//                                     ]),
//                               ),
//                             )
//                           ],
//                         ),
//                       ),
//                       Padding(
//                         padding: EdgeInsets.only(
//                             top: 5, right: 10, bottom: 5, left: 10),
//                         child: Row(
//                           children: [
//                             Padding(
//                               padding: EdgeInsets.only(right: 10),
//                               child: SvgPicture.asset(
//                                 'assets/images/Checked.svg',
//                                 fit: BoxFit.cover,
//                                 //width: MediaQuery.of(context).size.width * 0.6,
//                               ),
//                             ),
//                             Flexible(
//                               child: RichText(
//                                 text: TextSpan(
//                                     style: AppStyles.body.copyWith(
//                                         color: Theme.of(context)
//                                             .colorScheme
//                                             .secondary,
//                                         fontSize: 16),
//                                     children: <TextSpan>[
//                                       TextSpan(
//                                         text: AppLocalization.of(context)!
//                                                 .getTranslatedValues(
//                                                     "answersWith")! +
//                                             " ",
//                                       ),
//                                       TextSpan(
//                                           text: AppLocalization.of(context)!
//                                               .getTranslatedValues(
//                                                   "detailedExplain")!,
//                                           style: AppStyles.bodyBold.copyWith(
//                                               color: Theme.of(context)
//                                                   .colorScheme
//                                                   .secondary,
//                                               fontSize: 16)),
//                                     ]),
//                               ),
//                             )
//                           ],
//                         ),
//                       ),
//                       Padding(
//                         padding: const EdgeInsets.only(bottom: 10.0),
//                         child: Row(
//                           children: [
//                             Padding(
//                               padding: EdgeInsets.only(
//                                   top: 5, right: 10, bottom: 5, left: 10),
//                               child: SvgPicture.asset(
//                                 'assets/images/Checked.svg',
//                                 fit: BoxFit.cover,
//                                 //width: MediaQuery.of(context).size.width * 0.6,
//                               ),
//                             ),
//                             Flexible(
//                               child: RichText(
//                                 text: TextSpan(
//                                     style: AppStyles.body.copyWith(
//                                         color: Theme.of(context)
//                                             .colorScheme
//                                             .secondary,
//                                         fontSize: 16),
//                                     children: <TextSpan>[
//                                       TextSpan(
//                                         text: AppLocalization.of(context)!
//                                                 .getTranslatedValues("fully")! +
//                                             " ",
//                                       ),
//                                       TextSpan(
//                                           text: AppLocalization.of(context)!
//                                               .getTranslatedValues(
//                                                   "updateYear")!
//                                               .replaceAll(
//                                                   '%1',
//                                                   DateTime.now()
//                                                       .year
//                                                       .toString()),
//                                           style: AppStyles.bodyBold.copyWith(
//                                               color: Theme.of(context)
//                                                   .colorScheme
//                                                   .secondary,
//                                               fontSize: 16)),
//                                     ]),
//                               ),
//                             )
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//               Expanded(
//                 child: Padding(
//                   padding:
//                       const EdgeInsets.only(left: 10.0, right: 10, bottom: 10),
//                   child: FutureBuilder(
//                     future: getOffers,
//                     builder: (BuildContext context, AsyncSnapshot snapshot) {
//                       if (snapshot.hasData) {
//                         return ListView.builder(
//                             shrinkWrap: true,
//                             itemCount: snapshot.data.length + 1,
//                             itemBuilder: (BuildContext context, int index) {
//                               first_time == true
//                                   ? {
//                                       //currentPackage = snapshot.data[index],
//                                       first_time = false
//                                     }
//                                   : null;
//                               return index == snapshot.data.length
//                                   ? Container(
//                                       margin: const EdgeInsets.fromLTRB(
//                                           10, 10, 10, 0),
//                                       child: Text(
//                                         AppLocalization.of(context)!
//                                             .getTranslatedValues(
//                                                 "trialDescription")!,
//                                         textAlign: TextAlign.left,
//                                         style: AppStyles.body.copyWith(
//                                             color: AppColors.blackText,
//                                             fontSize: 12,
//                                             height: 1.5,
//                                             fontWeight: FontWeight.w500),
//                                       ),
//                                     )
//                                   : InkWell(
//                                       onTap: () async {},
//                                       child: index != _currentIndex
//                                           ? GestureDetector(
//                                               onTap: () {
//                                                 setState(() {
//                                                   _currentIndex = index;
//                                                   currentPackage =
//                                                       snapshot.data[index];
//                                                 });
//                                               },
//                                               child: PaymentOptions(
//                                                 duration: paymentDuration[index]
//                                                     .duration,
//                                                 price: snapshot.data[index]
//                                                     .storeProduct.price,
//                                                 currrency: snapshot.data[index]
//                                                     .storeProduct.currencyCode
//                                                     .toString(),
//                                               ),
//                                             )
//                                           : PaymentOptionsSelected(
//                                               duration: paymentDuration[index]
//                                                   .duration,
//                                               price: snapshot.data[index]
//                                                   .storeProduct.price,
//                                               currrency: snapshot.data[index]
//                                                   .storeProduct.currencyCode
//                                                   .toString(),
//                                             ),
//                                     );
//                             });
//                       } else {
//                         return Center(
//                           child: SizedBox(
//                             height: 50,
//                             width: 50,
//                             child: CircularProgressIndicator(
//                               strokeWidth: 3,
//                             ),
//                           ),
//                         );
//                       }
//                     },
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(left: 20.0, right: 20),
//                 child: Container(
//                   child: Padding(
//                     padding: const EdgeInsets.only(bottom: 20),
//                     child: Column(
//                       children: [
//                         Padding(
//                           padding: const EdgeInsets.only(bottom: 10),
//                           child: Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               IntrinsicHeight(
//                                 child: Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     GestureDetector(
//                                       onTap: () {
//                                         launch(
//                                             "https://scrumpass.com/terms-of-service/");
//                                       },
//                                       child: Container(
//                                         child: Text(
//                                           AppLocalization.of(context)!
//                                               .getTranslatedValues("term")!,
//                                           textAlign: TextAlign.center,
//                                           style: AppStyles.bodyBold.copyWith(
//                                               color: available
//                                                   ? Color(0xff7A8694)
//                                                   : Color(0xff7A8694)
//                                                       .withOpacity(0.4),
//                                               fontSize: 14),
//                                         ),
//                                       ),
//                                     ),
//                                     const VerticalDivider(
//                                       color: Color(0xff7A8694),
//                                       thickness: 2,
//                                       width: 10,
//                                     ),
//                                     GestureDetector(
//                                       onTap: () {
//                                         launch(
//                                             "https://scrumpass.com/privacy/");
//                                       },
//                                       child: Container(
//                                         child: Text(
//                                           AppLocalization.of(context)!
//                                               .getTranslatedValues(
//                                                   "privacyPolicy")!,
//                                           textAlign: TextAlign.center,
//                                           style: AppStyles.bodyBold.copyWith(
//                                               color: available
//                                                   ? Color(0xff7A8694)
//                                                   : Color(0xff7A8694)
//                                                       .withOpacity(0.4),
//                                               fontSize: 14),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                               // GestureDetector(
//                               //   onTap: () async {
//                               //     if (available && package_available) {
//                               //       redeemCode();
//                               //     }
//                               //   },
//                               //   child: Container(
//                               //     margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
//                               //     child: Text(
//                               //       AppLocalization.of(context)!
//                               //           .getTranslatedValues("redeemCode")!,
//                               //       textAlign: TextAlign.center,
//                               //       style: AppStyles.bodyBold.copyWith(
//                               //           color: available
//                               //               ? const Color(0xff7A8694)
//                               //               : const Color(0xff7A8694)
//                               //                   .withOpacity(0.4),
//                               //           fontSize: 14),
//                               //     ),
//                               //   ),
//                               // ),
//                               const SizedBox()
//                             ],
//                           ),
//                         ),
//                         ElevatedButton(
//                             style: ElevatedButton.styleFrom(
//                               minimumSize: Size.fromHeight(40),
//                               primary: available && package_available
//                                   ? Theme.of(context).primaryColor
//                                   : Theme.of(context)
//                                       .primaryColor
//                                       .withOpacity(0.2),
//                             ),
//                             child: Padding(
//                               padding: const EdgeInsets.all(16.0),
//                               child: Text(
//                                 AppLocalization.of(context)!
//                                     .getTranslatedValues("startTrial")!,
//                                 textAlign: TextAlign.center,
//                                 style: AppStyles.bodyBold.copyWith(
//                                     color: AppColors.white, fontSize: 18),
//                               ),
//                             ),
//                             onPressed: () async {
//                               if (available && package_available) {
//                                 context.loaderOverlay.show();
//                                 var purchase =
//                                     await PurchaseApi.purchasePackage(
//                                         currentPackage);
//                                 if (purchase == 'success') {
//                                   /* showDialog<String>(
//                                       context: context,
//                                       builder: (BuildContext context) => AlertDialog(
//                                             content: Text('Subcribe Success'),
//                                           )); */
//                                   setState(() {
//                                     premium = Common.premium;
//                                     premiumType = Common.premiumType;
//                                     context.loaderOverlay.hide();
//                                   });
//                                   context.loaderOverlay.hide();
//                                   success(AppLocalization.of(context)!
//                                       .getTranslatedValues(
//                                           "thankForPurchase")!);

//                                   /* Navigator.push(context,
//                                       MaterialPageRoute(builder: (context) => Home())); */
//                                 } else if (purchase == 'canceled') {
//                                   context.loaderOverlay.hide();
//                                 } else {
//                                   error();
//                                   context.loaderOverlay.hide();
//                                 }
//                               }
//                             }),
//                         Container(
//                           margin: const EdgeInsets.fromLTRB(0, 10, 10, 0),
//                           child: Text(
//                             AppLocalization.of(context)!
//                                 .getTranslatedValues("cancelAnyTime")!,
//                             textAlign: TextAlign.center,
//                             style: AppStyles.body.copyWith(
//                                 color: Theme.of(context).colorScheme.secondary,
//                                 fontSize: 12,
//                                 fontWeight: FontWeight.w600),
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () async {
//                             if (available && package_available) {
//                               print("run restore");
//                               context.loaderOverlay.show();
//                               bool restore =
//                                   await PurchaseApi.restorePurchase();
//                               if (restore) {
//                                 /* showDialog<String>(
//                                         context: context,
//                                         builder: (BuildContext context) => AlertDialog(
//                                               content: Text('Restore premium success'),
//                                             )); */
//                                 setState(() {
//                                   premium = Common.premium;
//                                   premiumType = Common.premiumType;
//                                 });
//                                 success(AppLocalization.of(context)!
//                                     .getTranslatedValues("restorePurchase")!);
//                                 /* Navigator.push(context,
//                                         MaterialPageRoute(builder: (context) => Home())); */
//                                 context.loaderOverlay.hide();
//                               } else {
//                                 //ErrorDialog();
//                                 error();
//                                 context.loaderOverlay.hide();
//                               }
//                               context.loaderOverlay.hide();
//                             }
//                           },
//                           child: Container(
//                             margin: const EdgeInsets.fromLTRB(0, 10, 10, 10),
//                             child: Text(
//                               AppLocalization.of(context)!
//                                   .getTranslatedValues("alreadyPaid")!,
//                               textAlign: TextAlign.center,
//                               style: AppStyles.bodyBold.copyWith(
//                                   color: available && package_available
//                                       ? Theme.of(context).primaryColor
//                                       : Theme.of(context)
//                                           .primaryColor
//                                           .withOpacity(0.4),
//                                   fontSize: 14),
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ]),
//           ),
//         ),
//       ),
//     );
//   }
// }

// class PaymentCard {
//   double price;
//   int duration;
//   PaymentCard({required this.price, required this.duration});
// }

// var formatter = NumberFormat(',000');

// class PaymentOptions extends StatelessWidget {
//   double price;
//   int duration;
//   String currrency;

//   PaymentOptions(
//       {required this.price, required this.duration, required this.currrency});
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(left: 10.0, right: 10, top: 5, bottom: 5),
//       child: Container(
//           //height: 70,
//           width: MediaQuery.of(context).size.width,
//           decoration: BoxDecoration(
//             border: Border.all(color: Color(0xffC8C8C8)),
//             borderRadius: BorderRadius.circular(10),
//           ),
//           child: Padding(
//             padding: const EdgeInsets.fromLTRB(16, 12.0, 16, 12),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Flexible(
//                   child: RichText(
//                     text: TextSpan(
//                         style: AppStyles.body.copyWith(
//                             color: Theme.of(context).colorScheme.secondary,
//                             fontSize: 16),
//                         children: <TextSpan>[
//                           TextSpan(
//                               text: price > 1000
//                                   ? formatter.format(price).toString() +
//                                       " " +
//                                       currrency
//                                   : price.toStringAsFixed(2) + " " + currrency,
//                               style: AppStyles.bodyBold.copyWith(
//                                   color:
//                                       Theme.of(context).colorScheme.secondary,
//                                   fontSize: 16)),
//                           TextSpan(
//                               text: duration > 1
//                                   ? "/" +
//                                       duration.toString() +
//                                       " " +
//                                       AppLocalization.of(context)!
//                                           .getTranslatedValues("months")!
//                                   : "/" +
//                                       duration.toString() +
//                                       " " +
//                                       AppLocalization.of(context)!
//                                           .getTranslatedValues("month")!,
//                               style: AppStyles.body.copyWith(
//                                 color: Theme.of(context).colorScheme.secondary,
//                                 fontSize: 16,
//                               )),
//                         ]),
//                   ),
//                 ),
//                 Container(
//                   height: 25,
//                   child: SvgPicture.asset(
//                     'assets/images/Uncheck_Icon.svg',
//                     fit: BoxFit.contain,
//                     //width: MediaQuery.of(context).size.width * 0.6,
//                   ),
//                 ),
//               ],
//             ),
//           ) /* Column(
//           children: [
//             Padding(
//               padding: const EdgeInsets.only(
//                   left: 10.0, bottom: 5, top: 10, right: 10),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     duration > 1
//                         ? duration.toString() +
//                             " " +
//                             AppLocalizations.of(context).months
//                         : duration.toString() +
//                             " " +
//                             AppLocalizations.of(context).month,
//                     style: AppStyles.bodyBold
//                         .copyWith(fontSize: 18, color: Colors.black),
//                   ),
//                   Text(
//                     price > 1000
//                         ? formatter.format(price).toString() + " " + currrency
//                         : price.toStringAsFixed(2) + " " + currrency,
//                     style: AppStyles.bodyBold
//                         .copyWith(fontSize: 18, color: Colors.black),
//                   ),
//                 ],
//               ),
//             ),
//             Padding(
//               padding: const EdgeInsets.only(
//                   left: 10.0, bottom: 10, top: 5, right: 10),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     price > 1000
//                         ? formatter
//                                 .format(
//                                     ((price / duration) / 1000).round() * 1000)
//                                 .toString() +
//                             " " +
//                             currrency +
//                             " " +
//                             AppLocalizations.of(context).monthly
//                         : (price / duration).toStringAsFixed(2) +
//                             " " +
//                             currrency +
//                             " " +
//                             AppLocalizations.of(context).monthly,
//                     style: AppStyles.body
//                         .copyWith(fontSize: 16, color: Colors.black),
//                   ),
//                   Text(AppLocalizations.of(context).total,
//                       style: AppStyles.bodyBold
//                           .copyWith(fontSize: 16, color: Colors.black)),
//                 ],
//               ),
//             ),
//           ],
//         ), */
//           ),
//     );
//   }
// }

// class PaymentOptionsSelected extends StatelessWidget {
//   double price;
//   int duration;
//   String currrency;

//   PaymentOptionsSelected(
//       {required this.price, required this.duration, required this.currrency});
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(left: 10.0, right: 10, top: 5, bottom: 5),
//       child: Container(
//         //height: 70,
//         width: MediaQuery.of(context).size.width,
//         decoration: BoxDecoration(
//           color: Theme.of(context).primaryColor.withOpacity(0.2),
//           border: Border.all(color: Theme.of(context).primaryColor, width: 3),
//           borderRadius: BorderRadius.circular(10),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.fromLTRB(16, 12.0, 16, 12),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Flexible(
//                 child: RichText(
//                   text: TextSpan(
//                       style: AppStyles.body
//                           .copyWith(color: Colors.black, fontSize: 16),
//                       children: <TextSpan>[
//                         TextSpan(
//                             text: price > 1000
//                                 ? formatter.format(price).toString() +
//                                     " " +
//                                     currrency
//                                 : price.toStringAsFixed(2) + " " + currrency,
//                             style: AppStyles.bodyBold.copyWith(
//                                 color: Theme.of(context).primaryColor,
//                                 fontSize: 16)),
//                         TextSpan(
//                             text: duration > 1
//                                 ? "/" +
//                                     duration.toString() +
//                                     " " +
//                                     AppLocalization.of(context)!
//                                         .getTranslatedValues("months")!
//                                 : "/" +
//                                     duration.toString() +
//                                     " " +
//                                     AppLocalization.of(context)!
//                                         .getTranslatedValues("month")!,
//                             style: AppStyles.body.copyWith(
//                               color: Theme.of(context).primaryColor,
//                               fontSize: 16,
//                             )),
//                       ]),
//                 ),
//               ),
//               Container(
//                 height: 25,
//                 child: SvgPicture.asset(
//                   'assets/images/checked_Icon.svg',
//                   fit: BoxFit.contain,
//                   //width: MediaQuery.of(context).size.width * 0.6,
//                 ),
//               ),
//             ],
//           ),
//         ) /* Column(
//           children: [
//             Padding(
//               padding: const EdgeInsets.only(
//                   left: 10.0, bottom: 5, top: 10, right: 10),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     duration > 1
//                         ? duration.toString() +
//                             " " +
//                             AppLocalizations.of(context).months
//                         : duration.toString() +
//                             " " +
//                             AppLocalizations.of(context).month,
//                     style: AppStyles.bodyBold
//                         .copyWith(fontSize: 18, color: Color(0xff00A690)),
//                   ),
//                   Text(
//                     price > 1000
//                         ? formatter.format(price).toString() + " " + currrency
//                         : price.toStringAsFixed(2) + " " + currrency,
//                     style: AppStyles.bodyBold
//                         .copyWith(fontSize: 18, color: Color(0xff00A690)),
//                   ),
//                 ],
//               ),
//             ),
//             Padding(
//               padding: const EdgeInsets.only(
//                   left: 10.0, bottom: 10, top: 5, right: 10),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     price > 1000
//                         ? formatter
//                                 .format(
//                                     ((price / duration) / 1000).round() * 1000)
//                                 .toString() +
//                             " " +
//                             currrency +
//                             " " +
//                             AppLocalizations.of(context).monthly
//                         : (price / duration).toStringAsFixed(2) +
//                             " " +
//                             currrency +
//                             " " +
//                             AppLocalizations.of(context).monthly,
//                     style: AppStyles.body
//                         .copyWith(fontSize: 16, color: Color(0xff00A690)),
//                   ),
//                   Text(AppLocalizations.of(context).total,
//                       style: AppStyles.bodyBold
//                           .copyWith(fontSize: 16, color: Color(0xff00A690))),
//                 ],
//               ),
//             ),
//           ],
//         ) */
//         ,
//       ),
//     );
//   }
// }

// class ReconnectingOverlay extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) => Center(
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 20.0),
//               child: CircularProgressIndicator(),
//             ),
//             SizedBox(height: 12),
//             Text(
//               'Reconnecting...',
//             ),
//           ],
//         ),
//       );
// }
