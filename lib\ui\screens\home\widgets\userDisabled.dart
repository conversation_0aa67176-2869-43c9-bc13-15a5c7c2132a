import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';

class UserDisabledDialog extends StatelessWidget {
  const UserDisabledDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => Future.value(false),
      child: AlertDialog(
        content: Text(
          AppLocalization.of(context)!.getTranslatedValues("userDisabledLbl")!,
          style: TextStyle(color: Theme.of(context).colorScheme.secondary),
        ),
        actions: [
          TextButton(
              onPressed: () {
                Navigator.of(context).pop();

                context.read<AuthCubit>().signOut();
                Navigator.of(context).pushReplacementNamed(Routes.login);
              },
              child: Text(
                "OK",
                style: TextStyle(color: Theme.of(context).primaryColor),
              )),
        ],
      ),
    );
  }
}
