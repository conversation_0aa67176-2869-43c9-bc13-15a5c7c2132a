//State
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/models/result_detail.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/systemConfig/model/result_model.dart';
import 'package:flutterquiz/features/systemConfig/model/result_status.dart';
import 'package:flutterquiz/utils/answerEncryption.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/stringLabels.dart';

@immutable
abstract class ResultState {}

class ResultIntial extends ResultState {}

class ResultFetchInProgress extends ResultState {
  final QuizTypes quizType;
  ResultFetchInProgress(this.quizType);
}

class ResultFetchFailure extends ResultState {
  final String errorMessage;
  ResultFetchFailure(this.errorMessage);
}

class ResultFetchSuccess extends ResultState {
  final List<Result> resultDetail;
  final QuizTypes quizType;
  ResultFetchSuccess({required this.resultDetail, required this.quizType});
}

class ResultResultFetchSuccess extends ResultState {
  final List<ResultStatus> resultStatus;
  // final int currentPoints;
  // final QuizTypes quizType;

  ResultResultFetchSuccess({
    required this.resultStatus,
    // required this.currentPoints,
    // required this.quizType
  });
}

class ResultCubit extends Cubit<ResultState> {
  final QuizRepository _quizRepository;
  ResultCubit(this._quizRepository) : super(ResultIntial());

  void updateState(ResultState newState) {
    emit(newState);
  }

  getResultDetail(String rid) {
    emit(ResultFetchInProgress(QuizTypes.exam));
    _quizRepository.getResultDetail(rid: rid).then((questions) {
      emit(ResultResultFetchSuccess(resultStatus: questions));
    }).catchError((e) {
      emit(ResultFetchFailure(e.toString()));
    });
  }

  List<Result> ResultDetail() {
    if (state is ResultFetchSuccess) {
      return (state as ResultFetchSuccess).resultDetail;
    }
    return [];
  }
}
