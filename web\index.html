<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    Fore more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter application.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Elite Quiz">
  <meta name="google-signin-client_id" content="322915959213-d59srtk19gfj4cepvlf2lcpbo45a00et.apps.googleusercontent.com">
  
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Elite Quiz</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>

      <script src="https://www.gstatic.com/firebasejs/8.6.1/firebase-app.js"></script>
      <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-auth.js"></script>
      <script src="https://www.gstatic.com/firebasejs/8.4.1/firebase-firestore.js"></script>
      <script src="https://www.gstatic.com/firebasejs/8.4.1/firebase-messaging.js"></script>
  <!-- This script installs service_worker.js to provide PWA functionality to
       application. For more information, see:
       https://developers.google.com/web/fundamentals/primers/service-workers -->
      <script>
  // Your web app's Firebase configuration
    const firebaseConfig = {
    apiKey: "AIzaSyAO_zgaFbek9EZIjvV1QjRIVFxWPypcN1w",
    authDomain: "quiz-flutter-new.firebaseapp.com",
    projectId: "quiz-flutter-new",
    storageBucket: "quiz-flutter-new.appspot.com",
    messagingSenderId: "322915959213",
    appId: "1:322915959213:web:faea1ba0d6c3375342e124",
    measurementId: "G-P6RYH0TNCK"
  };
  // Initialize Firebase
  firebase.initializeApp(firebaseConfig);
</script>
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('flutter-first-frame', function () {
        navigator.serviceWorker.register('flutter_service_worker.js');
      });
    }

    
  </script>
  <script src="main.dart.js" type="application/javascript"></script>
  
</body>
</html>
