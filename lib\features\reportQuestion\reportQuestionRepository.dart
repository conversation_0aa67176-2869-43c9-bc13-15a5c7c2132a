import 'package:flutterquiz/features/reportQuestion/reportQuestionException.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRemoteDataSource.dart';

import 'model/report.dart';

class ReportQuestionRepository {
  static final ReportQuestionRepository _reportQuestionRepository =
      ReportQuestionRepository._internal();
  late ReportQuestionRemoteDataSource _reportQuestionRemoteDataSource;

  factory ReportQuestionRepository() {
    _reportQuestionRepository._reportQuestionRemoteDataSource =
        ReportQuestionRemoteDataSource();
    return _reportQuestionRepository;
  }

  ReportQuestionRepository._internal();

  Future<void> reportQuestion(
      {required String questionId,
      required String message,
      required String userId}) async {
    try {
      await _reportQuestionRemoteDataSource.reportQuestion(
          message: message, questionId: questionId, userId: userId);
    } catch (e) {
      throw ReportQuestionException(errorMessageCode: e.toString());
    }
  }

  Future<List<Report>> reportQuestionList({required String userId}) async {
    try {
      List result = await _reportQuestionRemoteDataSource.reportQuestionList(
          userId: userId);
      result = List.from(result.reversed);
      List<Report> listReport = [];
      listReport = result
          .map((question) => Report.fromJson(Map.from(question)))
          .toList();
      listReport.sort((a, b) {
        final dateA = DateTime.parse(a.updatedDate!);
        final dateB = DateTime.parse(b.updatedDate!);
        return dateB.compareTo(dateA); // giảm dần
      });
      return listReport;
    } catch (e) {
      throw ReportQuestionException(errorMessageCode: e.toString());
    }
  }

  Future reportQuestionDetail({required String id}) async {
    try {
      final result =
          await _reportQuestionRemoteDataSource.reportQuestionDetail(id: id);
      return Report.fromJson(result);
    } catch (e) {
      throw ReportQuestionException(errorMessageCode: e.toString());
    }
  }
}
