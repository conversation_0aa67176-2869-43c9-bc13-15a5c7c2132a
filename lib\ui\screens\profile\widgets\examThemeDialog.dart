import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/ui/styles/theme/examThemeCubit.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class ExamThemeDialog extends StatelessWidget {
  const ExamThemeDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ExamThemeCubit, ExamThemeState>(
      bloc: context.read<ExamThemeCubit>(),
      builder: (context, state) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
          content: Column(mainAxisSize: MainAxisSize.min, children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 10.0),
              decoration: BoxDecoration(
                color: state.appTheme == ExamTheme.Scrum
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.secondary,
                borderRadius: BorderRadius.circular(10),
              ),
              child: ListTile(
                trailing: state.appTheme == ExamTheme.Scrum
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.surface,
                      )
                    : SizedBox(),
                onTap: () {
                  //
                  context.read<ExamThemeCubit>().changeTheme(ExamTheme.Scrum);
                },
                title: Text(
                  AppLocalization.of(context)!.getTranslatedValues(
                      UiUtils.getExamThemeLabelFromExamTheme(ExamTheme.Scrum))!,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.symmetric(vertical: 10.0),
              decoration: BoxDecoration(
                color: state.appTheme == ExamTheme.Elite
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.secondary,
                borderRadius: BorderRadius.circular(10),
              ),
              child: ListTile(
                trailing: state.appTheme == ExamTheme.Elite
                    ? Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.surface,
                      )
                    : SizedBox(),
                onTap: () {
                  //
                  context.read<ExamThemeCubit>().changeTheme(ExamTheme.Elite);
                },
                title: Text(
                  AppLocalization.of(context)!.getTranslatedValues(
                      UiUtils.getExamThemeLabelFromExamTheme(ExamTheme.Elite))!,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            )
          ]),
        );
      },
    );
  }
}
