import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/ui/screens/home/<USER>';

import '../../styles/payment/app_styles.dart';

class PurchaseSuccessDialog extends StatelessWidget {
  final String text;
  const PurchaseSuccessDialog({Key? key, required this.text}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamedAndRemoveUntil(
            Routes.home, (Route<dynamic> route) => false);
      },
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        color: Colors.transparent,
        child: Dialog(
          backgroundColor: Colors.white,
          insetPadding: EdgeInsets.symmetric(horizontal: 20),
          child: SingleChildScrollView(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              child: Stack(
                children: [
                  Positioned(
                      top: 0,
                      right: 0,
                      child: Icon(
                        Icons.close_outlined,
                        size: 30,
                        color: Colors.black,
                      )),
                  Column(children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 0),
                      child: SvgPicture.asset(
                        'assets/images/purchaseSuccess.svg',
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Container(
                        margin: EdgeInsets.only(top: 10),
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 10.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "Congratulation!",
                                    textAlign: TextAlign.center,
                                    style: AppStyles.titleBold.copyWith(
                                        color: Color(0xff00326C), fontSize: 20),
                                  ),
                                  SizedBox(
                                    width: 5,
                                  ),
                                  SvgPicture.asset("assets/images/Congrat.svg")
                                ],
                              ),
                            ),
                            Text(
                              text,
                              textAlign: TextAlign.center,
                              style: AppStyles.dialogText,
                            ),
                          ],
                        ),
                      ),
                    )
                  ]),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
