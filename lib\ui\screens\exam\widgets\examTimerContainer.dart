import 'dart:async';

import 'package:flutter/material.dart';

class ExamTimerContainer extends StatefulWidget {
  final int examDurationInMinutes;
  final Function? navigateToResultScreen;
  ExamTimerContainer(
      {Key? key,
      required this.examDurationInMinutes,
      this.navigateToResultScreen})
      : super(key: key);

  @override
  ExamTimerContainerState createState() => ExamTimerContainerState();
}

class ExamTimerContainerState extends State<ExamTimerContainer>
    with WidgetsBindingObserver {
  late int minutesLeft = widget.examDurationInMinutes - 1;
  late int secondsLeft = 59;
  AppLifecycleState _appLifecycleState = AppLifecycleState.resumed;
  bool _isInBackground = false;
  void startTimer() {
    if (_appLifecycleState == AppLifecycleState.resumed) {
      examTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        if (minutesLeft == 0 && secondsLeft == 0) {
          timer.cancel();
          if (widget.navigateToResultScreen != null) {
            widget.navigateToResultScreen!();
          }
        } else {
          if (secondsLeft == 0) {
            secondsLeft = 59;
            minutesLeft--;
          } else {
            secondsLeft--;
          }
          setState(() {});
        }
      });
    }
  }

  Timer? examTimer;

  int getCompletedExamDuration() {
    print("Exam completed in ${(widget.examDurationInMinutes - minutesLeft)}");
    return (widget.examDurationInMinutes - minutesLeft);
  }

  int getCompletedExamDurationSecond() {
    if (widget.examDurationInMinutes >= 1)
      return (widget.examDurationInMinutes - (minutesLeft + 1)) * 60 +
          (60 - secondsLeft);
    else
      return (60 - secondsLeft);
  }

  void cancelTimer() {
    print("Cancel timer");
    examTimer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    cancelTimer();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    setState(() {
      _appLifecycleState = state;
      if (_appLifecycleState == AppLifecycleState.paused ||
          _appLifecycleState == AppLifecycleState.inactive) {
        _isInBackground = true;
        cancelTimer();
      } else if (_appLifecycleState == AppLifecycleState.resumed) {
        _isInBackground = false;
        startTimer();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    String hours = (minutesLeft ~/ 60).toString().length == 1
        ? "0${(minutesLeft ~/ 60)}"
        : (minutesLeft ~/ 60).toString();

    String minutes = (minutesLeft % 60).toString().length == 1
        ? "0${(minutesLeft % 60)}"
        : (minutesLeft % 60).toString();
    hours = hours == "00" ? "" : hours;

    String seconds = secondsLeft < 10 ? "0$secondsLeft" : "$secondsLeft";
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2.5),
      child: Text(
        hours.isEmpty ? "$minutes:$seconds" : "$hours:$minutes:$seconds",
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Theme.of(context).colorScheme.secondary,
        ),
      ),
    );
  }
}
