import 'package:bloc/bloc.dart';
import 'package:flutterquiz/features/profileManagement/models/certificate.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';

abstract class CertificateState {
  const CertificateState();
}

class CertificateInitial extends CertificateState {}

class CertificateLoading extends CertificateState {}

class CertificateSuccess extends CertificateState {
  final List<Certificate> certificates;

  const CertificateSuccess({required this.certificates});
}

class CertificateChangeSuccess extends CertificateState {}

class CertificateFailure extends CertificateState {
  final String error;

  const CertificateFailure(this.error);
}

class CertificateCubit extends Cubit<CertificateState> {
  final ProfileManagementRepository _profileManagementRepository;

  CertificateCubit(this._profileManagementRepository)
      : super(CertificateInitial());

  void getAllCertificates(String userId) async {
    emit(CertificateLoading());
    try {
      final result =
          await _profileManagementRepository.getAllCertificates(userId: userId);
      final certificates =
          result.map<Certificate>((e) => Certificate.fromJson(e)).toList();
      emit(CertificateSuccess(certificates: certificates));
    } catch (e) {
      emit(CertificateFailure(e.toString()));
    }
  }

  void changeCertificate(String userId, String certificateId) async {
    emit(CertificateLoading());
    try {
      await _profileManagementRepository.changeCertificate(
        userId: userId,
        certificateId: certificateId,
      );
      emit(CertificateChangeSuccess());
    } catch (e) {
      emit(CertificateFailure(e.toString()));
    }
  }
}
