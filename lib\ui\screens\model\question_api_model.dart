class Question {
  String? id;
  String? type;
  late String question;
  List<Options>? options;
  String? correct;
  List<int>? correctIndex;
  String? quizId;
  String? description;
  String? userAns;

  Question(
      {this.id,
      this.type,
      required this.question,
      this.options,
      this.description,
      this.userAns,
      this.quizId});

  Question.fromJson(Map<String, dynamic> json) {
    if (json["id"] is String) this.id = json["id"];
    if (json["quizId"] is String) this.quizId = json["quizId"];
    if (json["type"] is String) this.type = json["type"];
    if (json["question"] is String) this.question = json["question"];
    if (json["description"] is String) this.description = json["description"];
    if (json["options"] is List)
      this.options = json["options"] == null
          ? null
          : (json["options"] as List).map((e) => Options.fromJson(e)).toList();
    if (json["correct"] is String) this.correct = json["correct"];
    if (json["userAns"] is String) this.correct = json["userAns"];
    if (json["correct_index"] is List)
      this.correctIndex = json["correct_index"] == null
          ? null
          : List<int>.from(json["correct_index"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["id"] = this.id;
    data["quizId"] = this.quizId;
    data["type"] = this.type;
    data["question"] = this.question;
    data["description"] = this.description;
    if (this.options != null)
      data["options"] = this.options?.map((e) => e.toJson()).toList();
    data["correct"] = this.correct;
    data["userAns"] = this.userAns;
    data["correct_index"] = this.correctIndex;

    return data;
  }
}

class Options {
  late String oid;
  String? qid;
  late String qOption;
  dynamic? qOptionMatch;
  String? qOption1;
  String? score;
  String? qOptionMatch1;

  Options(
      {required this.oid,
      this.qid,
      required this.qOption,
      this.qOptionMatch,
      this.qOption1,
      this.score,
      this.qOptionMatch1});

  Options.fromJson(Map<String, dynamic> json) {
    if (json["oid"] is String) this.oid = json["oid"];
    if (json["qid"] is String) this.qid = json["qid"];
    if (json["q_option"] is String) this.qOption = json["q_option"];
    this.qOptionMatch = json["q_option_match"];
    if (json["q_option1"] is String) this.qOption1 = json["q_option1"];
    if (json["score"] is String) this.score = json["score"];
    if (json["q_option_match1"] is String)
      this.qOptionMatch1 = json["q_option_match1"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["oid"] = this.oid;
    data["qid"] = this.qid;
    data["q_option"] = this.qOption;
    data["q_option_match"] = this.qOptionMatch;
    data["q_option1"] = this.qOption1;
    data["score"] = this.score;
    data["q_option_match1"] = this.qOptionMatch1;
    return data;
  }
}
