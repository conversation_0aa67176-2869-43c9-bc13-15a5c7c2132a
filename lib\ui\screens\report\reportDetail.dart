import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/features/reportQuestion/model/report.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionCubit.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/ui/widgets/roundedAppbar.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class ReportDetailScreen extends StatefulWidget {
  final String id;
  const ReportDetailScreen({Key? key, required this.id}) : super(key: key);

  @override
  State<ReportDetailScreen> createState() => _ReportDetailScreenState();

  static Route<ReportDetailScreen> route(RouteSettings routeSettings) {
    Map arguments = routeSettings.arguments as Map;

    return CupertinoPageRoute(
        builder: (_) => BlocProvider<ReportQuestionCubit>(
            child: ReportDetailScreen(
              id: arguments['id'],
            ),
            create: (_) => ReportQuestionCubit(ReportQuestionRepository())));
  }
}

class _ReportDetailScreenState extends State<ReportDetailScreen> {
  Report report = Report();

  void getReport() {
    Future.delayed(Duration.zero, () {
      context.read<ReportQuestionCubit>().getReportDetail(id: widget.id);
    });
  }

  @override
  void initState() {
    getReport();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageBackgroundGradientContainer(),
          BlocConsumer<ReportQuestionCubit, ReportQuestionState>(
              listener: (context, state) {},
              builder: (context, state) {
                if (state is ReportQuestionSuccess) {
                  report = state.reports![0];
                  return Align(
                    alignment: Alignment.topCenter,
                    child: _buildReportDetailContainer(report),
                  );
                }
                if (state is ReportQuestionFailure) {
                  return Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("noData"),
                        onTapRetry: () {
                          setState(() {
                            getReport();
                          });
                        },
                        showErrorImage: true),
                  );
                }
                return Center(
                  child: CircularProgressContainer(
                    useWhiteLoader: false,
                  ),
                );
              }),
          Align(
            alignment: Alignment.topCenter,
            child: RoundedAppbar(
                title: AppLocalization.of(context)!
                    .getTranslatedValues(reportLbl)!),
          ),
        ],
      ),
    );
  }

  Widget _buildReportDetailContainer(Report report) {
    return Padding(
        padding: EdgeInsets.only(
          right: MediaQuery.of(context).size.width * (0.05),
          left: MediaQuery.of(context).size.width * (0.05),
          top: MediaQuery.of(context).size.height *
              UiUtils.appBarHeightPercentage *
              1.2,
          bottom: MediaQuery.of(context).size.height * 0.075,
        ),
        child: SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Row(
              children: [
                Text(
                    "${AppLocalization.of(context)!.getTranslatedValues('status')!}: ",
                    style: TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).primaryColor)),
                Text(
                    report.status == "0"
                        ? "${AppLocalization.of(context)!.getTranslatedValues('processing')!}"
                        : report.status == "1"
                            ? "${AppLocalization.of(context)!.getTranslatedValues('answered')!}"
                            : "${AppLocalization.of(context)!.getTranslatedValues('closed')!}",
                    style: TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).primaryColor)),
              ],
            ),
            SizedBox(height: 16),
            Text(
                "${AppLocalization.of(context)!.getTranslatedValues('questionLbl')!}:",
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                    fontSize: 16)),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                  boxShadow: [
                    UiUtils.buildBoxShadow(
                        blurRadius: 3.0,
                        color: Colors.black.withOpacity(0.2),
                        offset: Offset(2.5, 2.5)),
                  ],
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8)),
              child: /* Text((report.reportQuestion ?? "").toString().trim(),
                  style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).primaryColor)) */
                  HtmlWidget(
                (report.reportQuestion ?? "")
                    .toString()
                    .trim()
                    .replaceAll('../../', Common.apiDomain),
                key: ValueKey(0),
                textStyle: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.w400,
                    color: Theme.of(context).primaryColor),
                onTapImage: (p0) {
                  final imageProvider =
                      Image.network(p0.sources.first.url).image;
                  showImageViewer(context, imageProvider,
                      backgroundColor: Colors.black.withOpacity(0.8),
                      useSafeArea: true,
                      doubleTapZoomable: true);
                },
              ),
            ),
            SizedBox(height: 16),
            Text(
                "${AppLocalization.of(context)!.getTranslatedValues('reportContent')!}:",
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                    fontSize: 16)),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                  boxShadow: [
                    UiUtils.buildBoxShadow(
                        blurRadius: 3.0,
                        color: Colors.black.withOpacity(0.2),
                        offset: Offset(2.5, 2.5)),
                  ],
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8)),
              child: Text(report.reportDetail ?? "",
                  style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).primaryColor)),
            ),
            SizedBox(height: 16),
            if ((report.response?.length ?? 0) > 1) ...{
              Text(
                  "${AppLocalization.of(context)!.getTranslatedValues('response')!}:",
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                      fontSize: 16)),
              SizedBox(height: 10),
            },
            if (report.response?.isNotEmpty ?? false) ...{
              ListView.separated(
                padding: EdgeInsets.only(top: 0),
                shrinkWrap: true,
                itemCount: report.response!.length,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (BuildContext context, int index) {
                  if (report.response![index]["response"] != null) {
                    return Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          boxShadow: [
                            UiUtils.buildBoxShadow(
                                blurRadius: 3.0,
                                color: Colors.black.withOpacity(0.2),
                                offset: Offset(2.5, 2.5)),
                          ],
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(8)),
                      child: Row(
                        children: [
                          Text(
                              "${AppLocalization.of(context)!.getTranslatedValues(report.response![index]["is_admin"] == "1" ? 'adminLbl' : 'youLbl')!}: ",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 16)),
                          Text(
                              (report.response![index]["response"] ?? "")
                                  .toString()
                                  .trim(),
                              style: TextStyle(
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.w400,
                                  color: Theme.of(context).primaryColor)),
                        ],
                      ),
                    );
                  } else {
                    return Container();
                  }
                },
                separatorBuilder: (context, index) {
                  return Container(
                    height: 16,
                  );
                },
              )
            }
          ]),
        ));
  }
}
