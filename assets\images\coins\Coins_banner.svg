<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="252.506" height="284.526" viewBox="0 0 252.506 284.526">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b38746"/>
      <stop offset="0.389" stop-color="#d9973b"/>
      <stop offset="0.75" stop-color="#ad6c10"/>
      <stop offset="0.858" stop-color="#d3953d"/>
      <stop offset="1" stop-color="#b07f35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffee76"/>
      <stop offset="1" stop-color="#1d1d1b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#e6a729"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#e0c0a8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-15" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f0cc62"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-20" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-27" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-29" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-40" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-41" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e0c0a8"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-90" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-91" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-108" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-154" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-164" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
  </defs>
  <g id="Coins_banner" transform="translate(-163.29 -159.507)">
    <g id="Group_3335" data-name="Group 3335" transform="translate(279.522 159.507)">
      <path id="Path_11528" data-name="Path 11528" d="M204.452,224.643c0,16.59,26.982,30.037,60.265,30.037s60.265-13.447,60.265-30.037l0-13.128H204.452v13.128Z" transform="translate(-195.456 -53.29)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11529" data-name="Path 11529" d="M237.609,224.144q3.856.251,7.855.256a117.887,117.887,0,0,0,15.823-1.05v13.1a89.934,89.934,0,0,1-15.823,1.044c-8.528,0-22.4-1.628-31.585-4.451V219.93a103.949,103.949,0,0,0,23.73,4.214Z" transform="translate(-176.203 -36.104)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11530" data-name="Path 11530" d="M237.76,233.382c11.695-5.452,19.051-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V233.382Z" transform="translate(-127.43 -53.174)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11531" data-name="Path 11531" d="M216.828,229.738v13.14c-7.764-5.056-12.376-11.378-12.376-18.236V211.515c.037,6.854,4.643,13.167,12.376,18.223Z" transform="translate(-195.456 -53.29)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11532" data-name="Path 11532" d="M233.526,223.041q3.856.251,7.855.256V236.39a113.118,113.118,0,0,1-25.5-2.814V220.47a108.478,108.478,0,0,0,17.648,2.571Z" transform="translate(-172.12 -35.001)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11533" data-name="Path 11533" d="M272.028,241.089a27.37,27.37,0,0,1-.35,4.5.3.3,0,0,1-.6,0,28.876,28.876,0,0,1,0-9,.3.3,0,0,1,.6,0,27.411,27.411,0,0,1,.35,4.5Zm7.934-.8a28.148,28.148,0,0,1-.338,4.5.287.287,0,0,1-.569,0,30.171,30.171,0,0,1,0-9,.287.287,0,0,1,.569,0,28.187,28.187,0,0,1,.338,4.5Zm7.855-1.378a29.828,29.828,0,0,1-.322,4.5.273.273,0,0,1-.545,0,31.568,31.568,0,0,1,0-9,.273.273,0,0,1,.545,0,29.756,29.756,0,0,1,.322,4.5Zm7.715-2.011a31.165,31.165,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.107,33.107,0,0,1,0-9,.26.26,0,0,1,.52,0,31.176,31.176,0,0,1,.307,4.5Zm7.487-2.741a32.71,32.71,0,0,1-.292,4.5.262.262,0,0,1-.246.246.267.267,0,0,1-.246-.246,34.855,34.855,0,0,1,0-9.005.27.27,0,0,1,.246-.249.265.265,0,0,1,.246.249,32.71,32.71,0,0,1,.292,4.5Zm7.116-3.6a34.342,34.342,0,0,1-.277,4.5.235.235,0,1,1-.468,0,36.724,36.724,0,0,1,0-9,.235.235,0,1,1,.468,0,34.322,34.322,0,0,1,.277,4.5Zm6.434-4.7a36.806,36.806,0,0,1-.262,4.5.224.224,0,1,1-.444,0,38.85,38.85,0,0,1,0-9,.224.224,0,1,1,.444,0,36.821,36.821,0,0,1,.262,4.5Zm-61.835,15.23a27.367,27.367,0,0,0,.35,4.5.3.3,0,0,0,.6,0,28.878,28.878,0,0,0,0-9,.3.3,0,0,0-.6,0,27.408,27.408,0,0,0-.35,4.5Zm-7.934-.8a28.138,28.138,0,0,0,.338,4.5.285.285,0,0,0,.283.249.288.288,0,0,0,.286-.249,30.163,30.163,0,0,0,0-9,.288.288,0,0,0-.286-.249.285.285,0,0,0-.283.249,28.177,28.177,0,0,0-.338,4.5Zm-7.855-1.378a29.816,29.816,0,0,0,.323,4.5.273.273,0,0,0,.545,0,31.574,31.574,0,0,0,0-9,.273.273,0,0,0-.545,0,29.743,29.743,0,0,0-.323,4.5ZM231.23,236.9a31.15,31.15,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.123,33.123,0,0,0,0-9,.26.26,0,0,0-.52,0,31.161,31.161,0,0,0-.307,4.5Zm-7.487-2.741a33.244,33.244,0,0,0,.289,4.5.248.248,0,0,0,.5,0,34.86,34.86,0,0,0,0-9.005.248.248,0,1,0-.5,0,33.245,33.245,0,0,0-.289,4.5Zm-7.116-3.6a34.348,34.348,0,0,0,.277,4.5.235.235,0,1,0,.469,0,36.726,36.726,0,0,0,0-9,.235.235,0,1,0-.469,0,34.328,34.328,0,0,0-.277,4.5Zm-6.435-4.7a36.8,36.8,0,0,0,.262,4.5.224.224,0,1,0,.444,0,38.85,38.85,0,0,0,0-9,.224.224,0,1,0-.444,0,36.819,36.819,0,0,0-.262,4.5Zm52.51,15.5a26.046,26.046,0,0,0,.368,4.5.319.319,0,0,0,.621,0,27.72,27.72,0,0,0,0-9.005.318.318,0,0,0-.621,0,26.046,26.046,0,0,0-.368,4.5Zm-57.6-21.613a38.239,38.239,0,0,0,.246,4.5c.015.146.1.249.207.249s.192-.1.21-.249a41.235,41.235,0,0,0,0-9c-.018-.146-.1-.249-.21-.249s-.192.1-.207.249a38.294,38.294,0,0,0-.246,4.5Zm116.551,0a38.265,38.265,0,0,1-.246,4.5c-.015.146-.1.249-.207.249s-.195-.1-.21-.249a41.23,41.23,0,0,1,0-9c.015-.146.1-.249.21-.249s.192.1.207.249a38.32,38.32,0,0,1,.246,4.5Z" transform="translate(-194.12 -46.183)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11534" data-name="Path 11534" d="M204.616,232.2c0,16.836,26.55,30.487,59.3,30.487s59.3-13.651,59.3-30.487-26.547-30.484-59.3-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-195.121 -73.303)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11535" data-name="Path 11535" d="M204.452,231.729c0,16.651,26.982,30.152,60.265,30.152s60.265-13.5,60.265-30.152S298,201.577,264.717,201.577s-60.265,13.5-60.265,30.152Z" transform="translate(-195.456 -73.587)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11536" data-name="Path 11536" d="M206.325,228.15c-.389,14.865,23.73,27.551,53.87,28.342s54.893-10.621,55.282-25.482-23.73-27.554-53.87-28.342-54.892,10.621-55.282,25.482Z" transform="translate(-191.64 -71.438)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11537" data-name="Path 11537" d="M206.448,229.691c0,14.974,24.262,27.113,54.193,27.113s54.193-12.139,54.193-27.113-24.262-27.116-54.193-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-191.379 -71.549)" fill="#9e9e9e" fill-rule="evenodd"/>
      <path id="Path_11538" data-name="Path 11538" d="M208.132,229.421c0,14.807,23.992,26.809,53.584,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.584,12-53.584,26.809Z" transform="translate(-187.94 -71.473)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11539" data-name="Path 11539" d="M314.733,231.364c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.924,13.924,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.193,12.142,54.193,27.116a13.911,13.911,0,0,1-.1,1.673Z" transform="translate(-191.379 -71.549)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11540" data-name="Path 11540" d="M202.973,219.969c0,16.587,26.982,30.037,60.265,30.037s60.265-13.45,60.265-30.037l0-13.128H202.973v13.128Z" transform="translate(-198.476 -62.836)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11541" data-name="Path 11541" d="M236.13,219.47q3.856.251,7.855.253a117.882,117.882,0,0,0,15.823-1.05v13.1a89.879,89.879,0,0,1-15.823,1.047c-8.528,0-22.4-1.628-31.585-4.451V215.256a104.145,104.145,0,0,0,23.73,4.214Z" transform="translate(-179.224 -45.65)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11542" data-name="Path 11542" d="M236.281,228.71c11.695-5.452,19.051-13.2,19.194-21.813l0,12.957c0,8.677-7.387,16.5-19.2,21.981V228.71Z" transform="translate(-130.451 -62.722)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11543" data-name="Path 11543" d="M215.349,225.064V238.2c-7.764-5.056-12.376-11.378-12.376-18.236V206.841c.04,6.851,4.646,13.167,12.376,18.223Z" transform="translate(-198.476 -62.836)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11544" data-name="Path 11544" d="M232.047,218.369q3.856.251,7.855.253v13.1a112.955,112.955,0,0,1-25.5-2.817V215.795a108.8,108.8,0,0,0,17.648,2.574Z" transform="translate(-175.141 -44.549)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11545" data-name="Path 11545" d="M270.552,236.412a27.41,27.41,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.9,28.9,0,0,1,0-9.005.3.3,0,0,1,.6,0,27.41,27.41,0,0,1,.353,4.5Zm7.931-.8a28.585,28.585,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.165,30.165,0,0,1,0-9,.289.289,0,0,1,.572,0,28.515,28.515,0,0,1,.335,4.5Zm7.855-1.375a29.748,29.748,0,0,1-.322,4.5.273.273,0,0,1-.545,0,31.574,31.574,0,0,1,0-9,.273.273,0,0,1,.545,0,29.819,29.819,0,0,1,.322,4.5Zm7.715-2.014a31.193,31.193,0,0,1-.307,4.5.274.274,0,0,1-.259.249.276.276,0,0,1-.262-.249,33.468,33.468,0,0,1,0-9.005.274.274,0,0,1,.262-.246.271.271,0,0,1,.259.246,31.194,31.194,0,0,1,.307,4.5Zm7.487-2.738a33.189,33.189,0,0,1-.289,4.5.248.248,0,1,1-.5,0,34.828,34.828,0,0,1,0-9,.248.248,0,1,1,.5,0,33.2,33.2,0,0,1,.289,4.5Zm7.116-3.6a34.4,34.4,0,0,1-.277,4.5.235.235,0,1,1-.469,0,36.761,36.761,0,0,1,0-9.005.235.235,0,1,1,.469,0,34.4,34.4,0,0,1,.277,4.5Zm6.435-4.7a36.265,36.265,0,0,1-.262,4.5.224.224,0,1,1-.444,0,38.877,38.877,0,0,1,0-9.005.223.223,0,1,1,.444,0,36.266,36.266,0,0,1,.262,4.5Zm-61.835,15.23a27.431,27.431,0,0,0,.35,4.5.3.3,0,0,0,.6,0,28.9,28.9,0,0,0,0-9.005.3.3,0,0,0-.6,0,27.431,27.431,0,0,0-.35,4.5Zm-7.934-.8a28.2,28.2,0,0,0,.338,4.5.287.287,0,0,0,.569,0,30.168,30.168,0,0,0,0-9,.287.287,0,0,0-.569,0,28.133,28.133,0,0,0-.338,4.5Zm-7.855-1.375a29.748,29.748,0,0,0,.323,4.5.273.273,0,0,0,.545,0,31.568,31.568,0,0,0,0-9,.273.273,0,0,0-.545,0,29.819,29.819,0,0,0-.323,4.5Zm-7.715-2.014a31.2,31.2,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.134,33.134,0,0,0,0-9.005.26.26,0,0,0-.52,0,31.2,31.2,0,0,0-.307,4.5Zm-7.487-2.738a32.671,32.671,0,0,0,.292,4.5.263.263,0,0,0,.246.249.267.267,0,0,0,.246-.249,34.83,34.83,0,0,0,0-9,.267.267,0,0,0-.246-.249.263.263,0,0,0-.246.249,32.683,32.683,0,0,0-.292,4.5Zm-7.116-3.6a34.4,34.4,0,0,0,.277,4.5.261.261,0,0,0,.234.246.256.256,0,0,0,.234-.246,36.755,36.755,0,0,0,0-9.005.259.259,0,0,0-.234-.249.264.264,0,0,0-.234.249,34.4,34.4,0,0,0-.277,4.5Zm-6.435-4.7a36.872,36.872,0,0,0,.262,4.5.224.224,0,1,0,.444,0,38.874,38.874,0,0,0,0-9.005.223.223,0,1,0-.444,0,36.873,36.873,0,0,0-.262,4.5Zm52.51,15.5a26.006,26.006,0,0,0,.368,4.5.318.318,0,0,0,.621,0,27.7,27.7,0,0,0,0-9,.318.318,0,0,0-.621,0,26.018,26.018,0,0,0-.368,4.5Zm-57.6-21.616a38.335,38.335,0,0,0,.246,4.5c.018.146.1.249.21.249s.192-.1.207-.249a41.228,41.228,0,0,0,0-9c-.015-.146-.1-.249-.207-.249s-.192.1-.21.249a38.242,38.242,0,0,0-.246,4.5Zm116.551,0a38.332,38.332,0,0,1-.246,4.5c-.015.146-.1.249-.207.249s-.192-.1-.21-.249a41.23,41.23,0,0,1,0-9c.018-.146.1-.249.21-.249s.192.1.207.249a38.239,38.239,0,0,1,.246,4.5Z" transform="translate(-197.141 -55.729)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11546" data-name="Path 11546" d="M203.137,227.528c0,16.836,26.55,30.484,59.3,30.484s59.3-13.648,59.3-30.484-26.547-30.487-59.3-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-198.142 -82.851)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11547" data-name="Path 11547" d="M202.973,227.052c0,16.654,26.982,30.152,60.265,30.152s60.265-13.5,60.265-30.152S296.521,196.9,263.238,196.9s-60.265,13.5-60.265,30.149Z" transform="translate(-198.476 -83.133)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11548" data-name="Path 11548" d="M204.846,223.478c-.386,14.862,23.73,27.551,53.873,28.339S313.609,241.2,314,226.335s-23.73-27.551-53.87-28.342-54.893,10.621-55.282,25.485Z" transform="translate(-194.66 -80.985)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11549" data-name="Path 11549" d="M204.969,225.014c0,14.977,24.262,27.116,54.193,27.116s54.2-12.139,54.2-27.116-24.266-27.113-54.2-27.113-54.193,12.139-54.193,27.113Z" transform="translate(-194.4 -81.094)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11550" data-name="Path 11550" d="M313.254,226.69c-1.728-14.2-25.285-25.443-54.092-25.443S206.8,212.495,205.069,226.69a13.962,13.962,0,0,1-.1-1.676c0-14.974,24.262-27.113,54.193-27.113s54.2,12.139,54.2,27.113a13.963,13.963,0,0,1-.1,1.676Z" transform="translate(-194.4 -81.094)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11551" data-name="Path 11551" d="M206.671,215.294c0,16.59,26.979,30.037,60.262,30.037S327.2,231.883,327.2,215.294l0-13.128H206.671v13.128Z" transform="translate(-190.924 -72.384)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11552" data-name="Path 11552" d="M239.827,214.795q3.856.251,7.855.256A117.85,117.85,0,0,0,263.505,214v13.1a89.892,89.892,0,0,1-15.823,1.044c-8.528,0-22.4-1.628-31.585-4.451V210.581a103.977,103.977,0,0,0,23.73,4.214Z" transform="translate(-171.673 -55.198)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-18)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11553" data-name="Path 11553" d="M239.978,224.033c11.695-5.449,19.054-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V224.033Z" transform="translate(-122.901 -72.267)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11554" data-name="Path 11554" d="M219.044,220.389v13.143c-7.761-5.056-12.373-11.381-12.373-18.239V202.166c.036,6.854,4.643,13.17,12.373,18.224Z" transform="translate(-190.924 -72.384)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11555" data-name="Path 11555" d="M235.744,213.692q3.856.251,7.855.256v13.094a113.162,113.162,0,0,1-25.5-2.814V211.121a108.49,108.49,0,0,0,17.648,2.571Z" transform="translate(-167.59 -54.095)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11556" data-name="Path 11556" d="M274.249,231.74a27.341,27.341,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.886,28.886,0,0,1,0-9,.3.3,0,0,1,.6,0,27.407,27.407,0,0,1,.353,4.5Zm7.931-.8a28.587,28.587,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.19,30.19,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.586,28.586,0,0,1,.335,4.5Zm7.855-1.378a29.8,29.8,0,0,1-.322,4.5.278.278,0,0,1-.271.249.281.281,0,0,1-.274-.249,31.571,31.571,0,0,1,0-9,.281.281,0,0,1,.274-.249.278.278,0,0,1,.271.249,29.756,29.756,0,0,1,.322,4.5Zm7.715-2.011a31.137,31.137,0,0,1-.307,4.5.259.259,0,0,1-.517,0,33.123,33.123,0,0,1,0-9,.259.259,0,0,1,.517,0,31.18,31.18,0,0,1,.307,4.5Zm7.49-2.741a32.715,32.715,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.832,34.832,0,0,1,0-9,.248.248,0,1,1,.5,0,32.636,32.636,0,0,1,.292,4.5Zm7.113-3.6a34.359,34.359,0,0,1-.277,4.5.235.235,0,1,1-.469,0,36.72,36.72,0,0,1,0-9,.235.235,0,1,1,.469,0,34.374,34.374,0,0,1,.277,4.5Zm6.435-4.7a36.177,36.177,0,0,1-.262,4.5c-.018.146-.11.249-.222.249a.254.254,0,0,1-.222-.249,38.845,38.845,0,0,1,0-9,.254.254,0,0,1,.222-.249c.113,0,.2.1.222.249a36.228,36.228,0,0,1,.262,4.5Zm-61.835,15.23a27.361,27.361,0,0,0,.35,4.5.3.3,0,0,0,.3.249.3.3,0,0,0,.3-.249,29.13,29.13,0,0,0,0-9,.3.3,0,0,0-.3-.249.3.3,0,0,0-.3.249,27.427,27.427,0,0,0-.35,4.5Zm-7.934-.8a28.564,28.564,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.457,30.457,0,0,0,0-9.005.289.289,0,0,0-.572,0,28.564,28.564,0,0,0-.338,4.5Zm-7.855-1.378a29.789,29.789,0,0,0,.322,4.5.283.283,0,0,0,.274.249.278.278,0,0,0,.271-.249,31.574,31.574,0,0,0,0-9,.278.278,0,0,0-.271-.249.283.283,0,0,0-.274.249,29.748,29.748,0,0,0-.322,4.5Zm-7.715-2.011a31.128,31.128,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.123,33.123,0,0,0,0-9,.26.26,0,0,0-.52,0,31.171,31.171,0,0,0-.307,4.5Zm-7.487-2.741a32.718,32.718,0,0,0,.292,4.5.246.246,0,1,0,.493,0,34.832,34.832,0,0,0,0-9,.246.246,0,1,0-.493,0,32.639,32.639,0,0,0-.292,4.5Zm-7.116-3.6a34.892,34.892,0,0,0,.277,4.5.262.262,0,0,0,.234.249.257.257,0,0,0,.234-.249,36.726,36.726,0,0,0,0-9,.257.257,0,0,0-.234-.249.262.262,0,0,0-.234.249,34.906,34.906,0,0,0-.277,4.5Zm-6.434-4.7a36.763,36.763,0,0,0,.262,4.5.254.254,0,0,0,.222.249c.113,0,.2-.1.222-.249a38.85,38.85,0,0,0,0-9c-.018-.146-.11-.249-.222-.249a.254.254,0,0,0-.222.249,36.815,36.815,0,0,0-.262,4.5Zm52.51,15.5a26.343,26.343,0,0,0,.368,4.5.309.309,0,0,0,.31.249.315.315,0,0,0,.313-.249,27.955,27.955,0,0,0,0-9.005.313.313,0,0,0-.313-.246.307.307,0,0,0-.31.246,26.342,26.342,0,0,0-.368,4.5Zm-57.6-21.613a39.027,39.027,0,0,0,.246,4.5c.018.143.1.246.21.246s.192-.1.207-.246a41.268,41.268,0,0,0,0-9.005c-.015-.143-.1-.249-.207-.249s-.192.106-.21.249a39.026,39.026,0,0,0-.246,4.5Zm116.554,0a38.308,38.308,0,0,1-.249,4.5c-.015.143-.1.246-.207.246s-.192-.1-.21-.246a41.263,41.263,0,0,1,0-9.005c.018-.143.107-.249.21-.249s.192.106.207.249a38.307,38.307,0,0,1,.249,4.5Z" transform="translate(-189.59 -65.277)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11557" data-name="Path 11557" d="M206.835,222.851c0,16.839,26.547,30.487,59.295,30.487s59.3-13.648,59.3-30.487-26.547-30.484-59.3-30.484-59.295,13.648-59.295,30.484Z" transform="translate(-190.589 -92.397)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11558" data-name="Path 11558" d="M206.671,222.38c0,16.651,26.979,30.152,60.262,30.152s60.265-13.5,60.265-30.152-26.982-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-190.924 -92.68)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11559" data-name="Path 11559" d="M210.291,220.513c0,14.8,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809S293.465,193.7,263.872,193.7s-53.581,12-53.581,26.809Z" transform="translate(-183.531 -89.666)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11560" data-name="Path 11560" d="M208.544,218.8c-.389,14.865,23.727,27.551,53.87,28.342s54.889-10.621,55.279-25.482-23.73-27.554-53.87-28.342S208.934,203.94,208.544,218.8Z" transform="translate(-187.108 -90.531)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11561" data-name="Path 11561" d="M208.666,220.342c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.266-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-186.849 -90.642)" fill="#9e9e9e" fill-rule="evenodd"/>
      <path id="Path_11562" data-name="Path 11562" d="M316.951,222.015c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.922,13.922,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.325,13.325,0,0,1-.1,1.673Z" transform="translate(-186.849 -90.642)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11563" data-name="Path 11563" d="M205.192,210.62c0,16.59,26.979,30.037,60.262,30.037s60.265-13.447,60.265-30.037l0-13.128H205.192V210.62Z" transform="translate(-193.945 -81.93)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11564" data-name="Path 11564" d="M238.346,210.121q3.856.251,7.855.253a117.85,117.85,0,0,0,15.823-1.05v13.1A89.825,89.825,0,0,1,246.2,223.47c-8.528,0-22.4-1.628-31.582-4.451V205.907a104.087,104.087,0,0,0,23.727,4.214Z" transform="translate(-174.692 -64.744)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11565" data-name="Path 11565" d="M238.5,219.361c11.7-5.452,19.054-13.2,19.194-21.813l0,12.957c0,8.68-7.387,16.5-19.2,21.981V219.361Z" transform="translate(-125.921 -81.815)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11566" data-name="Path 11566" d="M217.565,215.715v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V197.492c.036,6.851,4.643,13.167,12.373,18.223Z" transform="translate(-193.945 -81.93)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11567" data-name="Path 11567" d="M234.265,209.018q3.856.251,7.855.253v13.1a112.979,112.979,0,0,1-25.5-2.817v-13.1a109.2,109.2,0,0,0,17.648,2.571Z" transform="translate(-170.611 -63.641)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11568" data-name="Path 11568" d="M272.77,227.063a27.055,27.055,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.878,28.878,0,0,1,0-9,.3.3,0,0,1,.6,0,27.018,27.018,0,0,1,.353,4.5Zm7.934-.8a28.539,28.539,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.171,30.171,0,0,1,0-9,.289.289,0,0,1,.572,0,28.5,28.5,0,0,1,.338,4.5Zm7.852-1.375a29.365,29.365,0,0,1-.322,4.5.278.278,0,0,1-.271.249.283.283,0,0,1-.274-.249,31.577,31.577,0,0,1,0-9,.286.286,0,0,1,.274-.249.28.28,0,0,1,.271.249,29.435,29.435,0,0,1,.322,4.5Zm7.715-2.014a31.232,31.232,0,0,1-.3,4.5.274.274,0,0,1-.262.249.271.271,0,0,1-.259-.249,33.12,33.12,0,0,1,0-9,.271.271,0,0,1,.259-.249.274.274,0,0,1,.262.249,31.158,31.158,0,0,1,.3,4.5Zm7.49-2.738a32.65,32.65,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.831,34.831,0,0,1,0-9,.248.248,0,1,1,.5,0,32.73,32.73,0,0,1,.292,4.5Zm7.113-3.6a34.386,34.386,0,0,1-.277,4.5.259.259,0,0,1-.234.249.264.264,0,0,1-.234-.249,36.72,36.72,0,0,1,0-9,.261.261,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.3,34.3,0,0,1,.277,4.5Zm6.435-4.7a36.228,36.228,0,0,1-.262,4.5.254.254,0,0,1-.222.249c-.11,0-.2-.106-.219-.249a38.4,38.4,0,0,1,0-9c.018-.146.11-.249.219-.249s.2.1.222.249a36.176,36.176,0,0,1,.262,4.5Zm-61.835,15.23a27.052,27.052,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.127,29.127,0,0,0,0-9,.3.3,0,0,0-.6,0,27.014,27.014,0,0,0-.353,4.5Zm-7.934-.8a28.536,28.536,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.44,30.44,0,0,0,0-9,.289.289,0,0,0-.572,0,28.5,28.5,0,0,0-.338,4.5Zm-7.855-1.375a29.753,29.753,0,0,0,.322,4.5.275.275,0,0,0,.548,0,31.87,31.87,0,0,0,0-9,.275.275,0,0,0-.548,0,29.824,29.824,0,0,0-.322,4.5Zm-7.715-2.014a31.194,31.194,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.117,33.117,0,0,0,0-9,.26.26,0,0,0-.52,0,31.12,31.12,0,0,0-.307,4.5Zm-7.487-2.738a32.64,32.64,0,0,0,.292,4.5.267.267,0,0,0,.246.249.263.263,0,0,0,.246-.249,34.837,34.837,0,0,0,0-9,.263.263,0,0,0-.246-.249.267.267,0,0,0-.246.249,32.72,32.72,0,0,0-.292,4.5Zm-7.116-3.6a34.931,34.931,0,0,0,.277,4.5.236.236,0,1,0,.472,0,36.736,36.736,0,0,0,0-9,.236.236,0,1,0-.472,0,34.844,34.844,0,0,0-.277,4.5Zm-6.431-4.7a36.21,36.21,0,0,0,.262,4.5c.018.143.109.249.219.249a.254.254,0,0,0,.222-.249,38.841,38.841,0,0,0,0-9c-.018-.146-.11-.249-.222-.249s-.2.1-.219.249a36.159,36.159,0,0,0-.262,4.5Zm52.507,15.5a26.309,26.309,0,0,0,.368,4.5.309.309,0,0,0,.31.25.312.312,0,0,0,.313-.25,27.923,27.923,0,0,0,0-9,.312.312,0,0,0-.313-.249.309.309,0,0,0-.31.249,26.319,26.319,0,0,0-.368,4.5Zm-57.6-21.616a38.967,38.967,0,0,0,.246,4.5c.018.146.106.249.21.249s.192-.1.207-.249a40.734,40.734,0,0,0,0-9c-.015-.146-.1-.249-.207-.249s-.192.1-.21.249a38.912,38.912,0,0,0-.246,4.5Zm116.554,0a38.964,38.964,0,0,1-.246,4.5c-.018.146-.107.249-.21.249s-.192-.1-.21-.249a41.23,41.23,0,0,1,0-9c.018-.146.106-.249.21-.249s.192.1.21.249a38.909,38.909,0,0,1,.246,4.5Z" transform="translate(-192.611 -74.822)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11569" data-name="Path 11569" d="M205.356,218.179c0,16.836,26.547,30.484,59.3,30.484s59.295-13.648,59.295-30.484-26.547-30.487-59.295-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-193.61 -101.944)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11570" data-name="Path 11570" d="M205.192,217.706c0,16.651,26.979,30.149,60.262,30.149s60.265-13.5,60.265-30.149-26.979-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-193.945 -102.226)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11571" data-name="Path 11571" d="M207.065,214.127c-.389,14.862,23.73,27.551,53.87,28.339s54.889-10.618,55.279-25.482-23.73-27.551-53.87-28.339-54.889,10.618-55.279,25.482Z" transform="translate(-190.129 -100.077)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11572" data-name="Path 11572" d="M207.187,215.668c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.262-27.116-54.2-27.116-54.193,12.139-54.193,27.116Z" transform="translate(-189.87 -100.188)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11573" data-name="Path 11573" d="M315.472,217.341c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.961,13.961,0,0,1-.1-1.673c0-14.977,24.262-27.116,54.193-27.116s54.2,12.139,54.2,27.116a13.361,13.361,0,0,1-.1,1.673Z" transform="translate(-189.87 -100.188)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11574" data-name="Path 11574" d="M208.132,215.875c0,14.807,23.992,26.809,53.584,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.584,12-53.584,26.809Z" transform="translate(-187.94 -99.138)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11575" data-name="Path 11575" d="M202.973,205.945c0,16.59,26.982,30.04,60.265,30.04s60.265-13.45,60.265-30.04l0-13.128H202.973v13.128Z" transform="translate(-198.476 -91.477)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11576" data-name="Path 11576" d="M236.13,205.446q3.856.256,7.855.256a117.9,117.9,0,0,0,15.823-1.05v13.1a89.873,89.873,0,0,1-15.823,1.047c-8.528,0-22.4-1.631-31.585-4.454V201.232a103.813,103.813,0,0,0,23.73,4.214Z" transform="translate(-179.224 -74.291)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11577" data-name="Path 11577" d="M236.281,214.684c11.695-5.449,19.051-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V214.684Z" transform="translate(-130.451 -91.361)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11578" data-name="Path 11578" d="M215.349,211.04v13.143c-7.764-5.056-12.376-11.378-12.376-18.239V192.817c.04,6.854,4.646,13.17,12.376,18.223Z" transform="translate(-198.476 -91.477)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11579" data-name="Path 11579" d="M232.047,204.343q3.856.256,7.855.256v13.1a112.964,112.964,0,0,1-25.5-2.817V201.772a108.483,108.483,0,0,0,17.648,2.571Z" transform="translate(-175.141 -73.189)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11580" data-name="Path 11580" d="M270.552,222.391a27.344,27.344,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.879,28.879,0,0,1,0-9,.3.3,0,0,1,.6,0,27.411,27.411,0,0,1,.353,4.5Zm7.931-.8a28.584,28.584,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.186,30.186,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.584,28.584,0,0,1,.335,4.5Zm7.855-1.378a29.79,29.79,0,0,1-.322,4.5.273.273,0,0,1-.545,0,31.574,31.574,0,0,1,0-9,.273.273,0,0,1,.545,0,29.778,29.778,0,0,1,.322,4.5Zm7.715-2.011a31.194,31.194,0,0,1-.307,4.5.271.271,0,0,1-.259.246.274.274,0,0,1-.262-.246,33.468,33.468,0,0,1,0-9.005.274.274,0,0,1,.262-.249.271.271,0,0,1,.259.249,31.162,31.162,0,0,1,.307,4.5Zm7.487-2.741a33.2,33.2,0,0,1-.289,4.5.248.248,0,1,1-.5,0,34.828,34.828,0,0,1,0-9,.248.248,0,1,1,.5,0,33.154,33.154,0,0,1,.289,4.5Zm7.116-3.6a34.312,34.312,0,0,1-.277,4.5.235.235,0,1,1-.469,0,36.736,36.736,0,0,1,0-9,.235.235,0,1,1,.469,0,34.363,34.363,0,0,1,.277,4.5Zm6.435-4.7a36.177,36.177,0,0,1-.262,4.5c-.018.146-.11.249-.222.249a.254.254,0,0,1-.222-.249,38.85,38.85,0,0,1,0-9,.254.254,0,0,1,.222-.249c.113,0,.2.1.222.249a36.228,36.228,0,0,1,.262,4.5Zm-61.835,15.23a27.365,27.365,0,0,0,.35,4.5.3.3,0,0,0,.6,0,28.876,28.876,0,0,0,0-9,.3.3,0,0,0-.6,0,27.432,27.432,0,0,0-.35,4.5Zm-7.934-.8a28.2,28.2,0,0,0,.338,4.5.287.287,0,0,0,.569,0,30.188,30.188,0,0,0,0-9.005.287.287,0,0,0-.569,0,28.2,28.2,0,0,0-.338,4.5Zm-7.855-1.378a29.79,29.79,0,0,0,.323,4.5.273.273,0,0,0,.545,0,31.568,31.568,0,0,0,0-9,.273.273,0,0,0-.545,0,29.777,29.777,0,0,0-.323,4.5Zm-7.715-2.011a31.2,31.2,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.134,33.134,0,0,0,0-9.005.26.26,0,0,0-.52,0,31.166,31.166,0,0,0-.307,4.5Zm-7.487-2.741a32.682,32.682,0,0,0,.292,4.5.265.265,0,0,0,.246.249.27.27,0,0,0,.246-.249,34.829,34.829,0,0,0,0-9,.267.267,0,0,0-.246-.249.263.263,0,0,0-.246.249,32.637,32.637,0,0,0-.292,4.5Zm-7.116-3.6a34.314,34.314,0,0,0,.277,4.5.261.261,0,0,0,.234.249.257.257,0,0,0,.234-.249,36.729,36.729,0,0,0,0-9,.257.257,0,0,0-.234-.249.262.262,0,0,0-.234.249,34.364,34.364,0,0,0-.277,4.5Zm-6.435-4.7a36.782,36.782,0,0,0,.262,4.5.224.224,0,1,0,.444,0,38.848,38.848,0,0,0,0-9,.224.224,0,1,0-.444,0,36.834,36.834,0,0,0-.262,4.5Zm52.51,15.5a26.043,26.043,0,0,0,.368,4.5.318.318,0,0,0,.621,0,27.7,27.7,0,0,0,0-9,.318.318,0,0,0-.621,0,25.982,25.982,0,0,0-.368,4.5Zm-57.6-21.613a38.335,38.335,0,0,0,.246,4.5c.018.143.1.246.21.246s.192-.1.207-.246a41.256,41.256,0,0,0,0-9.005c-.015-.143-.1-.249-.207-.249s-.192.107-.21.249a38.335,38.335,0,0,0-.246,4.5Zm116.551,0a38.332,38.332,0,0,1-.246,4.5c-.015.143-.1.246-.207.246s-.192-.1-.21-.246a41.259,41.259,0,0,1,0-9.005c.018-.143.1-.249.21-.249s.192.107.207.249a38.332,38.332,0,0,1,.246,4.5Z" transform="translate(-197.141 -84.37)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11581" data-name="Path 11581" d="M203.137,213.5c0,16.839,26.55,30.487,59.3,30.487s59.3-13.648,59.3-30.487-26.547-30.484-59.3-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-198.142 -111.49)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11582" data-name="Path 11582" d="M202.973,213.031c0,16.654,26.982,30.152,60.265,30.152s60.265-13.5,60.265-30.152-26.982-30.152-60.265-30.152-60.265,13.5-60.265,30.152Z" transform="translate(-198.476 -111.774)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11583" data-name="Path 11583" d="M204.846,209.452c-.386,14.865,23.73,27.554,53.873,28.342S313.609,227.174,314,212.312s-23.73-27.551-53.87-28.342-54.893,10.621-55.282,25.483Z" transform="translate(-194.66 -109.625)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11584" data-name="Path 11584" d="M204.969,210.993c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.266-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-194.4 -109.736)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_11585" data-name="Path 11585" d="M204.459,211.421c0,14.807,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.581,12-53.581,26.809Z" transform="translate(-195.442 -108.235)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11586" data-name="Path 11586" d="M313.254,212.666c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.912,13.912,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.913,13.913,0,0,1-.1,1.673Z" transform="translate(-194.4 -109.736)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11587" data-name="Path 11587" d="M205.192,201.271c0,16.59,26.979,30.037,60.262,30.037s60.265-13.447,60.265-30.037l0-13.128H205.192v13.128Z" transform="translate(-193.945 -101.023)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11588" data-name="Path 11588" d="M238.346,200.772q3.856.251,7.855.252a117.842,117.842,0,0,0,15.823-1.05v13.1a89.833,89.833,0,0,1-15.823,1.047c-8.528,0-22.4-1.628-31.582-4.451V196.558a104.088,104.088,0,0,0,23.727,4.214Z" transform="translate(-174.692 -83.837)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11589" data-name="Path 11589" d="M238.5,210.012c11.7-5.452,19.054-13.2,19.194-21.813l0,12.957c0,8.68-7.387,16.5-19.2,21.984V210.012Z" transform="translate(-125.921 -100.909)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11590" data-name="Path 11590" d="M217.565,206.366v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V188.143c.036,6.851,4.643,13.167,12.373,18.223Z" transform="translate(-193.945 -101.023)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11591" data-name="Path 11591" d="M234.265,199.669q3.856.251,7.855.252v13.1a112.982,112.982,0,0,1-25.5-2.817V197.1a108.527,108.527,0,0,0,17.648,2.571Z" transform="translate(-170.611 -82.734)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11592" data-name="Path 11592" d="M272.77,217.714a27.056,27.056,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.879,28.879,0,0,1,0-9,.3.3,0,0,1,.6,0,27.017,27.017,0,0,1,.353,4.5Zm7.934-.8a28.539,28.539,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.17,30.17,0,0,1,0-9,.289.289,0,0,1,.572,0,28.5,28.5,0,0,1,.338,4.5Zm7.852-1.375a29.435,29.435,0,0,1-.322,4.5.278.278,0,0,1-.271.246.283.283,0,0,1-.274-.246,31.6,31.6,0,0,1,0-9.005.286.286,0,0,1,.274-.249.28.28,0,0,1,.271.249,29.435,29.435,0,0,1,.322,4.5Zm7.715-2.014a31.2,31.2,0,0,1-.3,4.5.274.274,0,0,1-.262.249.271.271,0,0,1-.259-.249,33.12,33.12,0,0,1,0-9,.271.271,0,0,1,.259-.249.274.274,0,0,1,.262.249,31.157,31.157,0,0,1,.3,4.5Zm7.49-2.738a32.651,32.651,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.832,34.832,0,0,1,0-9,.248.248,0,1,1,.5,0,32.73,32.73,0,0,1,.292,4.5Zm7.113-3.6a34.387,34.387,0,0,1-.277,4.5.257.257,0,0,1-.234.249.261.261,0,0,1-.234-.249,36.72,36.72,0,0,1,0-9,.262.262,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.3,34.3,0,0,1,.277,4.5Zm6.435-4.7a36.229,36.229,0,0,1-.262,4.5c-.018.146-.11.249-.222.249s-.2-.1-.219-.249a38.405,38.405,0,0,1,0-9c.018-.146.11-.249.219-.249s.2.1.222.249a36.212,36.212,0,0,1,.262,4.5Zm-61.835,15.23a27.053,27.053,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.128,29.128,0,0,0,0-9,.3.3,0,0,0-.6,0,27.014,27.014,0,0,0-.353,4.5Zm-7.934-.8a28.536,28.536,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.44,30.44,0,0,0,0-9,.289.289,0,0,0-.572,0,28.5,28.5,0,0,0-.338,4.5Zm-7.855-1.375a29.824,29.824,0,0,0,.322,4.5.275.275,0,0,0,.548,0,31.891,31.891,0,0,0,0-9.005.275.275,0,0,0-.548,0,29.824,29.824,0,0,0-.322,4.5Zm-7.715-2.014a31.162,31.162,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.117,33.117,0,0,0,0-9,.26.26,0,0,0-.52,0,31.119,31.119,0,0,0-.307,4.5Zm-7.487-2.738a32.641,32.641,0,0,0,.292,4.5.267.267,0,0,0,.246.249.263.263,0,0,0,.246-.249,34.837,34.837,0,0,0,0-9,.265.265,0,0,0-.246-.249.27.27,0,0,0-.246.249,32.72,32.72,0,0,0-.292,4.5Zm-7.116-3.6a34.931,34.931,0,0,0,.277,4.5.236.236,0,1,0,.472,0,36.736,36.736,0,0,0,0-9,.236.236,0,1,0-.472,0,34.845,34.845,0,0,0-.277,4.5Zm-6.431-4.7a36.212,36.212,0,0,0,.262,4.5c.018.146.109.249.219.249s.2-.1.222-.249a38.841,38.841,0,0,0,0-9c-.018-.146-.11-.249-.222-.249s-.2.1-.219.249a36.2,36.2,0,0,0-.262,4.5Zm52.507,15.5a26.282,26.282,0,0,0,.368,4.5.309.309,0,0,0,.31.249.312.312,0,0,0,.313-.249,27.923,27.923,0,0,0,0-9,.312.312,0,0,0-.313-.249.309.309,0,0,0-.31.249,26.345,26.345,0,0,0-.368,4.5Zm-57.6-21.613a38.951,38.951,0,0,0,.246,4.5c.018.146.106.249.21.249s.192-.1.207-.249a40.733,40.733,0,0,0,0-9c-.015-.146-.1-.249-.207-.249s-.192.1-.21.249a38.967,38.967,0,0,0-.246,4.5Zm116.554,0a38.949,38.949,0,0,1-.246,4.5c-.018.146-.107.249-.21.249s-.192-.1-.21-.249a41.23,41.23,0,0,1,0-9c.018-.146.106-.249.21-.249s.192.1.21.249a38.964,38.964,0,0,1,.246,4.5Z" transform="translate(-192.611 -93.916)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11593" data-name="Path 11593" d="M205.356,208.83c0,16.836,26.547,30.487,59.3,30.487s59.295-13.651,59.295-30.487-26.547-30.487-59.295-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-193.61 -121.038)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11594" data-name="Path 11594" d="M205.192,208.357c0,16.651,26.979,30.149,60.262,30.149s60.265-13.5,60.265-30.149S298.74,178.2,265.454,178.2s-60.262,13.5-60.262,30.152Z" transform="translate(-193.945 -121.32)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11595" data-name="Path 11595" d="M207.065,204.778c-.389,14.862,23.73,27.551,53.87,28.342s54.889-10.621,55.279-25.485-23.73-27.551-53.87-28.339-54.889,10.618-55.279,25.482Z" transform="translate(-190.129 -119.171)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11596" data-name="Path 11596" d="M207.187,206.319c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113S291.313,179.2,261.38,179.2s-54.193,12.139-54.193,27.116Z" transform="translate(-189.87 -119.282)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11597" data-name="Path 11597" d="M206.8,206.37c0,14.8,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.581,12-53.581,26.809Z" transform="translate(-190.658 -118.55)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11598" data-name="Path 11598" d="M315.472,207.992c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.961,13.961,0,0,1-.1-1.673c0-14.977,24.262-27.116,54.193-27.116s54.2,12.139,54.2,27.116a13.361,13.361,0,0,1-.1,1.673Z" transform="translate(-189.87 -119.282)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11599" data-name="Path 11599" d="M201.495,196.6c0,16.59,26.979,30.04,60.262,30.04s60.265-13.45,60.265-30.04l0-13.128H201.495V196.6Z" transform="translate(-201.495 -110.571)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11600" data-name="Path 11600" d="M234.651,196.1q3.856.251,7.855.252a117.862,117.862,0,0,0,15.823-1.05v13.1a89.856,89.856,0,0,1-15.823,1.047c-8.528,0-22.4-1.631-31.585-4.451V191.883a103.975,103.975,0,0,0,23.73,4.217Z" transform="translate(-182.244 -93.385)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11601" data-name="Path 11601" d="M234.8,205.335c11.695-5.449,19.054-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V205.335Z" transform="translate(-133.472 -110.455)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11602" data-name="Path 11602" d="M213.868,201.691v13.143c-7.764-5.056-12.373-11.378-12.373-18.239V183.468c.037,6.854,4.643,13.17,12.373,18.223Z" transform="translate(-201.495 -110.571)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11603" data-name="Path 11603" d="M230.568,195q3.856.251,7.855.253v13.1a112.984,112.984,0,0,1-25.5-2.817V192.423A108.816,108.816,0,0,0,230.568,195Z" transform="translate(-178.161 -92.282)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11604" data-name="Path 11604" d="M269.073,213.04a27.411,27.411,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.9,28.9,0,0,1,0-9.005.3.3,0,0,1,.6,0,27.41,27.41,0,0,1,.353,4.5Zm7.931-.8a28.587,28.587,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.185,30.185,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.587,28.587,0,0,1,.335,4.5Zm7.855-1.375a29.777,29.777,0,0,1-.322,4.5.273.273,0,0,1-.545,0,31.565,31.565,0,0,1,0-9,.273.273,0,0,1,.545,0,29.79,29.79,0,0,1,.322,4.5Zm7.715-2.014a31.2,31.2,0,0,1-.307,4.5.271.271,0,0,1-.259.246.276.276,0,0,1-.262-.246,33.465,33.465,0,0,1,0-9.005.276.276,0,0,1,.262-.249.271.271,0,0,1,.259.249,31.2,31.2,0,0,1,.307,4.5Zm7.49-2.741a32.688,32.688,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.835,34.835,0,0,1,0-9,.248.248,0,1,1,.5,0,32.674,32.674,0,0,1,.292,4.5Zm7.113-3.6a34.319,34.319,0,0,1-.277,4.5.235.235,0,1,1-.469,0,36.732,36.732,0,0,1,0-9,.235.235,0,1,1,.469,0,34.367,34.367,0,0,1,.277,4.5Zm6.434-4.7a36.252,36.252,0,0,1-.262,4.5.223.223,0,1,1-.444,0,38.876,38.876,0,0,1,0-9.005.224.224,0,1,1,.444,0,36.253,36.253,0,0,1,.262,4.5Zm-61.835,15.23a27.432,27.432,0,0,0,.35,4.5.3.3,0,0,0,.3.246.3.3,0,0,0,.3-.246,29.145,29.145,0,0,0,0-9.005.307.307,0,0,0-.3-.249.3.3,0,0,0-.3.249,27.431,27.431,0,0,0-.35,4.5Zm-7.934-.8a28.56,28.56,0,0,0,.338,4.5.293.293,0,0,0,.286.249.29.29,0,0,0,.283-.249,30.185,30.185,0,0,0,0-9.005.29.29,0,0,0-.283-.249.293.293,0,0,0-.286.249,28.56,28.56,0,0,0-.338,4.5Zm-7.855-1.375a29.785,29.785,0,0,0,.322,4.5.273.273,0,0,0,.545,0,31.574,31.574,0,0,0,0-9,.273.273,0,0,0-.545,0,29.8,29.8,0,0,0-.322,4.5Zm-7.715-2.014a31.209,31.209,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.143,33.143,0,0,0,0-9.005.26.26,0,0,0-.52,0,31.208,31.208,0,0,0-.307,4.5Zm-7.487-2.741a32.685,32.685,0,0,0,.292,4.5.246.246,0,1,0,.493,0,34.824,34.824,0,0,0,0-9,.246.246,0,1,0-.493,0,32.671,32.671,0,0,0-.292,4.5Zm-7.116-3.6a34.842,34.842,0,0,0,.277,4.5.262.262,0,0,0,.234.249.257.257,0,0,0,.234-.249,36.724,36.724,0,0,0,0-9,.259.259,0,0,0-.234-.249.264.264,0,0,0-.234.249,34.891,34.891,0,0,0-.277,4.5Zm-6.434-4.7a36.868,36.868,0,0,0,.262,4.5.223.223,0,1,0,.444,0,38.876,38.876,0,0,0,0-9.005.224.224,0,1,0-.444,0,36.868,36.868,0,0,0-.262,4.5Zm52.51,15.5a26.014,26.014,0,0,0,.368,4.5.318.318,0,0,0,.621,0,27.7,27.7,0,0,0,0-9,.318.318,0,0,0-.621,0,25.978,25.978,0,0,0-.368,4.5Zm-57.6-21.613a39,39,0,0,0,.246,4.5c.018.143.1.249.21.249s.192-.106.207-.249a41.258,41.258,0,0,0,0-9.005c-.015-.143-.1-.246-.207-.246s-.192.1-.21.246a39,39,0,0,0-.246,4.5Zm116.554,0a39.012,39.012,0,0,1-.246,4.5c-.018.143-.106.249-.21.249s-.192-.106-.21-.249a41.248,41.248,0,0,1,0-9.005c.018-.143.1-.246.21-.246s.192.1.21.246a39.013,39.013,0,0,1,.246,4.5Z" transform="translate(-200.161 -103.462)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11605" data-name="Path 11605" d="M201.659,204.156c0,16.836,26.547,30.484,59.295,30.484s59.3-13.648,59.3-30.484-26.547-30.487-59.3-30.487-59.295,13.648-59.295,30.487Z" transform="translate(-201.16 -130.584)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11606" data-name="Path 11606" d="M201.495,203.682c0,16.654,26.979,30.152,60.262,30.152s60.265-13.5,60.265-30.152S295.04,173.53,261.757,173.53s-60.262,13.5-60.262,30.152Z" transform="translate(-201.495 -130.868)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11607" data-name="Path 11607" d="M203.368,200.1c-.389,14.865,23.727,27.554,53.87,28.342s54.889-10.621,55.279-25.482-23.73-27.551-53.87-28.342-54.89,10.621-55.279,25.482Z" transform="translate(-197.679 -128.718)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11608" data-name="Path 11608" d="M203.49,201.642c0,14.974,24.262,27.116,54.193,27.116s54.2-12.142,54.2-27.116-24.266-27.113-54.2-27.113-54.193,12.139-54.193,27.113Z" transform="translate(-197.421 -128.827)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11609" data-name="Path 11609" d="M204.321,201.579c0,14.807,23.989,26.809,53.581,26.809s53.584-12,53.584-26.809S287.495,174.77,257.9,174.77s-53.581,12-53.581,26.809Z" transform="translate(-195.723 -128.335)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11610" data-name="Path 11610" d="M311.775,203.315c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.919,13.919,0,0,1-.1-1.673c0-14.974,24.262-27.113,54.193-27.113s54.2,12.139,54.2,27.113a13.933,13.933,0,0,1-.1,1.673Z" transform="translate(-197.421 -128.827)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11611" data-name="Path 11611" d="M205.192,191.922c0,16.59,26.979,30.037,60.262,30.037s60.265-13.447,60.265-30.037l0-13.128H205.192v13.128Z" transform="translate(-193.945 -120.117)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11612" data-name="Path 11612" d="M238.346,191.423q3.856.251,7.855.253a117.853,117.853,0,0,0,15.823-1.05v13.1a89.888,89.888,0,0,1-15.823,1.044c-8.528,0-22.4-1.628-31.582-4.451V187.209a103.918,103.918,0,0,0,23.727,4.214Z" transform="translate(-174.692 -102.931)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11613" data-name="Path 11613" d="M238.5,200.663c11.7-5.452,19.054-13.2,19.194-21.813l0,12.957c0,8.68-7.387,16.5-19.2,21.984V200.663Z" transform="translate(-125.921 -120.002)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11614" data-name="Path 11614" d="M217.565,197.017v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V178.794c.036,6.854,4.643,13.167,12.373,18.223Z" transform="translate(-193.945 -120.117)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11615" data-name="Path 11615" d="M234.265,190.32q3.856.251,7.855.253v13.1a112.978,112.978,0,0,1-25.5-2.817v-13.1a108.525,108.525,0,0,0,17.648,2.571Z" transform="translate(-170.611 -101.828)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11616" data-name="Path 11616" d="M272.77,208.368a27.044,27.044,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.878,28.878,0,0,1,0-9,.3.3,0,0,1,.6,0,27.056,27.056,0,0,1,.353,4.5Zm7.934-.8a28.5,28.5,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.171,30.171,0,0,1,0-9,.289.289,0,0,1,.572,0,28.54,28.54,0,0,1,.338,4.5Zm7.852-1.378a29.436,29.436,0,0,1-.322,4.5.281.281,0,0,1-.271.249.286.286,0,0,1-.274-.249,31.6,31.6,0,0,1,0-9.005.283.283,0,0,1,.274-.246.278.278,0,0,1,.271.246,29.436,29.436,0,0,1,.322,4.5Zm7.715-2.014a31.2,31.2,0,0,1-.3,4.5.274.274,0,0,1-.262.249.271.271,0,0,1-.259-.249,33.12,33.12,0,0,1,0-9,.271.271,0,0,1,.259-.249.274.274,0,0,1,.262.249,31.155,31.155,0,0,1,.3,4.5Zm7.49-2.738a32.729,32.729,0,0,1-.292,4.5.248.248,0,0,1-.5,0,34.855,34.855,0,0,1,0-9.005.248.248,0,1,1,.5,0,32.731,32.731,0,0,1,.292,4.5Zm7.113-3.6a34.351,34.351,0,0,1-.277,4.5.257.257,0,0,1-.234.249.262.262,0,0,1-.234-.249,36.719,36.719,0,0,1,0-9,.261.261,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.3,34.3,0,0,1,.277,4.5Zm6.435-4.7a36.213,36.213,0,0,1-.262,4.5c-.018.146-.11.249-.222.249s-.2-.1-.219-.249a38.405,38.405,0,0,1,0-9c.018-.146.11-.249.219-.249s.2.1.222.249a36.229,36.229,0,0,1,.262,4.5Zm-61.835,15.23a27.04,27.04,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.127,29.127,0,0,0,0-9,.3.3,0,0,0-.6,0,27.052,27.052,0,0,0-.353,4.5Zm-7.934-.8a28.5,28.5,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.44,30.44,0,0,0,0-9,.289.289,0,0,0-.572,0,28.536,28.536,0,0,0-.338,4.5Zm-7.855-1.378a29.824,29.824,0,0,0,.322,4.5.275.275,0,0,0,.548,0,31.891,31.891,0,0,0,0-9.005.275.275,0,0,0-.548,0,29.825,29.825,0,0,0-.322,4.5Zm-7.715-2.014a31.162,31.162,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.116,33.116,0,0,0,0-9,.26.26,0,0,0-.52,0,31.117,31.117,0,0,0-.307,4.5Zm-7.487-2.738a32.719,32.719,0,0,0,.292,4.5.267.267,0,0,0,.246.246.262.262,0,0,0,.246-.246,34.86,34.86,0,0,0,0-9.005.265.265,0,0,0-.246-.249.27.27,0,0,0-.246.249,32.721,32.721,0,0,0-.292,4.5Zm-7.116-3.6a34.894,34.894,0,0,0,.277,4.5.236.236,0,1,0,.472,0,36.736,36.736,0,0,0,0-9,.236.236,0,1,0-.472,0,34.844,34.844,0,0,0-.277,4.5Zm-6.431-4.7a36.2,36.2,0,0,0,.262,4.5c.018.146.109.249.219.249s.2-.1.222-.249a38.841,38.841,0,0,0,0-9c-.018-.146-.11-.249-.222-.249s-.2.1-.219.249a36.211,36.211,0,0,0-.262,4.5Zm52.507,15.5a26.345,26.345,0,0,0,.368,4.5.309.309,0,0,0,.31.246.313.313,0,0,0,.313-.246,27.941,27.941,0,0,0,0-9.005.315.315,0,0,0-.313-.249.312.312,0,0,0-.31.249,26.345,26.345,0,0,0-.368,4.5Zm-57.6-21.613a38.914,38.914,0,0,0,.246,4.5c.018.146.106.249.21.249s.192-.1.207-.249a40.734,40.734,0,0,0,0-9c-.015-.146-.1-.249-.207-.249s-.192.1-.21.249a38.967,38.967,0,0,0-.246,4.5Zm116.554,0a38.911,38.911,0,0,1-.246,4.5c-.018.146-.107.249-.21.249s-.192-.1-.21-.249a41.231,41.231,0,0,1,0-9c.018-.146.106-.249.21-.249s.192.1.21.249a38.965,38.965,0,0,1,.246,4.5Z" transform="translate(-192.611 -113.01)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11617" data-name="Path 11617" d="M205.356,199.481c0,16.836,26.547,30.487,59.3,30.487s59.295-13.651,59.295-30.487-26.547-30.487-59.295-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-193.61 -140.132)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11618" data-name="Path 11618" d="M205.192,199.008c0,16.651,26.979,30.149,60.262,30.149s60.265-13.5,60.265-30.149-26.979-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-193.945 -140.413)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11619" data-name="Path 11619" d="M207.065,195.429c-.389,14.862,23.73,27.551,53.87,28.342s54.889-10.621,55.279-25.486-23.73-27.551-53.87-28.339-54.889,10.621-55.279,25.482Z" transform="translate(-190.129 -138.264)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11620" data-name="Path 11620" d="M207.187,196.97c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.262-27.116-54.2-27.116-54.193,12.139-54.193,27.116Z" transform="translate(-189.87 -138.375)" fill="#9e9e9e" fill-rule="evenodd"/>
      <path id="Path_11621" data-name="Path 11621" d="M315.472,198.643c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.922,13.922,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.325,13.325,0,0,1-.1,1.673Z" transform="translate(-189.87 -138.375)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11622" data-name="Path 11622" d="M205.913,197.046c0,14.807,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.806-53.581-26.806-53.581,12-53.581,26.806Z" transform="translate(-192.472 -137.587)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11623" data-name="Path 11623" d="M206.671,187.245c0,16.59,26.979,30.04,60.262,30.04s60.265-13.45,60.265-30.04l0-13.125H206.671v13.125Z" transform="translate(-190.924 -129.663)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11624" data-name="Path 11624" d="M239.827,186.751q3.856.251,7.855.253a117.863,117.863,0,0,0,15.823-1.05v13.1a89.832,89.832,0,0,1-15.823,1.047c-8.528,0-22.4-1.628-31.585-4.451V182.534a103.974,103.974,0,0,0,23.73,4.217Z" transform="translate(-171.673 -112.479)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11625" data-name="Path 11625" d="M239.978,195.989c11.695-5.452,19.054-13.2,19.194-21.813l0,12.954c0,8.68-7.387,16.5-19.2,21.984V195.989Z" transform="translate(-122.901 -129.548)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11626" data-name="Path 11626" d="M219.044,192.34v13.143c-7.761-5.056-12.373-11.378-12.373-18.239V174.12c.036,6.851,4.643,13.167,12.373,18.22Z" transform="translate(-190.924 -129.663)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11627" data-name="Path 11627" d="M235.744,185.648q3.856.251,7.855.253V199a112.98,112.98,0,0,1-25.5-2.817V183.074a108.8,108.8,0,0,0,17.648,2.574Z" transform="translate(-167.59 -111.376)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11628" data-name="Path 11628" d="M274.249,203.691a27.408,27.408,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.906,28.906,0,0,1,0-9.005.3.3,0,0,1,.6,0,27.407,27.407,0,0,1,.353,4.5Zm7.931-.8a28.587,28.587,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.19,30.19,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.586,28.586,0,0,1,.335,4.5Zm7.855-1.375a29.786,29.786,0,0,1-.322,4.5.278.278,0,0,1-.271.249.281.281,0,0,1-.274-.249,31.571,31.571,0,0,1,0-9,.281.281,0,0,1,.274-.249.278.278,0,0,1,.271.249,29.8,29.8,0,0,1,.322,4.5Zm7.715-2.014a31.212,31.212,0,0,1-.307,4.5.259.259,0,0,1-.517,0,33.146,33.146,0,0,1,0-9.005.259.259,0,0,1,.517,0,31.212,31.212,0,0,1,.307,4.5Zm7.49-2.738a32.669,32.669,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.832,34.832,0,0,1,0-9,.248.248,0,1,1,.5,0,32.682,32.682,0,0,1,.292,4.5Zm7.113-3.6a34.408,34.408,0,0,1-.277,4.5.235.235,0,1,1-.469,0,36.745,36.745,0,0,1,0-9.005.235.235,0,1,1,.469,0,34.408,34.408,0,0,1,.277,4.5Zm6.435-4.7a36.265,36.265,0,0,1-.262,4.5.223.223,0,1,1-.444,0,38.872,38.872,0,0,1,0-9.005.223.223,0,1,1,.444,0,36.266,36.266,0,0,1,.262,4.5Zm-61.835,15.23a27.429,27.429,0,0,0,.35,4.5.307.307,0,0,0,.3.249.3.3,0,0,0,.3-.249,29.15,29.15,0,0,0,0-9.005.3.3,0,0,0-.3-.246.3.3,0,0,0-.3.246,27.428,27.428,0,0,0-.35,4.5Zm-7.934-.8a28.565,28.565,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.457,30.457,0,0,0,0-9.005.289.289,0,0,0-.572,0,28.564,28.564,0,0,0-.338,4.5Zm-7.855-1.375a29.778,29.778,0,0,0,.322,4.5.283.283,0,0,0,.274.249.278.278,0,0,0,.271-.249,31.574,31.574,0,0,0,0-9,.278.278,0,0,0-.271-.249.283.283,0,0,0-.274.249,29.79,29.79,0,0,0-.322,4.5Zm-7.715-2.014a31.2,31.2,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.146,33.146,0,0,0,0-9.005.26.26,0,0,0-.52,0,31.2,31.2,0,0,0-.307,4.5Zm-7.487-2.738a32.672,32.672,0,0,0,.292,4.5.246.246,0,1,0,.493,0,34.832,34.832,0,0,0,0-9,.246.246,0,1,0-.493,0,32.685,32.685,0,0,0-.292,4.5Zm-7.116-3.6a34.941,34.941,0,0,0,.277,4.5.261.261,0,0,0,.234.246.256.256,0,0,0,.234-.246,36.751,36.751,0,0,0,0-9.005.259.259,0,0,0-.234-.249.264.264,0,0,0-.234.249,34.942,34.942,0,0,0-.277,4.5Zm-6.434-4.7a36.853,36.853,0,0,0,.262,4.5.223.223,0,1,0,.444,0,38.876,38.876,0,0,0,0-9.005.223.223,0,1,0-.444,0,36.853,36.853,0,0,0-.262,4.5Zm52.51,15.5a26.316,26.316,0,0,0,.368,4.5.306.306,0,0,0,.31.249.312.312,0,0,0,.313-.249,27.937,27.937,0,0,0,0-9,.312.312,0,0,0-.313-.249.306.306,0,0,0-.31.249,26.306,26.306,0,0,0-.368,4.5Zm-57.6-21.613a39.027,39.027,0,0,0,.246,4.5c.018.143.1.249.21.249s.192-.107.207-.249a41.269,41.269,0,0,0,0-9.005c-.015-.143-.1-.246-.207-.246s-.192.1-.21.246a39.026,39.026,0,0,0-.246,4.5Zm116.554,0a38.308,38.308,0,0,1-.249,4.5c-.015.143-.1.249-.207.249s-.192-.107-.21-.249a41.263,41.263,0,0,1,0-9.005c.018-.143.107-.246.21-.246s.192.1.207.246a38.307,38.307,0,0,1,.249,4.5Z" transform="translate(-189.59 -122.555)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11629" data-name="Path 11629" d="M206.835,194.807c0,16.836,26.547,30.484,59.295,30.484s59.3-13.648,59.3-30.484-26.547-30.487-59.3-30.487-59.295,13.648-59.295,30.487Z" transform="translate(-190.589 -149.677)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11630" data-name="Path 11630" d="M206.671,194.333c0,16.654,26.979,30.152,60.262,30.152s60.265-13.5,60.265-30.152-26.982-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-190.924 -149.961)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11631" data-name="Path 11631" d="M208.544,190.757c-.389,14.862,23.727,27.551,53.87,28.339s54.889-10.618,55.279-25.482-23.73-27.551-53.87-28.342-54.889,10.621-55.279,25.485Z" transform="translate(-187.108 -147.812)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11632" data-name="Path 11632" d="M208.666,192.293c0,14.977,24.262,27.116,54.193,27.116s54.2-12.139,54.2-27.116-24.266-27.113-54.2-27.113-54.193,12.139-54.193,27.113Z" transform="translate(-186.849 -147.921)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11633" data-name="Path 11633" d="M208.316,192.365c0,14.8,23.989,26.809,53.581,26.809s53.584-12.005,53.584-26.809S291.49,165.556,261.9,165.556s-53.581,12-53.581,26.809Z" transform="translate(-187.564 -147.153)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11634" data-name="Path 11634" d="M316.951,193.966c-1.728-14.192-25.285-25.44-54.092-25.44s-52.364,11.247-54.092,25.44a13.885,13.885,0,0,1-.1-1.673c0-14.974,24.262-27.113,54.193-27.113s54.2,12.139,54.2,27.113a13.29,13.29,0,0,1-.1,1.673Z" transform="translate(-186.849 -147.921)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11635" data-name="Path 11635" d="M202.973,182.573c0,16.59,26.982,30.037,60.265,30.037S323.5,199.162,323.5,182.573l0-13.128H202.973v13.128Z" transform="translate(-198.476 -139.21)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11636" data-name="Path 11636" d="M236.13,182.074q3.856.251,7.855.255a118.393,118.393,0,0,0,15.823-1.05v13.1a89.936,89.936,0,0,1-15.823,1.043c-8.528,0-22.4-1.628-31.585-4.451V177.86a103.981,103.981,0,0,0,23.73,4.214Z" transform="translate(-179.224 -122.024)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11637" data-name="Path 11637" d="M236.281,191.312c11.695-5.449,19.051-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V191.312Z" transform="translate(-130.451 -139.094)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11638" data-name="Path 11638" d="M215.349,187.668v13.14c-7.764-5.056-12.376-11.378-12.376-18.236V169.445c.04,6.854,4.646,13.167,12.376,18.223Z" transform="translate(-198.476 -139.21)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11639" data-name="Path 11639" d="M232.047,180.971q3.856.251,7.855.256V194.32a113.141,113.141,0,0,1-25.5-2.814V178.4a108.478,108.478,0,0,0,17.648,2.571Z" transform="translate(-175.141 -120.922)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11640" data-name="Path 11640" d="M270.552,199.019a27.345,27.345,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.879,28.879,0,0,1,0-9,.3.3,0,0,1,.6,0,27.382,27.382,0,0,1,.353,4.5Zm7.931-.8a28.514,28.514,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.165,30.165,0,0,1,0-9,.289.289,0,0,1,.572,0,28.556,28.556,0,0,1,.335,4.5Zm7.855-1.378a29.819,29.819,0,0,1-.322,4.5.273.273,0,0,1-.545,0,31.574,31.574,0,0,1,0-9,.273.273,0,0,1,.545,0,29.747,29.747,0,0,1,.322,4.5Zm7.715-2.014a31.163,31.163,0,0,1-.307,4.5.271.271,0,0,1-.259.249.274.274,0,0,1-.262-.249,33.446,33.446,0,0,1,0-9,.274.274,0,0,1,.262-.249.271.271,0,0,1,.259.249,31.15,31.15,0,0,1,.307,4.5Zm7.487-2.738a33.235,33.235,0,0,1-.289,4.5.248.248,0,0,1-.5,0,34.851,34.851,0,0,1,0-9.005.248.248,0,0,1,.5,0,33.235,33.235,0,0,1,.289,4.5Zm7.116-3.6a34.349,34.349,0,0,1-.277,4.5.235.235,0,1,1-.469,0,36.736,36.736,0,0,1,0-9,.235.235,0,1,1,.469,0,34.363,34.363,0,0,1,.277,4.5Zm6.435-4.7a36.215,36.215,0,0,1-.262,4.5c-.018.146-.11.249-.222.249a.254.254,0,0,1-.222-.249,38.85,38.85,0,0,1,0-9,.254.254,0,0,1,.222-.249c.113,0,.2.1.222.249a36.229,36.229,0,0,1,.262,4.5Zm-61.835,15.23a27.366,27.366,0,0,0,.35,4.5.3.3,0,0,0,.6,0,28.876,28.876,0,0,0,0-9,.3.3,0,0,0-.6,0,27.4,27.4,0,0,0-.35,4.5Zm-7.934-.8a28.133,28.133,0,0,0,.338,4.5.287.287,0,0,0,.569,0,30.168,30.168,0,0,0,0-9,.287.287,0,0,0-.569,0,28.174,28.174,0,0,0-.338,4.5Zm-7.855-1.378a29.819,29.819,0,0,0,.323,4.5.273.273,0,0,0,.545,0,31.568,31.568,0,0,0,0-9,.273.273,0,0,0-.545,0,29.747,29.747,0,0,0-.323,4.5Zm-7.715-2.014a31.167,31.167,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.112,33.112,0,0,0,0-9,.26.26,0,0,0-.52,0,31.154,31.154,0,0,0-.307,4.5Zm-7.487-2.738a32.716,32.716,0,0,0,.292,4.5.262.262,0,0,0,.246.246.267.267,0,0,0,.246-.246,34.853,34.853,0,0,0,0-9.005.267.267,0,0,0-.246-.246.262.262,0,0,0-.246.246,32.716,32.716,0,0,0-.292,4.5Zm-7.116-3.6a34.351,34.351,0,0,0,.277,4.5.262.262,0,0,0,.234.249.257.257,0,0,0,.234-.249,36.73,36.73,0,0,0,0-9,.257.257,0,0,0-.234-.249.262.262,0,0,0-.234.249,34.365,34.365,0,0,0-.277,4.5Zm-6.435-4.7a36.821,36.821,0,0,0,.262,4.5.224.224,0,1,0,.444,0,38.848,38.848,0,0,0,0-9,.224.224,0,1,0-.444,0,36.835,36.835,0,0,0-.262,4.5Zm52.51,15.5a26.043,26.043,0,0,0,.368,4.5.318.318,0,0,0,.621,0,27.717,27.717,0,0,0,0-9.005.318.318,0,0,0-.621,0,26.043,26.043,0,0,0-.368,4.5Zm-57.6-21.613a38.243,38.243,0,0,0,.246,4.5c.018.146.1.249.21.249s.192-.1.207-.249a41.228,41.228,0,0,0,0-9c-.015-.146-.1-.249-.207-.249s-.192.1-.21.249a38.3,38.3,0,0,0-.246,4.5Zm116.551,0a38.24,38.24,0,0,1-.246,4.5c-.015.146-.1.249-.207.249s-.192-.1-.21-.249a41.23,41.23,0,0,1,0-9c.018-.146.1-.249.21-.249s.192.1.207.249a38.294,38.294,0,0,1,.246,4.5Z" transform="translate(-197.141 -132.103)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11641" data-name="Path 11641" d="M203.137,190.13c0,16.836,26.55,30.487,59.3,30.487s59.3-13.651,59.3-30.487-26.547-30.484-59.3-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-198.142 -159.223)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11642" data-name="Path 11642" d="M202.973,189.659c0,16.651,26.982,30.152,60.265,30.152s60.265-13.5,60.265-30.152-26.982-30.152-60.265-30.152-60.265,13.5-60.265,30.152Z" transform="translate(-198.476 -159.507)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11643" data-name="Path 11643" d="M204.846,186.08c-.386,14.862,23.73,27.551,53.873,28.342S313.609,203.8,314,188.94s-23.73-27.554-53.87-28.342-54.893,10.621-55.282,25.482Z" transform="translate(-194.66 -157.358)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11644" data-name="Path 11644" d="M204.969,187.621c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.266-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-194.4 -157.469)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_11645" data-name="Path 11645" d="M313.254,189.294c-1.728-14.2-25.285-25.44-54.092-25.44S206.8,175.1,205.069,189.294a13.914,13.914,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.915,13.915,0,0,1-.1,1.673Z" transform="translate(-194.4 -157.469)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11646" data-name="Path 11646" d="M259.094,161.606c-27.722,0-50.581,10.414-53.806,23.849a14.9,14.9,0,0,0-.286,1.591,14.62,14.62,0,0,0,.514,2.428,17.874,17.874,0,0,0,3.137,5.832l.478-.07,67.223-9.781,34.372-5c-6.988-10.931-27.46-18.847-51.631-18.847Z" transform="translate(-194.333 -155.22)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11647" data-name="Path 11647" d="M208.662,188.229c.347-13.231,20-23.724,45.574-25.312,2.927-.246,5.936-.374,9-.374,27.144,0,49.626,9.985,53.578,23.012.143.365.268.73.38,1.1l5.82-.846c-3.785-14.831-29.1-26.3-59.778-26.3-33.283,0-60.265,13.5-60.265,30.152v.082c.027,4.542,2.057,8.847,5.674,12.7l5.911-.858a24.712,24.712,0,0,1-1.762-2,17.874,17.874,0,0,1-3.137-5.832,14.535,14.535,0,0,1-.952-4.019,13.9,13.9,0,0,1-.046-1.512Z" transform="translate(-198.476 -159.507)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
    <g id="Group_3336" data-name="Group 3336" transform="translate(163.29 243.73)">
      <path id="Path_11648" data-name="Path 11648" d="M165.508,234.125c0,16.587,26.982,30.037,60.265,30.037s60.262-13.45,60.262-30.037V221H165.508v13.128Z" transform="translate(-158.76 -118.148)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11649" data-name="Path 11649" d="M198.662,233.626q3.861.251,7.858.253a117.844,117.844,0,0,0,15.82-1.05v13.1a89.809,89.809,0,0,1-15.82,1.047c-8.531,0-22.4-1.628-31.585-4.451V229.412a104.116,104.116,0,0,0,23.727,4.214Z" transform="translate(-139.507 -100.962)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11650" data-name="Path 11650" d="M198.816,242.866c11.695-5.452,19.051-13.2,19.194-21.813V234.01c0,8.68-7.384,16.5-19.194,21.984V242.866Z" transform="translate(-90.735 -118.034)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-90)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11651" data-name="Path 11651" d="M177.881,239.22v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V221c.037,6.851,4.643,13.167,12.373,18.223Z" transform="translate(-158.76 -118.148)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11652" data-name="Path 11652" d="M194.581,232.525q3.861.251,7.858.253v13.1a112.984,112.984,0,0,1-25.507-2.817V229.951a108.91,108.91,0,0,0,17.648,2.574Z" transform="translate(-135.427 -99.862)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11653" data-name="Path 11653" d="M233.086,250.568a27.077,27.077,0,0,1-.353,4.5.3.3,0,0,1-.6,0,29.127,29.127,0,0,1,0-9,.3.3,0,0,1,.6,0,27.012,27.012,0,0,1,.353,4.5Zm7.934-.8a28.536,28.536,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.44,30.44,0,0,1,0-9,.289.289,0,0,1,.572,0,28.5,28.5,0,0,1,.338,4.5Zm7.852-1.375a29.846,29.846,0,0,1-.319,4.5.275.275,0,0,1-.548,0,31.888,31.888,0,0,1,0-9.005.275.275,0,0,1,.548,0,29.845,29.845,0,0,1,.319,4.5Zm7.718-2.014a31.158,31.158,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.116,33.116,0,0,1,0-9,.26.26,0,0,1,.52,0,31.113,31.113,0,0,1,.307,4.5Zm7.487-2.738a32.641,32.641,0,0,1-.292,4.5.267.267,0,0,1-.246.249.263.263,0,0,1-.246-.249,34.842,34.842,0,0,1,0-9,.263.263,0,0,1,.246-.25.267.267,0,0,1,.246.25,32.721,32.721,0,0,1,.292,4.5Zm7.113-3.6a34.967,34.967,0,0,1-.274,4.5.236.236,0,1,1-.472,0,36.761,36.761,0,0,1,0-9.005.236.236,0,1,1,.472,0,34.966,34.966,0,0,1,.274,4.5Zm6.438-4.7a36.816,36.816,0,0,1-.262,4.5.254.254,0,0,1-.222.249c-.113,0-.2-.1-.222-.249a38.846,38.846,0,0,1,0-9c.018-.146.11-.249.222-.249a.254.254,0,0,1,.222.249,36.764,36.764,0,0,1,.262,4.5Zm-61.838,15.23a27.084,27.084,0,0,0,.353,4.5.3.3,0,0,0,.6,0,28.886,28.886,0,0,0,0-9,.3.3,0,0,0-.6,0,27.018,27.018,0,0,0-.353,4.5Zm-7.934-.8a28.536,28.536,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.164,30.164,0,0,0,0-9,.289.289,0,0,0-.572,0,28.5,28.5,0,0,0-.338,4.5ZM200,248.39a29.432,29.432,0,0,0,.322,4.5.278.278,0,0,0,.271.246.281.281,0,0,0,.274-.246,31.6,31.6,0,0,0,0-9.005.281.281,0,0,0-.274-.249.278.278,0,0,0-.271.249,29.431,29.431,0,0,0-.322,4.5Zm-7.715-2.014a31.207,31.207,0,0,0,.3,4.5.277.277,0,0,0,.262.249.274.274,0,0,0,.259-.249,33.125,33.125,0,0,0,0-9,.271.271,0,0,0-.259-.25.274.274,0,0,0-.262.25,31.162,31.162,0,0,0-.3,4.5Zm-7.49-2.738a32.65,32.65,0,0,0,.292,4.5.248.248,0,1,0,.5,0,34.829,34.829,0,0,0,0-9,.248.248,0,1,0-.5,0,32.73,32.73,0,0,0-.292,4.5Zm-7.113-3.6a34.394,34.394,0,0,0,.277,4.5.259.259,0,0,0,.234.249.264.264,0,0,0,.234-.249,36.751,36.751,0,0,0,0-9.005.261.261,0,0,0-.234-.246.256.256,0,0,0-.234.246,34.394,34.394,0,0,0-.277,4.5Zm-6.435-4.7a36.226,36.226,0,0,0,.262,4.5c.018.146.11.249.222.249s.2-.1.219-.249a38.41,38.41,0,0,0,0-9c-.018-.146-.11-.249-.219-.249s-.2.1-.222.249a36.174,36.174,0,0,0-.262,4.5Zm52.51,15.5a26.305,26.305,0,0,0,.365,4.5.312.312,0,0,0,.313.249.309.309,0,0,0,.31-.249,27.7,27.7,0,0,0,0-9,.309.309,0,0,0-.31-.249.312.312,0,0,0-.313.249,26.343,26.343,0,0,0-.365,4.5Zm-57.6-21.616a38.968,38.968,0,0,0,.246,4.5c.018.146.106.249.21.249s.192-.1.21-.249a41.23,41.23,0,0,0,0-9c-.018-.146-.106-.249-.21-.249s-.192.1-.21.249a38.912,38.912,0,0,0-.246,4.5Zm116.554,0a38.972,38.972,0,0,1-.246,4.5c-.018.146-.1.249-.21.249s-.192-.1-.21-.249a41.225,41.225,0,0,1,0-9c.018-.146.106-.249.21-.249s.192.1.21.249a38.916,38.916,0,0,1,.246,4.5Z" transform="translate(-157.426 -111.041)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11654" data-name="Path 11654" d="M165.672,241.684c0,16.836,26.547,30.484,59.3,30.484s59.3-13.648,59.3-30.484-26.55-30.487-59.3-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-158.425 -138.163)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11655" data-name="Path 11655" d="M165.508,241.208c0,16.654,26.982,30.152,60.265,30.152s60.262-13.5,60.262-30.152-26.979-30.149-60.262-30.149-60.265,13.5-60.265,30.149Z" transform="translate(-158.76 -138.445)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11656" data-name="Path 11656" d="M167.381,237.632c-.389,14.862,23.73,27.551,53.87,28.339s54.889-10.618,55.279-25.482S252.8,212.938,222.66,212.15s-54.89,10.618-55.279,25.482Z" transform="translate(-154.944 -136.296)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11657" data-name="Path 11657" d="M167.5,239.17c0,14.977,24.266,27.116,54.2,27.116s54.193-12.139,54.193-27.116-24.262-27.113-54.193-27.113S167.5,224.2,167.5,239.17Z" transform="translate(-154.686 -136.407)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11658" data-name="Path 11658" d="M167.3,239.217c0,14.8,23.989,26.809,53.581,26.809s53.581-12.005,53.581-26.809-23.989-26.809-53.581-26.809-53.581,12-53.581,26.809Z" transform="translate(-155.108 -135.69)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11659" data-name="Path 11659" d="M275.791,240.846C274.063,226.651,250.5,215.4,221.7,215.4s-52.364,11.247-54.092,25.443a13.959,13.959,0,0,1-.1-1.676c0-14.974,24.266-27.113,54.2-27.113s54.193,12.139,54.193,27.113a13.957,13.957,0,0,1-.1,1.676Z" transform="translate(-154.686 -136.407)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11660" data-name="Path 11660" d="M168.466,229.448c0,16.59,26.979,30.04,60.262,30.04s60.265-13.45,60.265-30.04l0-13.125H168.466v13.125Z" transform="translate(-152.719 -127.694)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11661" data-name="Path 11661" d="M201.62,228.951q3.856.251,7.855.256a117.857,117.857,0,0,0,15.823-1.05v13.1a89.835,89.835,0,0,1-15.823,1.047c-8.528,0-22.4-1.631-31.582-4.451V224.737a103.921,103.921,0,0,0,23.727,4.214Z" transform="translate(-133.466 -110.51)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11662" data-name="Path 11662" d="M201.773,238.189c11.7-5.449,19.054-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V238.189Z" transform="translate(-84.696 -127.58)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11663" data-name="Path 11663" d="M180.839,234.543v13.143c-7.761-5.056-12.373-11.378-12.373-18.239V216.323c.036,6.851,4.643,13.167,12.373,18.22Z" transform="translate(-152.719 -127.694)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11664" data-name="Path 11664" d="M197.539,227.848q3.856.251,7.855.256v13.1a112.978,112.978,0,0,1-25.5-2.817V225.277a108.522,108.522,0,0,0,17.648,2.571Z" transform="translate(-129.385 -109.407)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11665" data-name="Path 11665" d="M236.044,245.9a27.082,27.082,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.9,28.9,0,0,1,0-9.005.3.3,0,0,1,.6,0,27.083,27.083,0,0,1,.353,4.5Zm7.934-.8a28.565,28.565,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.188,30.188,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.564,28.564,0,0,1,.338,4.5Zm7.852-1.378a29.4,29.4,0,0,1-.322,4.5.278.278,0,0,1-.271.249.283.283,0,0,1-.274-.249,31.565,31.565,0,0,1,0-9,.283.283,0,0,1,.274-.249.278.278,0,0,1,.271.249,29.361,29.361,0,0,1,.322,4.5Zm7.715-2.011a31.159,31.159,0,0,1-.3,4.5.274.274,0,0,1-.262.25.271.271,0,0,1-.259-.25,33.116,33.116,0,0,1,0-9,.274.274,0,0,1,.259-.249.277.277,0,0,1,.262.249,31.236,31.236,0,0,1,.3,4.5Zm7.49-2.741a32.715,32.715,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.835,34.835,0,0,1,0-9,.248.248,0,1,1,.5,0,32.636,32.636,0,0,1,.292,4.5Zm7.113-3.6a34.308,34.308,0,0,1-.277,4.5.257.257,0,0,1-.234.249.262.262,0,0,1-.234-.249,36.736,36.736,0,0,1,0-9,.262.262,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.355,34.355,0,0,1,.277,4.5Zm6.434-4.7a36.278,36.278,0,0,1-.262,4.5.222.222,0,1,1-.441,0,38.867,38.867,0,0,1,0-9.005c.018-.146.109-.249.219-.249s.2.1.222.249a36.276,36.276,0,0,1,.262,4.5ZM218.748,245.9a27.077,27.077,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.151,29.151,0,0,0,0-9.005.3.3,0,0,0-.6,0,27.078,27.078,0,0,0-.353,4.5Zm-7.934-.8a28.567,28.567,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.455,30.455,0,0,0,0-9.005.289.289,0,0,0-.572,0,28.566,28.566,0,0,0-.338,4.5Zm-7.852-1.378a29.817,29.817,0,0,0,.319,4.5.275.275,0,0,0,.548,0,31.871,31.871,0,0,0,0-9,.275.275,0,0,0-.548,0,29.775,29.775,0,0,0-.319,4.5Zm-7.718-2.011a31.121,31.121,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.116,33.116,0,0,0,0-9,.26.26,0,0,0-.52,0,31.2,31.2,0,0,0-.307,4.5Zm-7.487-2.741a32.715,32.715,0,0,0,.292,4.5.267.267,0,0,0,.246.249.263.263,0,0,0,.246-.249,34.831,34.831,0,0,0,0-9,.263.263,0,0,0-.246-.25.267.267,0,0,0-.246.25,32.636,32.636,0,0,0-.292,4.5Zm-7.116-3.6a34.855,34.855,0,0,0,.277,4.5.236.236,0,1,0,.472,0,36.732,36.732,0,0,0,0-9,.236.236,0,1,0-.472,0,34.9,34.9,0,0,0-.277,4.5Zm-6.435-4.7a36.219,36.219,0,0,0,.265,4.5.248.248,0,0,0,.219.246.251.251,0,0,0,.222-.246,38.876,38.876,0,0,0,0-9.005c-.018-.146-.109-.249-.222-.249a.249.249,0,0,0-.219.249,36.218,36.218,0,0,0-.265,4.5Zm52.513,15.5a26.363,26.363,0,0,0,.365,4.5.309.309,0,0,0,.31.249.312.312,0,0,0,.313-.249,27.947,27.947,0,0,0,0-9.005.313.313,0,0,0-.313-.246.309.309,0,0,0-.31.246,26.363,26.363,0,0,0-.365,4.5Zm-57.6-21.613a39.008,39.008,0,0,0,.246,4.5c.018.143.1.246.21.246s.192-.1.207-.246a40.759,40.759,0,0,0,0-9.005c-.015-.143-.1-.249-.207-.249s-.192.106-.21.249a39.008,39.008,0,0,0-.246,4.5Zm116.554,0a39,39,0,0,1-.246,4.5c-.018.143-.106.246-.21.246s-.192-.1-.21-.246a41.258,41.258,0,0,1,0-9.005c.018-.143.106-.249.21-.249s.192.106.21.249a39,39,0,0,1,.246,4.5Z" transform="translate(-151.385 -120.589)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11666" data-name="Path 11666" d="M168.63,237.007c0,16.839,26.547,30.487,59.3,30.487s59.295-13.648,59.295-30.487-26.547-30.484-59.295-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-152.384 -147.709)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11667" data-name="Path 11667" d="M168.466,236.536c0,16.654,26.979,30.152,60.262,30.152s60.265-13.5,60.265-30.152-26.979-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-152.719 -147.993)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11668" data-name="Path 11668" d="M170.339,232.957c-.389,14.865,23.73,27.551,53.87,28.342s54.889-10.621,55.279-25.482-23.73-27.551-53.87-28.342-54.889,10.621-55.279,25.482Z" transform="translate(-148.903 -145.844)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11669" data-name="Path 11669" d="M170.461,234.5c0,14.974,24.262,27.116,54.193,27.116s54.2-12.142,54.2-27.116-24.262-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-148.645 -145.955)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_11670" data-name="Path 11670" d="M278.746,236.171c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.913,13.913,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.314,13.314,0,0,1-.1,1.673Z" transform="translate(-148.645 -145.955)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11671" data-name="Path 11671" d="M169.561,234.809c0,14.8,23.989,26.806,53.581,26.806s53.581-12,53.581-26.806S252.735,208,223.142,208s-53.581,12-53.581,26.809Z" transform="translate(-150.483 -144.692)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11672" data-name="Path 11672" d="M164.029,224.776c0,16.59,26.982,30.037,60.265,30.037s60.265-13.447,60.265-30.037l0-13.128H164.029v13.128Z" transform="translate(-161.781 -137.242)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11673" data-name="Path 11673" d="M197.186,224.277q3.856.251,7.855.253a117.852,117.852,0,0,0,15.82-1.05v13.1a89.52,89.52,0,0,1-15.82,1.047c-8.528,0-22.4-1.628-31.585-4.451V220.063a103.948,103.948,0,0,0,23.73,4.214Z" transform="translate(-142.528 -120.056)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11674" data-name="Path 11674" d="M197.337,233.517c11.695-5.452,19.051-13.2,19.194-21.813l0,12.957c0,8.68-7.387,16.5-19.2,21.984V233.517Z" transform="translate(-93.755 -137.128)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-108)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11675" data-name="Path 11675" d="M176.405,229.871v13.14c-7.764-5.056-12.376-11.378-12.376-18.236V211.648c.036,6.851,4.643,13.167,12.376,18.224Z" transform="translate(-161.781 -137.242)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11676" data-name="Path 11676" d="M193.105,223.174q3.856.251,7.855.253v13.1a112.99,112.99,0,0,1-25.507-2.817V220.6a108.9,108.9,0,0,0,17.651,2.571Z" transform="translate(-138.447 -118.953)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11677" data-name="Path 11677" d="M231.607,241.219a27.4,27.4,0,0,1-.35,4.5.3.3,0,0,1-.6,0,29.13,29.13,0,0,1,0-9,.3.3,0,0,1,.6,0,27.392,27.392,0,0,1,.35,4.5Zm7.934-.8a28.527,28.527,0,0,1-.338,4.5.29.29,0,0,1-.286.249.287.287,0,0,1-.283-.249,30.165,30.165,0,0,1,0-9,.287.287,0,0,1,.283-.249.291.291,0,0,1,.286.249,28.539,28.539,0,0,1,.338,4.5Zm7.855-1.378a29.828,29.828,0,0,1-.322,4.5.283.283,0,0,1-.274.246.278.278,0,0,1-.271-.246,31.6,31.6,0,0,1,0-9.005.281.281,0,0,1,.271-.249.286.286,0,0,1,.274.249,29.828,29.828,0,0,1,.322,4.5Zm7.715-2.014a31.167,31.167,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.123,33.123,0,0,1,0-9,.26.26,0,0,1,.52,0,31.154,31.154,0,0,1,.307,4.5Zm7.487-2.738a32.721,32.721,0,0,1-.292,4.5.246.246,0,0,1-.493,0,34.862,34.862,0,0,1,0-9.005.246.246,0,1,1,.493,0,32.72,32.72,0,0,1,.292,4.5Zm7.116-3.6a34.943,34.943,0,0,1-.277,4.5.262.262,0,0,1-.234.249.257.257,0,0,1-.234-.249,36.728,36.728,0,0,1,0-9,.257.257,0,0,1,.234-.249.262.262,0,0,1,.234.249,34.857,34.857,0,0,1,.277,4.5Zm6.435-4.7a36.816,36.816,0,0,1-.262,4.5.254.254,0,0,1-.222.249c-.113,0-.2-.1-.222-.249a38.85,38.85,0,0,1,0-9c.018-.146.11-.249.222-.249a.254.254,0,0,1,.222.249,36.764,36.764,0,0,1,.262,4.5Zm-61.838,15.23a27.386,27.386,0,0,0,.353,4.5.3.3,0,0,0,.6,0,28.883,28.883,0,0,0,0-9,.3.3,0,0,0-.6,0,27.373,27.373,0,0,0-.353,4.5Zm-7.931-.8a28.538,28.538,0,0,0,.335,4.5.289.289,0,0,0,.572,0,30.165,30.165,0,0,0,0-9,.289.289,0,0,0-.572,0,28.55,28.55,0,0,0-.335,4.5Zm-7.855-1.378a29.829,29.829,0,0,0,.322,4.5.274.274,0,0,0,.545,0,31.594,31.594,0,0,0,0-9.005.273.273,0,0,0-.545,0,29.83,29.83,0,0,0-.322,4.5Zm-7.715-2.014a31.178,31.178,0,0,0,.307,4.5.259.259,0,0,0,.517,0,33.125,33.125,0,0,0,0-9,.259.259,0,0,0-.517,0,31.165,31.165,0,0,0-.307,4.5Zm-7.49-2.738a32.718,32.718,0,0,0,.292,4.5.248.248,0,0,0,.5,0,34.853,34.853,0,0,0,0-9.005.248.248,0,1,0-.5,0,32.717,32.717,0,0,0-.292,4.5Zm-7.113-3.6a34.4,34.4,0,0,0,.277,4.5.235.235,0,1,0,.469,0,36.726,36.726,0,0,0,0-9,.235.235,0,1,0-.469,0,34.311,34.311,0,0,0-.277,4.5Zm-6.435-4.7a36.225,36.225,0,0,0,.262,4.5c.018.146.11.249.222.249a.254.254,0,0,0,.222-.249,38.848,38.848,0,0,0,0-9,.254.254,0,0,0-.222-.249c-.113,0-.2.1-.222.249a36.175,36.175,0,0,0-.262,4.5Zm52.51,15.5a26.3,26.3,0,0,0,.365,4.5.312.312,0,0,0,.313.249.306.306,0,0,0,.31-.249,27.7,27.7,0,0,0,0-9,.307.307,0,0,0-.31-.249.312.312,0,0,0-.313.249,26.368,26.368,0,0,0-.365,4.5Zm-57.6-21.613a38.967,38.967,0,0,0,.246,4.5c.018.146.107.249.21.249s.192-.1.21-.249a41.238,41.238,0,0,0,0-9c-.018-.146-.1-.249-.21-.249s-.192.1-.21.249a38.983,38.983,0,0,0-.246,4.5Zm116.554,0a38.97,38.97,0,0,1-.246,4.5c-.018.146-.1.249-.21.249s-.192-.1-.207-.249a41.235,41.235,0,0,1,0-9c.015-.146.1-.249.207-.249s.192.1.21.249a38.986,38.986,0,0,1,.246,4.5Z" transform="translate(-160.447 -130.135)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11678" data-name="Path 11678" d="M164.193,232.335c0,16.836,26.547,30.484,59.3,30.484s59.3-13.648,59.3-30.484-26.55-30.487-59.3-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-161.446 -157.257)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11679" data-name="Path 11679" d="M164.029,231.862c0,16.651,26.982,30.149,60.265,30.149s60.265-13.5,60.265-30.149-26.982-30.152-60.265-30.152-60.265,13.5-60.265,30.152Z" transform="translate(-161.781 -157.539)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11680" data-name="Path 11680" d="M165.9,228.283c-.389,14.862,23.73,27.551,53.87,28.339S274.665,246,275.054,231.14s-23.73-27.551-53.873-28.339-54.889,10.618-55.279,25.482Z" transform="translate(-157.965 -155.39)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11681" data-name="Path 11681" d="M166.024,229.824c0,14.974,24.266,27.113,54.2,27.113s54.193-12.139,54.193-27.113-24.262-27.116-54.193-27.116-54.2,12.139-54.2,27.116Z" transform="translate(-157.706 -155.5)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11682" data-name="Path 11682" d="M274.312,231.5c-1.728-14.2-25.285-25.443-54.092-25.443S167.855,217.3,166.127,231.5a13.948,13.948,0,0,1-.1-1.673c0-14.977,24.266-27.116,54.2-27.116s54.193,12.139,54.193,27.116a13.958,13.958,0,0,1-.1,1.673Z" transform="translate(-157.706 -155.5)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11683" data-name="Path 11683" d="M166.194,229.972c0,14.807,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.581,12-53.581,26.809Z" transform="translate(-157.359 -154.571)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11684" data-name="Path 11684" d="M166.987,220.1c0,16.59,26.979,30.04,60.265,30.04s60.262-13.45,60.262-30.04V206.974H166.987V220.1Z" transform="translate(-155.74 -146.788)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11685" data-name="Path 11685" d="M200.141,219.6q3.856.256,7.858.256a117.842,117.842,0,0,0,15.82-1.05v13.1A89.807,89.807,0,0,1,208,232.954c-8.531,0-22.4-1.631-31.585-4.451V215.388a103.781,103.781,0,0,0,23.727,4.214Z" transform="translate(-136.487 -129.604)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11686" data-name="Path 11686" d="M200.294,228.843c11.7-5.452,19.054-13.2,19.2-21.813v12.954c0,8.68-7.387,16.5-19.2,21.984V228.843Z" transform="translate(-87.716 -146.673)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11687" data-name="Path 11687" d="M179.36,225.194v13.143c-7.761-5.056-12.373-11.378-12.373-18.239V206.974c.036,6.851,4.643,13.167,12.373,18.22Z" transform="translate(-155.74 -146.788)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11688" data-name="Path 11688" d="M196.06,218.5q3.856.256,7.858.256v13.1a112.987,112.987,0,0,1-25.507-2.817V215.928A108.52,108.52,0,0,0,196.06,218.5Z" transform="translate(-132.406 -128.501)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11689" data-name="Path 11689" d="M234.565,236.547a27.081,27.081,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.9,28.9,0,0,1,0-9.005.3.3,0,0,1,.6,0,27.083,27.083,0,0,1,.353,4.5Zm7.934-.8a28.561,28.561,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.455,30.455,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.561,28.561,0,0,1,.338,4.5Zm7.852-1.375a29.8,29.8,0,0,1-.319,4.5.275.275,0,0,1-.548,0,31.574,31.574,0,0,1,0-9,.275.275,0,0,1,.548,0,29.816,29.816,0,0,1,.319,4.5Zm7.718-2.014a31.2,31.2,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.136,33.136,0,0,1,0-9.005.26.26,0,0,1,.52,0,31.2,31.2,0,0,1,.307,4.5Zm7.487-2.741a32.7,32.7,0,0,1-.292,4.5.248.248,0,1,1-.5,0,35.2,35.2,0,0,1,0-9,.248.248,0,1,1,.5,0,32.651,32.651,0,0,1,.292,4.5Zm7.113-3.6a34.391,34.391,0,0,1-.277,4.5.256.256,0,0,1-.234.246.261.261,0,0,1-.234-.246,36.752,36.752,0,0,1,0-9.005.261.261,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.39,34.39,0,0,1,.277,4.5Zm6.435-4.7a36.263,36.263,0,0,1-.262,4.5.222.222,0,1,1-.441,0,38.871,38.871,0,0,1,0-9.005c.018-.143.109-.249.219-.249a.254.254,0,0,1,.222.249,36.265,36.265,0,0,1,.262,4.5Zm-61.835,15.23a27.075,27.075,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.147,29.147,0,0,0,0-9.005.3.3,0,0,0-.6,0,27.077,27.077,0,0,0-.353,4.5Zm-7.934-.8a28.566,28.566,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.459,30.459,0,0,0,0-9.005.289.289,0,0,0-.572,0,28.567,28.567,0,0,0-.338,4.5Zm-7.852-1.375a29.81,29.81,0,0,0,.319,4.5.275.275,0,0,0,.548,0,31.574,31.574,0,0,0,0-9,.275.275,0,0,0-.548,0,29.822,29.822,0,0,0-.319,4.5Zm-7.718-2.014a31.211,31.211,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.136,33.136,0,0,0,0-9.005.26.26,0,0,0-.52,0,31.209,31.209,0,0,0-.307,4.5Zm-7.487-2.741a32.678,32.678,0,0,0,.292,4.5.248.248,0,1,0,.5,0,35.192,35.192,0,0,0,0-9,.248.248,0,1,0-.5,0,32.632,32.632,0,0,0-.292,4.5Zm-7.113-3.6a34.4,34.4,0,0,0,.277,4.5.256.256,0,0,0,.234.246.261.261,0,0,0,.234-.246,36.756,36.756,0,0,0,0-9.005.262.262,0,0,0-.234-.249.257.257,0,0,0-.234.249,34.4,34.4,0,0,0-.277,4.5Zm-6.434-4.7a36.255,36.255,0,0,0,.262,4.5.222.222,0,1,0,.441,0,38.878,38.878,0,0,0,0-9.005c-.018-.143-.11-.249-.219-.249a.254.254,0,0,0-.222.249,36.256,36.256,0,0,0-.262,4.5Zm52.51,15.5a26.346,26.346,0,0,0,.365,4.5.32.32,0,0,0,.624,0,27.927,27.927,0,0,0,0-9,.32.32,0,0,0-.624,0,26.308,26.308,0,0,0-.365,4.5Zm-57.6-21.613a39.012,39.012,0,0,0,.246,4.5c.018.143.106.249.21.249s.192-.106.21-.249a41.245,41.245,0,0,0,0-9.005c-.018-.143-.106-.249-.21-.249s-.192.107-.21.249a39.012,39.012,0,0,0-.246,4.5Zm116.554,0a38.991,38.991,0,0,1-.246,4.5c-.018.143-.106.249-.21.249s-.192-.106-.21-.249a41.258,41.258,0,0,1,0-9.005c.018-.143.106-.249.21-.249s.192.107.21.249a38.991,38.991,0,0,1,.246,4.5Z" transform="translate(-154.406 -139.683)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11690" data-name="Path 11690" d="M167.151,227.661c0,16.836,26.547,30.484,59.3,30.484s59.3-13.648,59.3-30.484-26.55-30.487-59.3-30.487-59.3,13.648-59.3,30.487Z" transform="translate(-155.405 -166.803)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11691" data-name="Path 11691" d="M166.987,227.187c0,16.654,26.979,30.152,60.265,30.152s60.262-13.5,60.262-30.152-26.979-30.152-60.262-30.152-60.265,13.5-60.265,30.152Z" transform="translate(-155.74 -167.086)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11692" data-name="Path 11692" d="M168.86,223.608c-.389,14.865,23.73,27.554,53.87,28.342s54.889-10.621,55.279-25.482-23.73-27.551-53.87-28.342-54.889,10.621-55.279,25.482Z" transform="translate(-151.924 -164.937)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11693" data-name="Path 11693" d="M168.982,225.147c0,14.974,24.262,27.116,54.2,27.116s54.193-12.142,54.193-27.116-24.262-27.113-54.193-27.113-54.2,12.139-54.2,27.113Z" transform="translate(-151.665 -165.046)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_11694" data-name="Path 11694" d="M168.092,225.258c0,14.8,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.581,12-53.581,26.809Z" transform="translate(-153.483 -164.199)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11695" data-name="Path 11695" d="M277.27,226.82c-1.728-14.192-25.288-25.44-54.092-25.44s-52.364,11.247-54.092,25.44a13.3,13.3,0,0,1-.1-1.673c0-14.974,24.262-27.113,54.2-27.113s54.193,12.139,54.193,27.113a13.907,13.907,0,0,1-.1,1.673Z" transform="translate(-151.665 -165.046)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11696" data-name="Path 11696" d="M163.29,215.427c0,16.59,26.979,30.037,60.262,30.037s60.265-13.447,60.265-30.037l0-13.128H163.29v13.128Z" transform="translate(-163.29 -156.336)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11697" data-name="Path 11697" d="M196.444,214.928q3.856.251,7.855.256a117.347,117.347,0,0,0,15.823-1.053v13.1a89.892,89.892,0,0,1-15.823,1.044c-8.528,0-22.4-1.628-31.582-4.451V210.714a103.925,103.925,0,0,0,23.727,4.214Z" transform="translate(-144.037 -139.15)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11698" data-name="Path 11698" d="M196.6,224.168c11.695-5.452,19.054-13.2,19.194-21.813l0,12.957c0,8.68-7.387,16.5-19.2,21.984V224.168Z" transform="translate(-95.267 -156.221)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11699" data-name="Path 11699" d="M175.663,220.522v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V202.3c.037,6.851,4.643,13.167,12.373,18.223Z" transform="translate(-163.29 -156.336)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11700" data-name="Path 11700" d="M192.363,213.825q3.856.251,7.855.256v13.094a112.979,112.979,0,0,1-25.5-2.817v-13.1a108.526,108.526,0,0,0,17.648,2.571Z" transform="translate(-139.957 -138.047)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11701" data-name="Path 11701" d="M230.868,231.87a27.377,27.377,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.88,28.88,0,0,1,0-9,.3.3,0,0,1,.6,0,27.365,27.365,0,0,1,.353,4.5Zm7.934-.8a28.527,28.527,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.168,30.168,0,0,1,0-9,.289.289,0,0,1,.572,0,28.54,28.54,0,0,1,.338,4.5Zm7.852-1.378a29.431,29.431,0,0,1-.323,4.5.281.281,0,0,1-.271.249.283.283,0,0,1-.274-.249,31.592,31.592,0,0,1,0-9.005.283.283,0,0,1,.274-.249.281.281,0,0,1,.271.249,29.43,29.43,0,0,1,.323,4.5Zm7.715-2.011a31.178,31.178,0,0,1-.3,4.5.274.274,0,0,1-.262.249.271.271,0,0,1-.259-.249,33.123,33.123,0,0,1,0-9,.271.271,0,0,1,.259-.249.274.274,0,0,1,.262.249,31.191,31.191,0,0,1,.3,4.5Zm7.49-2.741a32.716,32.716,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.848,34.848,0,0,1,0-9.005.248.248,0,1,1,.5,0,32.716,32.716,0,0,1,.292,4.5Zm7.113-3.6a34.358,34.358,0,0,1-.277,4.5.257.257,0,0,1-.234.249.262.262,0,0,1-.234-.249,36.732,36.732,0,0,1,0-9,.262.262,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.31,34.31,0,0,1,.277,4.5Zm6.435-4.7a36.2,36.2,0,0,1-.262,4.5c-.018.146-.11.249-.222.249a.249.249,0,0,1-.219-.249,38.4,38.4,0,0,1,0-9,.249.249,0,0,1,.219-.25c.113,0,.2.1.222.25a36.174,36.174,0,0,1,.262,4.5ZM213.572,231.87a27.056,27.056,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.128,29.128,0,0,0,0-9,.3.3,0,0,0-.6,0,27.044,27.044,0,0,0-.353,4.5Zm-7.934-.8a28.52,28.52,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.437,30.437,0,0,0,0-9,.289.289,0,0,0-.572,0,28.532,28.532,0,0,0-.338,4.5Zm-7.855-1.378a29.822,29.822,0,0,0,.323,4.5.275.275,0,0,0,.548,0,31.9,31.9,0,0,0,0-9.005.275.275,0,0,0-.548,0,29.821,29.821,0,0,0-.323,4.5Zm-7.715-2.011a31.163,31.163,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.123,33.123,0,0,0,0-9,.26.26,0,0,0-.52,0,31.176,31.176,0,0,0-.307,4.5Zm-7.487-2.741a32.727,32.727,0,0,0,.292,4.5.246.246,0,1,0,.493,0,34.85,34.85,0,0,0,0-9.005.246.246,0,1,0-.493,0,32.728,32.728,0,0,0-.292,4.5Zm-7.116-3.6a34.9,34.9,0,0,0,.277,4.5.236.236,0,1,0,.472,0,37.137,37.137,0,0,0,0-9,.236.236,0,1,0-.472,0,34.85,34.85,0,0,0-.277,4.5Zm-6.434-4.7a36.8,36.8,0,0,0,.262,4.5.254.254,0,0,0,.222.249c.113,0,.2-.1.222-.249a38.848,38.848,0,0,0,0-9c-.018-.146-.109-.25-.222-.25a.254.254,0,0,0-.222.25,36.781,36.781,0,0,0-.262,4.5Zm52.51,15.5a26.348,26.348,0,0,0,.368,4.5.309.309,0,0,0,.31.246.313.313,0,0,0,.313-.246,27.951,27.951,0,0,0,0-9.005.312.312,0,0,0-.313-.249.309.309,0,0,0-.31.249,26.347,26.347,0,0,0-.368,4.5Zm-57.6-21.613a38.966,38.966,0,0,0,.246,4.5c.018.146.1.249.21.249s.192-.1.207-.249a40.735,40.735,0,0,0,0-9c-.015-.146-.1-.249-.207-.249s-.192.1-.21.249a38.984,38.984,0,0,0-.246,4.5Zm116.554,0a38.963,38.963,0,0,1-.246,4.5c-.018.146-.106.249-.21.249s-.192-.1-.21-.249a41.221,41.221,0,0,1,0-9c.018-.146.107-.249.21-.249s.192.1.21.249a38.981,38.981,0,0,1,.246,4.5Z" transform="translate(-161.956 -149.228)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11702" data-name="Path 11702" d="M163.454,222.984c0,16.836,26.547,30.487,59.3,30.487s59.295-13.651,59.295-30.487S255.5,192.5,222.752,192.5s-59.3,13.648-59.3,30.484Z" transform="translate(-162.955 -176.348)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11703" data-name="Path 11703" d="M163.29,222.513c0,16.651,26.979,30.152,60.262,30.152s60.265-13.5,60.265-30.152-26.982-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-163.29 -176.632)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11704" data-name="Path 11704" d="M165.163,218.934c-.389,14.862,23.73,27.551,53.87,28.342s54.889-10.621,55.279-25.482-23.73-27.554-53.87-28.342-54.889,10.621-55.279,25.482Z" transform="translate(-159.474 -174.483)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11705" data-name="Path 11705" d="M165.285,220.475c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.262-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-159.216 -174.594)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11706" data-name="Path 11706" d="M273.57,222.148c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.923,13.923,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.325,13.325,0,0,1-.1,1.673Z" transform="translate(-159.216 -174.594)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11707" data-name="Path 11707" d="M165.4,220.115c0,14.8,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.581,12-53.581,26.809Z" transform="translate(-158.985 -174.702)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11708" data-name="Path 11708" d="M166.247,210.257c0,16.59,26.982,30.037,60.265,30.037s60.265-13.447,60.265-30.037l0-13.128H166.247v13.128Z" transform="translate(-157.251 -166.894)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11709" data-name="Path 11709" d="M199.4,209.758q3.856.251,7.855.256a117.886,117.886,0,0,0,15.823-1.05v13.1a89.938,89.938,0,0,1-15.823,1.044c-8.528,0-22.4-1.628-31.585-4.451V205.544a103.98,103.98,0,0,0,23.73,4.214Z" transform="translate(-137.998 -149.708)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-18)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11710" data-name="Path 11710" d="M199.555,219c11.695-5.449,19.051-13.2,19.194-21.81l0,12.954c0,8.68-7.387,16.5-19.2,21.984V219Z" transform="translate(-89.225 -166.778)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11711" data-name="Path 11711" d="M178.623,215.352V228.5c-7.764-5.059-12.376-11.378-12.376-18.239V197.129c.04,6.854,4.646,13.17,12.376,18.223Z" transform="translate(-157.251 -166.894)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_11712" data-name="Path 11712" d="M195.321,208.655q3.856.251,7.855.256V222a112.966,112.966,0,0,1-25.5-2.814V206.084a108.48,108.48,0,0,0,17.648,2.571Z" transform="translate(-133.915 -148.605)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11713" data-name="Path 11713" d="M233.826,226.7a27.344,27.344,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.876,28.876,0,0,1,0-9,.3.3,0,0,1,.6,0,27.411,27.411,0,0,1,.353,4.5Zm7.931-.8a28.584,28.584,0,0,1-.335,4.5.289.289,0,0,1-.572,0,30.191,30.191,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.584,28.584,0,0,1,.335,4.5Zm7.855-1.378a29.79,29.79,0,0,1-.322,4.5.273.273,0,0,1-.545,0,31.571,31.571,0,0,1,0-9,.273.273,0,0,1,.545,0,29.748,29.748,0,0,1,.322,4.5Zm7.715-2.011a31.124,31.124,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.123,33.123,0,0,1,0-9,.26.26,0,0,1,.52,0,31.167,31.167,0,0,1,.307,4.5Zm7.487-2.741a33.244,33.244,0,0,1-.289,4.5.248.248,0,1,1-.5,0,34.858,34.858,0,0,1,0-9.005.248.248,0,0,1,.5,0,33.246,33.246,0,0,1,.289,4.5Zm7.116-3.6a34.336,34.336,0,0,1-.277,4.5.235.235,0,1,1-.468,0,36.728,36.728,0,0,1,0-9,.235.235,0,1,1,.468,0,34.352,34.352,0,0,1,.277,4.5Zm6.435-4.7a36.183,36.183,0,0,1-.262,4.5.224.224,0,1,1-.444,0,38.846,38.846,0,0,1,0-9,.224.224,0,1,1,.444,0,36.272,36.272,0,0,1,.262,4.5ZM216.53,226.7a27.361,27.361,0,0,0,.35,4.5.3.3,0,0,0,.6,0,28.881,28.881,0,0,0,0-9,.3.3,0,0,0-.6,0,27.428,27.428,0,0,0-.35,4.5Zm-7.934-.8a28.208,28.208,0,0,0,.338,4.5.287.287,0,0,0,.569,0,30.188,30.188,0,0,0,0-9.005.287.287,0,0,0-.569,0,28.209,28.209,0,0,0-.338,4.5Zm-7.855-1.378a29.8,29.8,0,0,0,.322,4.5.273.273,0,0,0,.545,0,31.577,31.577,0,0,0,0-9,.273.273,0,0,0-.545,0,29.755,29.755,0,0,0-.322,4.5Zm-7.715-2.011a31.124,31.124,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.117,33.117,0,0,0,0-9,.26.26,0,0,0-.52,0,31.167,31.167,0,0,0-.307,4.5Zm-7.487-2.741a32.7,32.7,0,0,0,.292,4.5.265.265,0,0,0,.246.249.27.27,0,0,0,.246-.249,34.854,34.854,0,0,0,0-9.005.267.267,0,0,0-.246-.246.262.262,0,0,0-.246.246,32.705,32.705,0,0,0-.292,4.5Zm-7.116-3.6a34.336,34.336,0,0,0,.277,4.5.235.235,0,1,0,.469,0,36.724,36.724,0,0,0,0-9,.235.235,0,1,0-.469,0,34.352,34.352,0,0,0-.277,4.5Zm-6.434-4.7a36.764,36.764,0,0,0,.262,4.5.224.224,0,1,0,.444,0,38.846,38.846,0,0,0,0-9,.224.224,0,1,0-.444,0,36.854,36.854,0,0,0-.262,4.5Zm52.51,15.5a26.043,26.043,0,0,0,.368,4.5.318.318,0,0,0,.621,0,27.7,27.7,0,0,0,0-9,.318.318,0,0,0-.621,0,25.98,25.98,0,0,0-.368,4.5Zm-57.6-21.613a38.253,38.253,0,0,0,.246,4.5c.015.146.1.249.21.249s.192-.1.207-.249a41.236,41.236,0,0,0,0-9c-.015-.143-.1-.249-.207-.249s-.195.107-.21.249a38.347,38.347,0,0,0-.246,4.5Zm116.551,0a38.245,38.245,0,0,1-.246,4.5c-.015.146-.1.249-.207.249s-.192-.1-.21-.249a41.236,41.236,0,0,1,0-9c.018-.143.1-.249.21-.249s.192.107.207.249a38.34,38.34,0,0,1,.246,4.5Z" transform="translate(-155.915 -159.787)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_11714" data-name="Path 11714" d="M166.411,217.814c0,16.839,26.55,30.487,59.3,30.487s59.3-13.648,59.3-30.487-26.547-30.484-59.3-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-156.916 -186.907)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11715" data-name="Path 11715" d="M166.247,217.343c0,16.654,26.982,30.152,60.265,30.152s60.265-13.5,60.265-30.152-26.982-30.152-60.265-30.152-60.265,13.5-60.265,30.152Z" transform="translate(-157.251 -187.191)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_11716" data-name="Path 11716" d="M168.12,213.764c-.386,14.865,23.73,27.551,53.87,28.342s54.892-10.621,55.282-25.482-23.73-27.551-53.87-28.342S168.509,198.9,168.12,213.764Z" transform="translate(-153.435 -185.042)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_11717" data-name="Path 11717" d="M168.243,215.305c0,14.974,24.262,27.113,54.193,27.113s54.2-12.139,54.2-27.113-24.265-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-153.174 -185.153)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_11718" data-name="Path 11718" d="M276.528,216.978c-1.728-14.2-25.285-25.44-54.092-25.44s-52.364,11.244-54.092,25.44a13.918,13.918,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.925,13.925,0,0,1-.1,1.673Z" transform="translate(-153.174 -185.153)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_11719" data-name="Path 11719" d="M222.368,189.29c-27.722,0-50.582,10.414-53.806,23.849a14.18,14.18,0,0,0-.286,1.591,14.483,14.483,0,0,0,.514,2.428,17.872,17.872,0,0,0,3.137,5.832l.478-.067,67.223-9.784,34.372-5c-6.988-10.931-27.46-18.847-51.631-18.847Z" transform="translate(-153.107 -182.904)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_11720" data-name="Path 11720" d="M171.936,215.914c.347-13.231,20-23.724,45.571-25.312,2.93-.246,5.939-.374,9.005-.374,27.144,0,49.626,9.985,53.578,23.012.143.365.268.73.38,1.1l5.82-.849c-3.785-14.831-29.106-26.3-59.778-26.3-33.283,0-60.265,13.5-60.265,30.152v.082c.027,4.542,2.057,8.847,5.674,12.7l5.911-.858a24.7,24.7,0,0,1-1.762-2,17.872,17.872,0,0,1-3.137-5.832,14.484,14.484,0,0,1-.952-4.019,13.852,13.852,0,0,1-.046-1.512Z" transform="translate(-157.251 -187.191)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
    <g id="Group_3341" data-name="Group 3341" transform="translate(260.903 327.972)">
      <path id="Path_11721" data-name="Path 11721" d="M309.355,258.691c-1.728-14.2-25.285-25.44-54.092-25.44S202.9,244.5,201.17,258.691a13.915,13.915,0,0,1-.1-1.673c0-14.974,24.266-27.116,54.2-27.116s54.193,12.142,54.193,27.116a13.92,13.92,0,0,1-.1,1.673Z" transform="translate(-183.75 -184.203)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <g id="Group_3337" data-name="Group 3337" transform="translate(11.247 42.662)">
        <path id="Path_11722" data-name="Path 11722" d="M199.072,251.97c0,16.59,26.982,30.037,60.265,30.037S319.6,268.559,319.6,251.97l0-13.128H199.072V251.97Z" transform="translate(-199.072 -208.607)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_11723" data-name="Path 11723" d="M232.229,251.471q3.856.251,7.855.256a117.8,117.8,0,0,0,15.82-1.05v13.1a89.878,89.878,0,0,1-15.82,1.043c-8.528,0-22.4-1.628-31.585-4.451V247.257a103.95,103.95,0,0,0,23.73,4.214Z" transform="translate(-179.819 -191.421)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11724" data-name="Path 11724" d="M232.38,260.711c11.695-5.452,19.051-13.2,19.194-21.813l0,12.957c0,8.68-7.387,16.5-19.2,21.984V260.711Z" transform="translate(-131.047 -208.493)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11725" data-name="Path 11725" d="M211.448,257.065v13.14c-7.764-5.056-12.376-11.378-12.376-18.236V238.842c.036,6.854,4.643,13.167,12.376,18.224Z" transform="translate(-199.072 -208.607)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11726" data-name="Path 11726" d="M228.149,250.368q3.856.251,7.855.256v13.094A112.991,112.991,0,0,1,210.5,260.9V247.8a108.57,108.57,0,0,0,17.652,2.571Z" transform="translate(-175.739 -190.319)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11727" data-name="Path 11727" d="M266.648,268.416a27.365,27.365,0,0,1-.35,4.5.3.3,0,0,1-.6,0,28.881,28.881,0,0,1,0-9,.3.3,0,0,1,.6,0,27.4,27.4,0,0,1,.35,4.5Zm7.934-.8a28.133,28.133,0,0,1-.338,4.5.287.287,0,0,1-.569,0,30.162,30.162,0,0,1,0-9,.287.287,0,0,1,.569,0,28.2,28.2,0,0,1,.338,4.5Zm7.855-1.378a29.828,29.828,0,0,1-.322,4.5.281.281,0,0,1-.274.249.278.278,0,0,1-.271-.249,31.6,31.6,0,0,1,0-9.005.278.278,0,0,1,.271-.246.281.281,0,0,1,.274.246,29.828,29.828,0,0,1,.322,4.5Zm7.715-2.011a31.165,31.165,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.117,33.117,0,0,1,0-9,.26.26,0,0,1,.52,0,31.177,31.177,0,0,1,.307,4.5Zm7.487-2.741a32.716,32.716,0,0,1-.292,4.5.265.265,0,0,1-.246.249.27.27,0,0,1-.246-.249,34.851,34.851,0,0,1,0-9.005.267.267,0,0,1,.246-.246.262.262,0,0,1,.246.246,32.715,32.715,0,0,1,.292,4.5Zm7.116-3.6a34.888,34.888,0,0,1-.277,4.5.262.262,0,0,1-.234.249.257.257,0,0,1-.234-.249,36.728,36.728,0,0,1,0-9,.257.257,0,0,1,.234-.249.262.262,0,0,1,.234.249,34.9,34.9,0,0,1,.277,4.5Zm6.435-4.7a36.775,36.775,0,0,1-.262,4.5.254.254,0,0,1-.222.249c-.113,0-.2-.1-.222-.249a38.85,38.85,0,0,1,0-9c.018-.146.11-.249.222-.249a.254.254,0,0,1,.222.249,36.828,36.828,0,0,1,.262,4.5Zm-61.838,15.23a27.34,27.34,0,0,0,.353,4.5.3.3,0,0,0,.6,0,28.881,28.881,0,0,0,0-9,.3.3,0,0,0-.6,0,27.38,27.38,0,0,0-.353,4.5Zm-7.931-.8a28.515,28.515,0,0,0,.335,4.5.289.289,0,0,0,.572,0,30.168,30.168,0,0,0,0-9,.289.289,0,0,0-.572,0,28.583,28.583,0,0,0-.335,4.5Zm-7.855-1.378a29.815,29.815,0,0,0,.323,4.5.273.273,0,0,0,.545,0,31.592,31.592,0,0,0,0-9.005.274.274,0,0,0-.545,0,29.816,29.816,0,0,0-.323,4.5Zm-7.715-2.011a31.155,31.155,0,0,0,.307,4.5.271.271,0,0,0,.259.249.276.276,0,0,0,.262-.249,33.451,33.451,0,0,0,0-9,.276.276,0,0,0-.262-.249.271.271,0,0,0-.259.249,31.168,31.168,0,0,0-.307,4.5Zm-7.49-2.741a33.2,33.2,0,0,0,.292,4.5.248.248,0,1,0,.5,0,34.859,34.859,0,0,0,0-9.005.248.248,0,0,0-.5,0,33.2,33.2,0,0,0-.292,4.5Zm-7.113-3.6a34.344,34.344,0,0,0,.277,4.5.235.235,0,1,0,.469,0,36.736,36.736,0,0,0,0-9,.235.235,0,1,0-.469,0,34.358,34.358,0,0,0-.277,4.5Zm-6.435-4.7a36.179,36.179,0,0,0,.262,4.5c.018.146.11.249.222.249a.254.254,0,0,0,.222-.249,38.852,38.852,0,0,0,0-9,.254.254,0,0,0-.222-.249c-.113,0-.2.1-.222.249a36.231,36.231,0,0,0-.262,4.5Zm52.51,15.5a26.041,26.041,0,0,0,.368,4.5.319.319,0,0,0,.621,0,27.72,27.72,0,0,0,0-9.005.318.318,0,0,0-.621,0,26.04,26.04,0,0,0-.368,4.5Zm-57.6-21.613a38.239,38.239,0,0,0,.246,4.5c.015.146.1.249.207.249s.192-.1.21-.249a41.225,41.225,0,0,0,0-9c-.018-.146-.1-.249-.21-.249s-.192.1-.207.249a38.331,38.331,0,0,0-.246,4.5Zm116.551,0a38.247,38.247,0,0,1-.246,4.5c-.018.146-.1.249-.21.249s-.192-.1-.207-.249a41.23,41.23,0,0,1,0-9c.015-.146.1-.249.207-.249s.192.1.21.249a38.339,38.339,0,0,1,.246,4.5Z" transform="translate(-197.736 -201.5)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_11728" data-name="Path 11728" d="M199.236,259.527c0,16.836,26.55,30.487,59.3,30.487s59.3-13.651,59.3-30.487-26.547-30.484-59.3-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-198.737 -228.62)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11729" data-name="Path 11729" d="M199.072,259.056c0,16.651,26.982,30.152,60.265,30.152s60.265-13.5,60.265-30.152S292.62,228.9,259.337,228.9s-60.265,13.5-60.265,30.152Z" transform="translate(-199.072 -228.904)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_11730" data-name="Path 11730" d="M200.945,255.477c-.389,14.862,23.73,27.551,53.87,28.342S309.708,273.2,310.1,258.337s-23.73-27.554-53.87-28.342-54.892,10.621-55.282,25.482Z" transform="translate(-195.256 -226.755)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11731" data-name="Path 11731" d="M201.067,257.018c0,14.974,24.266,27.113,54.2,27.113s54.193-12.139,54.193-27.113S285.193,229.9,255.263,229.9s-54.2,12.142-54.2,27.116Z" transform="translate(-194.998 -226.866)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      </g>
      <path id="Path_11732" data-name="Path 11732" d="M200.893,257.046c0,14.807,23.989,26.809,53.581,26.809s53.581-12,53.581-26.809-23.989-26.806-53.581-26.806-53.581,12-53.581,26.806Z" transform="translate(-184.105 -183.513)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_3338" data-name="Group 3338" transform="translate(0 28.443)">
        <path id="Path_11733" data-name="Path 11733" d="M195.375,247.3c0,16.587,26.982,30.037,60.265,30.037S315.9,263.882,315.9,247.3V234.168H195.375V247.3Z" transform="translate(-195.375 -203.933)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_11734" data-name="Path 11734" d="M228.532,246.8q3.856.251,7.855.253a117.844,117.844,0,0,0,15.82-1.05v13.1a89.809,89.809,0,0,1-15.82,1.047c-8.531,0-22.4-1.628-31.585-4.451V242.583a104.11,104.11,0,0,0,23.73,4.214Z" transform="translate(-176.122 -186.747)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11735" data-name="Path 11735" d="M228.683,256.037c11.695-5.452,19.051-13.2,19.194-21.813v12.957c0,8.677-7.384,16.5-19.194,21.984V256.037Z" transform="translate(-127.35 -203.819)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-154)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11736" data-name="Path 11736" d="M207.748,252.391v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V234.168c.036,6.851,4.643,13.167,12.373,18.224Z" transform="translate(-195.375 -203.933)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11737" data-name="Path 11737" d="M224.451,245.7q3.856.251,7.855.253v13.1a112.986,112.986,0,0,1-25.507-2.817V243.122a108.892,108.892,0,0,0,17.651,2.574Z" transform="translate(-172.042 -185.647)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11738" data-name="Path 11738" d="M262.953,263.739a27.081,27.081,0,0,1-.353,4.5.3.3,0,0,1-.6,0,29.125,29.125,0,0,1,0-9,.3.3,0,0,1,.6,0,27.018,27.018,0,0,1,.353,4.5Zm7.934-.8a28.527,28.527,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.437,30.437,0,0,1,0-9,.289.289,0,0,1,.572,0,28.488,28.488,0,0,1,.338,4.5Zm7.852-1.375a29.773,29.773,0,0,1-.319,4.5.275.275,0,0,1-.548,0,31.879,31.879,0,0,1,0-9,.275.275,0,0,1,.548,0,29.846,29.846,0,0,1,.319,4.5Zm7.718-2.014a31.2,31.2,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.124,33.124,0,0,1,0-9,.26.26,0,0,1,.52,0,31.127,31.127,0,0,1,.307,4.5Zm7.487-2.738a32.656,32.656,0,0,1-.292,4.5.246.246,0,1,1-.493,0,34.835,34.835,0,0,1,0-9,.246.246,0,1,1,.493,0,32.7,32.7,0,0,1,.292,4.5Zm7.116-3.6a34.931,34.931,0,0,1-.277,4.5.236.236,0,1,1-.472,0,37.155,37.155,0,0,1,0-9.005.236.236,0,1,1,.472,0,34.929,34.929,0,0,1,.277,4.5Zm6.435-4.7a36.845,36.845,0,0,1-.262,4.5.254.254,0,0,1-.222.249c-.113,0-.2-.1-.222-.249a38.871,38.871,0,0,1,0-9.005.223.223,0,1,1,.444,0,36.845,36.845,0,0,1,.262,4.5Zm-61.838,15.23a27.409,27.409,0,0,0,.353,4.5.3.3,0,0,0,.6,0,28.886,28.886,0,0,0,0-9,.3.3,0,0,0-.6,0,27.344,27.344,0,0,0-.353,4.5Zm-7.931-.8a28.561,28.561,0,0,0,.335,4.5.289.289,0,0,0,.572,0,30.162,30.162,0,0,0,0-9,.289.289,0,0,0-.572,0,28.522,28.522,0,0,0-.335,4.5Zm-7.855-1.375a29.368,29.368,0,0,0,.322,4.5.278.278,0,0,0,.271.249.281.281,0,0,0,.274-.249,31.576,31.576,0,0,0,0-9,.281.281,0,0,0-.274-.249.278.278,0,0,0-.271.249,29.439,29.439,0,0,0-.322,4.5Zm-7.715-2.014a31.232,31.232,0,0,0,.3,4.5.277.277,0,0,0,.262.249.274.274,0,0,0,.259-.249,33.117,33.117,0,0,0,0-9,.271.271,0,0,0-.259-.249.274.274,0,0,0-.262.249,31.157,31.157,0,0,0-.3,4.5Zm-7.49-2.738a32.638,32.638,0,0,0,.292,4.5.248.248,0,1,0,.5,0,34.824,34.824,0,0,0,0-9,.248.248,0,1,0-.5,0,32.685,32.685,0,0,0-.292,4.5Zm-7.113-3.6a34.4,34.4,0,0,0,.277,4.5.235.235,0,1,0,.469,0,36.76,36.76,0,0,0,0-9.005.235.235,0,1,0-.469,0,34.393,34.393,0,0,0-.277,4.5Zm-6.435-4.7a36.243,36.243,0,0,0,.262,4.5c.018.146.109.249.222.249a.254.254,0,0,0,.222-.249,38.874,38.874,0,0,0,0-9.005.223.223,0,1,0-.444,0,36.243,36.243,0,0,0-.262,4.5Zm52.51,15.5a26.337,26.337,0,0,0,.365,4.5.312.312,0,0,0,.313.249.309.309,0,0,0,.31-.249,27.7,27.7,0,0,0,0-9,.309.309,0,0,0-.31-.249.312.312,0,0,0-.313.249,26.347,26.347,0,0,0-.365,4.5Zm-57.6-21.616a38.979,38.979,0,0,0,.246,4.5c.018.146.107.249.21.249s.192-.1.21-.249a41.23,41.23,0,0,0,0-9c-.018-.146-.106-.249-.21-.249s-.192.1-.21.249a38.925,38.925,0,0,0-.246,4.5Zm116.554,0a38.964,38.964,0,0,1-.246,4.5c-.018.146-.1.249-.21.249s-.192-.1-.207-.249a40.729,40.729,0,0,1,0-9c.015-.146.1-.249.207-.249s.192.1.21.249a38.91,38.91,0,0,1,.246,4.5Z" transform="translate(-194.041 -196.826)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_11739" data-name="Path 11739" d="M195.539,254.855c0,16.836,26.547,30.484,59.3,30.484s59.3-13.648,59.3-30.484-26.55-30.487-59.3-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-195.04 -223.948)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11740" data-name="Path 11740" d="M195.375,254.379c0,16.654,26.982,30.152,60.265,30.152s60.262-13.5,60.262-30.152S288.923,224.23,255.64,224.23s-60.265,13.5-60.265,30.149Z" transform="translate(-195.375 -224.23)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_11741" data-name="Path 11741" d="M197.248,250.8c-.389,14.862,23.73,27.551,53.87,28.339s54.892-10.618,55.279-25.482-23.727-27.551-53.87-28.339-54.889,10.618-55.279,25.482Z" transform="translate(-191.559 -222.081)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11742" data-name="Path 11742" d="M197.37,252.341c0,14.977,24.266,27.116,54.2,27.116s54.193-12.139,54.193-27.116-24.262-27.113-54.193-27.113-54.2,12.139-54.2,27.113Z" transform="translate(-191.301 -222.192)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
        <path id="Path_11743" data-name="Path 11743" d="M305.658,254.017c-1.728-14.2-25.288-25.443-54.092-25.443s-52.364,11.247-54.092,25.443a13.967,13.967,0,0,1-.1-1.676c0-14.974,24.266-27.113,54.2-27.113s54.193,12.139,54.193,27.113a13.976,13.976,0,0,1-.1,1.676Z" transform="translate(-191.301 -222.192)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      </g>
      <path id="Path_11744" data-name="Path 11744" d="M197.724,252.319c0,14.807,23.989,26.809,53.581,26.809s53.584-12,53.584-26.809S280.9,225.51,251.305,225.51s-53.581,12-53.581,26.809Z" transform="translate(-190.578 -193.173)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_3339" data-name="Group 3339" transform="translate(8.999 14.22)">
        <path id="Path_11745" data-name="Path 11745" d="M198.333,242.621c0,16.59,26.979,30.04,60.262,30.04s60.265-13.45,60.265-30.04V229.493H198.333v13.128Z" transform="translate(-198.333 -199.258)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_11746" data-name="Path 11746" d="M231.487,242.122q3.856.251,7.855.256a117.851,117.851,0,0,0,15.823-1.05v13.1a89.823,89.823,0,0,1-15.823,1.047c-8.528,0-22.4-1.631-31.582-4.451V237.908a103.923,103.923,0,0,0,23.727,4.214Z" transform="translate(-179.08 -182.072)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11747" data-name="Path 11747" d="M231.64,251.36c11.7-5.449,19.054-13.2,19.2-21.81V242.5c0,8.68-7.387,16.5-19.2,21.984V251.36Z" transform="translate(-130.31 -199.142)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11748" data-name="Path 11748" d="M210.706,247.716v13.143c-7.761-5.059-12.373-11.378-12.373-18.239V229.493c.037,6.854,4.643,13.17,12.373,18.224Z" transform="translate(-198.333 -199.258)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-164)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11749" data-name="Path 11749" d="M227.406,241.019q3.856.251,7.855.256v13.1a112.98,112.98,0,0,1-25.5-2.817V238.448a108.529,108.529,0,0,0,17.648,2.571Z" transform="translate(-175 -180.969)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11750" data-name="Path 11750" d="M265.911,259.067a27.012,27.012,0,0,1-.353,4.5.3.3,0,0,1-.6,0,28.881,28.881,0,0,1,0-9,.3.3,0,0,1,.6,0,27.049,27.049,0,0,1,.353,4.5Zm7.934-.8a28.569,28.569,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.191,30.191,0,0,1,0-9.005.289.289,0,0,1,.572,0,28.568,28.568,0,0,1,.338,4.5Zm7.852-1.378a29.816,29.816,0,0,1-.319,4.5.275.275,0,0,1-.548,0,31.568,31.568,0,0,1,0-9,.275.275,0,0,1,.548,0,29.773,29.773,0,0,1,.319,4.5Zm7.715-2.011a31.156,31.156,0,0,1-.3,4.5.274.274,0,0,1-.262.249.271.271,0,0,1-.259-.249,33.116,33.116,0,0,1,0-9,.271.271,0,0,1,.259-.249.274.274,0,0,1,.262.249,31.2,31.2,0,0,1,.3,4.5Zm7.49-2.741a32.725,32.725,0,0,1-.292,4.5.248.248,0,1,1-.5,0,34.855,34.855,0,0,1,0-9.005.248.248,0,0,1,.5,0,32.726,32.726,0,0,1,.292,4.5Zm7.113-3.6a34.312,34.312,0,0,1-.277,4.5.257.257,0,0,1-.234.249.262.262,0,0,1-.234-.249,36.732,36.732,0,0,1,0-9,.262.262,0,0,1,.234-.249.257.257,0,0,1,.234.249,34.363,34.363,0,0,1,.277,4.5Zm6.434-4.7a36.178,36.178,0,0,1-.262,4.5c-.018.146-.11.249-.222.249s-.2-.1-.219-.249a38.4,38.4,0,0,1,0-9c.018-.146.109-.249.219-.249s.2.1.222.249a36.266,36.266,0,0,1,.262,4.5Zm-61.835,15.23a27.019,27.019,0,0,0,.353,4.5.3.3,0,0,0,.6,0,29.128,29.128,0,0,0,0-9,.3.3,0,0,0-.6,0,27.056,27.056,0,0,0-.353,4.5Zm-7.934-.8a28.561,28.561,0,0,0,.338,4.5.289.289,0,0,0,.572,0,30.455,30.455,0,0,0,0-9.005.289.289,0,0,0-.572,0,28.561,28.561,0,0,0-.338,4.5Zm-7.852-1.378a29.824,29.824,0,0,0,.319,4.5.275.275,0,0,0,.548,0,31.87,31.87,0,0,0,0-9,.275.275,0,0,0-.548,0,29.782,29.782,0,0,0-.319,4.5Zm-7.718-2.011a31.134,31.134,0,0,0,.307,4.5.26.26,0,0,0,.52,0,33.12,33.12,0,0,0,0-9,.26.26,0,0,0-.52,0,31.178,31.178,0,0,0-.307,4.5Zm-7.487-2.741a32.717,32.717,0,0,0,.292,4.5.267.267,0,0,0,.246.249.262.262,0,0,0,.246-.249,34.849,34.849,0,0,0,0-9.005.262.262,0,0,0-.246-.246.267.267,0,0,0-.246.246,32.718,32.718,0,0,0-.292,4.5Zm-7.113-3.6a34.892,34.892,0,0,0,.274,4.5.236.236,0,1,0,.472,0,36.73,36.73,0,0,0,0-9,.236.236,0,1,0-.472,0,34.944,34.944,0,0,0-.274,4.5Zm-6.435-4.7a36.18,36.18,0,0,0,.262,4.5c.018.146.11.249.219.249s.2-.1.222-.249a38.85,38.85,0,0,0,0-9c-.018-.146-.109-.249-.222-.249s-.2.1-.219.249a36.269,36.269,0,0,0-.262,4.5Zm52.51,15.5a26.365,26.365,0,0,0,.365,4.5.312.312,0,0,0,.31.249.315.315,0,0,0,.313-.249,27.949,27.949,0,0,0,0-9.005.313.313,0,0,0-.313-.246.309.309,0,0,0-.31.246,26.365,26.365,0,0,0-.365,4.5Zm-57.6-21.613a38.913,38.913,0,0,0,.246,4.5c.018.146.106.249.21.249s.192-.1.21-.249a41.231,41.231,0,0,0,0-9c-.018-.146-.106-.249-.21-.249s-.192.1-.21.249a39.008,39.008,0,0,0-.246,4.5Zm116.554,0a38.917,38.917,0,0,1-.246,4.5c-.018.146-.106.249-.21.249s-.192-.1-.21-.249a41.226,41.226,0,0,1,0-9c.018-.146.106-.249.21-.249s.192.1.21.249a39.013,39.013,0,0,1,.246,4.5Z" transform="translate(-196.999 -192.151)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_11751" data-name="Path 11751" d="M198.5,250.178c0,16.839,26.547,30.487,59.3,30.487s59.295-13.648,59.295-30.487-26.547-30.484-59.295-30.484-59.3,13.648-59.3,30.484Z" transform="translate(-197.998 -219.271)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11752" data-name="Path 11752" d="M198.333,249.707c0,16.654,26.979,30.152,60.262,30.152s60.265-13.5,60.265-30.152-26.979-30.152-60.265-30.152-60.262,13.5-60.262,30.152Z" transform="translate(-198.333 -219.555)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_11753" data-name="Path 11753" d="M200.206,246.128c-.389,14.865,23.73,27.551,53.87,28.342s54.889-10.621,55.279-25.482-23.73-27.551-53.87-28.342-54.889,10.621-55.279,25.482Z" transform="translate(-194.517 -217.406)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11754" data-name="Path 11754" d="M200.328,247.669c0,14.974,24.262,27.116,54.193,27.116s54.2-12.142,54.2-27.116-24.262-27.116-54.2-27.116-54.193,12.142-54.193,27.116Z" transform="translate(-194.259 -217.517)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
        <path id="Path_11755" data-name="Path 11755" d="M308.616,249.342c-1.731-14.2-25.288-25.44-54.1-25.44s-52.364,11.244-54.092,25.44a13.923,13.923,0,0,1-.1-1.673c0-14.974,24.262-27.116,54.193-27.116s54.2,12.142,54.2,27.116a13.919,13.919,0,0,1-.1,1.673Z" transform="translate(-194.259 -217.517)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      </g>
      <path id="Path_11756" data-name="Path 11756" d="M199.377,247.635c0,14.807,23.992,26.809,53.584,26.809s53.581-12,53.581-26.809-23.989-26.809-53.581-26.809-53.584,12-53.584,26.809Z" transform="translate(-187.202 -202.739)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_3340" data-name="Group 3340" transform="translate(15.747)">
        <path id="Path_11757" data-name="Path 11757" d="M200.551,237.947c0,16.587,26.982,30.037,60.265,30.037s60.262-13.45,60.262-30.037V224.819H200.551v13.128Z" transform="translate(-200.551 -194.584)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_11758" data-name="Path 11758" d="M233.708,237.448q3.856.251,7.855.253a117.825,117.825,0,0,0,15.82-1.05v13.1a89.514,89.514,0,0,1-15.82,1.047c-8.528,0-22.4-1.628-31.585-4.451V233.234a104.109,104.109,0,0,0,23.73,4.214Z" transform="translate(-181.298 -177.398)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11759" data-name="Path 11759" d="M233.859,246.688c11.695-5.452,19.051-13.2,19.194-21.813v12.957c0,8.68-7.384,16.5-19.194,21.984V246.688Z" transform="translate(-132.526 -194.47)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-154)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11760" data-name="Path 11760" d="M212.924,243.042v13.14c-7.761-5.056-12.373-11.378-12.373-18.236V224.819c.037,6.851,4.643,13.167,12.373,18.223Z" transform="translate(-200.551 -194.584)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_11761" data-name="Path 11761" d="M229.628,236.345q3.856.251,7.855.252v13.1a112.987,112.987,0,0,1-25.507-2.817v-13.1a108.9,108.9,0,0,0,17.652,2.571Z" transform="translate(-177.217 -176.296)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11762" data-name="Path 11762" d="M268.129,254.39a27.4,27.4,0,0,1-.35,4.5.3.3,0,0,1-.3.249.3.3,0,0,1-.3-.249,29.13,29.13,0,0,1,0-9,.3.3,0,0,1,.3-.249.3.3,0,0,1,.3.249,27.365,27.365,0,0,1,.35,4.5Zm7.934-.8a28.543,28.543,0,0,1-.338,4.5.289.289,0,0,1-.572,0,30.44,30.44,0,0,1,0-9,.289.289,0,0,1,.572,0,28.531,28.531,0,0,1,.338,4.5Zm7.855-1.375a29.825,29.825,0,0,1-.322,4.5.283.283,0,0,1-.274.246.278.278,0,0,1-.271-.246,31.6,31.6,0,0,1,0-9.005.278.278,0,0,1,.271-.249.283.283,0,0,1,.274.249,29.823,29.823,0,0,1,.322,4.5Zm7.715-2.014a31.167,31.167,0,0,1-.307,4.5.26.26,0,0,1-.52,0,33.11,33.11,0,0,1,0-9,.26.26,0,0,1,.52,0,31.123,31.123,0,0,1,.307,4.5Zm7.487-2.738a32.643,32.643,0,0,1-.292,4.5.246.246,0,1,1-.493,0,34.828,34.828,0,0,1,0-9,.246.246,0,1,1,.493,0,32.72,32.72,0,0,1,.292,4.5Zm7.116-3.6a34.926,34.926,0,0,1-.277,4.5.261.261,0,0,1-.234.249.257.257,0,0,1-.234-.249,36.753,36.753,0,0,1,0-9.005.256.256,0,0,1,.234-.246.261.261,0,0,1,.234.246,34.925,34.925,0,0,1,.277,4.5Zm6.435-4.7a36.816,36.816,0,0,1-.262,4.5.254.254,0,0,1-.222.249c-.113,0-.2-.1-.222-.249a38.846,38.846,0,0,1,0-9c.018-.146.11-.249.222-.249a.254.254,0,0,1,.222.249,36.764,36.764,0,0,1,.262,4.5Zm-61.838,15.23a27.386,27.386,0,0,0,.353,4.5.3.3,0,0,0,.6,0,28.874,28.874,0,0,0,0-9,.3.3,0,0,0-.6,0,27.348,27.348,0,0,0-.353,4.5Zm-7.931-.8a28.555,28.555,0,0,0,.335,4.5.289.289,0,0,0,.572,0,30.162,30.162,0,0,0,0-9,.289.289,0,0,0-.572,0,28.543,28.543,0,0,0-.335,4.5Zm-7.855-1.375a29.424,29.424,0,0,0,.322,4.5.278.278,0,0,0,.271.246.281.281,0,0,0,.274-.246,31.6,31.6,0,0,0,0-9.005.281.281,0,0,0-.274-.249.278.278,0,0,0-.271.249,29.423,29.423,0,0,0-.322,4.5Zm-7.715-2.014a31.16,31.16,0,0,0,.307,4.5.259.259,0,0,0,.517,0,33.117,33.117,0,0,0,0-9,.259.259,0,0,0-.517,0,31.116,31.116,0,0,0-.307,4.5Zm-7.49-2.738a32.648,32.648,0,0,0,.292,4.5.248.248,0,1,0,.5,0,34.837,34.837,0,0,0,0-9,.248.248,0,1,0-.5,0,32.725,32.725,0,0,0-.292,4.5Zm-7.113-3.6a34.4,34.4,0,0,0,.277,4.5.235.235,0,1,0,.469,0,36.751,36.751,0,0,0,0-9.005.235.235,0,1,0-.469,0,34.4,34.4,0,0,0-.277,4.5Zm-6.435-4.7a36.226,36.226,0,0,0,.262,4.5c.018.146.109.249.222.249a.254.254,0,0,0,.222-.249,38.841,38.841,0,0,0,0-9,.254.254,0,0,0-.222-.249c-.113,0-.2.1-.222.249a36.175,36.175,0,0,0-.262,4.5Zm52.51,15.5a26.3,26.3,0,0,0,.365,4.5.312.312,0,0,0,.313.249.309.309,0,0,0,.31-.249,27.7,27.7,0,0,0,0-9,.309.309,0,0,0-.31-.249.312.312,0,0,0-.313.249,26.337,26.337,0,0,0-.365,4.5Zm-57.6-21.616a38.957,38.957,0,0,0,.246,4.5c.018.146.106.249.21.249s.192-.1.21-.249a41.237,41.237,0,0,0,0-9c-.018-.146-.106-.249-.21-.249s-.192.1-.21.249a38.9,38.9,0,0,0-.246,4.5Zm116.554,0a38.965,38.965,0,0,1-.246,4.5c-.018.146-.1.249-.21.249s-.192-.1-.207-.249a41.235,41.235,0,0,1,0-9c.015-.146.1-.249.207-.249s.192.1.21.249a38.909,38.909,0,0,1,.246,4.5Z" transform="translate(-199.217 -187.477)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_11763" data-name="Path 11763" d="M200.715,245.506c0,16.836,26.547,30.484,59.3,30.484s59.3-13.648,59.3-30.484-26.55-30.487-59.3-30.487-59.3,13.651-59.3,30.487Z" transform="translate(-200.216 -214.599)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11764" data-name="Path 11764" d="M200.551,245.03c0,16.654,26.982,30.152,60.265,30.152s60.262-13.5,60.262-30.152-26.979-30.149-60.262-30.149-60.265,13.5-60.265,30.149Z" transform="translate(-200.551 -214.881)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_11765" data-name="Path 11765" d="M202.424,241.454c-.389,14.862,23.73,27.551,53.87,28.339s54.893-10.618,55.279-25.482-23.727-27.551-53.87-28.339-54.889,10.618-55.279,25.482Z" transform="translate(-196.735 -212.732)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_11766" data-name="Path 11766" d="M202.546,242.992c0,14.977,24.265,27.116,54.2,27.116s54.193-12.139,54.193-27.116-24.263-27.113-54.193-27.113-54.2,12.139-54.2,27.113Z" transform="translate(-196.477 -212.843)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
        <path id="Path_11767" data-name="Path 11767" d="M310.834,244.668c-1.728-14.2-25.285-25.443-54.092-25.443s-52.364,11.247-54.092,25.443a13.981,13.981,0,0,1-.1-1.676c0-14.974,24.265-27.113,54.2-27.113s54.193,12.139,54.193,27.113a13.97,13.97,0,0,1-.1,1.676Z" transform="translate(-196.477 -212.843)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_11768" data-name="Path 11768" d="M256.672,216.979c-27.722,0-50.585,10.417-53.806,23.852a14.176,14.176,0,0,0-.286,1.591,14.325,14.325,0,0,0,.514,2.428,17.818,17.818,0,0,0,3.137,5.832l.478-.07,67.223-9.781,34.369-5c-6.985-10.931-27.46-18.85-51.628-18.85Z" transform="translate(-196.407 -210.596)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_11769" data-name="Path 11769" d="M206.24,243.6c.347-13.231,20-23.727,45.571-25.315q4.395-.365,9.005-.371c27.144,0,49.626,9.985,53.578,23.012.14.362.268.727.38,1.1l5.817-.846c-3.782-14.834-29.1-26.3-59.775-26.3-33.283,0-60.265,13.5-60.265,30.149v.085c.024,4.539,2.057,8.847,5.674,12.7l5.908-.861a24.634,24.634,0,0,1-1.758-1.993,17.818,17.818,0,0,1-3.137-5.832,14.324,14.324,0,0,1-.952-4.019,13.918,13.918,0,0,1-.046-1.512Z" transform="translate(-200.551 -214.881)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      </g>
    </g>
  </g>
</svg>
