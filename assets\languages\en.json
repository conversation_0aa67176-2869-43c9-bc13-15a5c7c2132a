{"termAgreement": "By SigningUp/Logging In, You agree to our", "termOfService": "Terms of Service", "andLbl": "and", "privacyPolicy": "Privacy Policy", "youLbl": "You", "adminLbl": "ScrumPass", "agreeLbl": "Agree", "otpVerificationLbl": "OTP\n Verification", "otpSendLbl": "OTP has been sent to", "submitBtn": "Start the Test", "resetLbl": "Resend Code in", "resendBtn": "Resend OTP", "resendSnackBar": "Request new OTP after 60 seconds", "noInterNetSnackBar": "Please check your connection again, or connect to Wi-Fi", "enterOtpMsg": "Please enter Otp", "enterNumberLbl": "Enter Your \nMobile Number", "receiveOtpLbl": "you'll Receive a 6 digit code to verify next.", "countryLbl": "Country", "validMobMsg": "Please enter valid mobile number", "requestOtpLbl": "Request OTP", "otpNotMatch": "Otp does Not Match", "requiredToPass": "Percent required to pass", "userLoginLbl": "User Login", "emailRequiredMsg": "Email is required", "validEmail": "Enter valid email", "emailLbl": "Email", "pwdLengthMsg": "Password should be more then 6 char long", "pwdLbl": "Password", "loginLbl": "<PERSON><PERSON>", "enterEmailLbl": "Enter email address", "forgotPwdLbl": "Forgot Password", "resetPwdLbl": "Reset Password", "resetEnterEmailLbl": "Enter the email address associated with your account", "pwdResetLinkLbl": "Password reset link has been sent to your mail", "orLbl": "OR", "loginSocialMediaLbl": "Connect with one of the following Option", "noAccountLbl": "Don't have an account? ", "signUpLbl": "Sign Up", "submittingButton": "Submitting", "PassTime": "Passed <PERSON>am", "cnPwdNotMatchMsg": "Confirm password not match", "cnPwdLbl": "Confirm Password", "alreadyAccountLbl": "Already have an account?", "emailVerify": "Verification email sent to", "liveChatLbl": "Live Chat", "entryLbl": "Entry", "creatingLoadingLbl": "Creating..", "creatingLbl": "Create", "enterCodeLbl": "Enter Code", "joiningLoadingLbl": "Joining..", "joinLbl": "Join", "waitOtherComplete": "Wait For Other to complete...", "waitingLbl": "Waiting..", "roomDeletedOwnerLbl": "Room deleted by owner", "okayLbl": "Okay", "entryAmountLbl": "Entry Amount", "roomCodeLbl": "Room code", "shareRoomCodeLbl": "Share this room code to friends \n and ask them to join", "vsLbl": "VS", "startLbl": "Start", "findingOpponentLbl": "Finding opponent...", "foundOpponentLbl": "Found opponent", "getReadyLbl": "Get Ready For Quiz", "bestOfLuckLbl": "Best Of Luck", "opponentNotFoundLbl": "Opponent not found.\nTry later!", "retryLbl": "Retry", "youWonLbl": "You won", "opponentLeftLbl": "Opponent left the game", "youLeftLbl": "You left the game", "everyOneLeftLbl": "Everyone left the game", "resultLbl": "RESULT", "youLostLbl": "YOU LOST", "exitLbl": "Exit", "referralCodeLbl": "Referral Code", "enterReferralCodeLbl": "Enter referral code", "selfChallengeLbl": "Self Challenge Mode", "challengeYourselfLbl": "Challenge yourself", "photoLibraryLbl": "Photo Library", "cameraLbl": "Camera", "updatingLbl": "Updating", "updateLbl": "Update", "enterValidEmailMsg": "Please enter valid email", "statisticLabel": "Analysis your study", "scrumpass": "ScrumPass", "enterValidNameMsg": "Please enter valid name", "submitTestConfirmation": "Are you sure to end the test?", "bookmarkLbl": "Bookmarks", "howToPlayLbl": "How to Use", "inviteFriendsLbl": "Invite Friends", "contactUs": "Contact Us", "aboutUs": "About Us", "termsAndConditions": "Terms & Conditions", "rateUsLbl": "Rate Us", "shareAppLbl": "Share App", "logoutLbl": "Logout", "logoutDialogLbl": "Are you sure to logout?", "yesBtn": "Yes", "noBtn": "No", "profileLbl": "Profile", "nameLbl": "Name", "notEditNumberMsg": "Can't edit mobile number", "mobileNumberLbl": "Mobile Number", "notEditMailLbl": "Can't edit email", "selectProfilePhotoLbl": "Select Profile Photo", "selectProfileLbl": "Please select profile photo", "uploadProfilePictureLbl": "uploadProfilePicture", "continueLbl": "Continue", "enterNameLbl": "Enter your name", "completedLbl": "Completed", "playingLbl": "Playing", "completeAllQueLbl": "Completed all questions", "goBAckLbl": "Go Back", "contestLeaderBoardLbl": "Contest LeaderBoard", "pastLbl": "PAST", "liveLbl": "LIVE", "upcomingLbl": "UPCOMING", "contestLbl": "Contest", "noPastGameLbl": "no past game", "entryFeesLbl": "Entry fees", "endsOnLbl": "Ends On", "playersLbl": "players", "leaderboardLbl": "Leaderboard", "playLbl": "Play", "questionLbl": "Question", "totalOf": "of", "next": "Next", "finish": "Finish", "previous": "Previous", "allQuestion": "All Questions", "answered": "Answered", "unAnswered": "Not Answered", "remainTime": "Remaining Time", "questionList": "Question List", "questionDone": "Questions Done", "timeDetails": "Time Details", "mustSelectAnswer": "Please select all that apply", "confirmEndQuiz": "Do you really want to finish the Test?", "quitQuizDialog": "Are you sure to quit the test?", "timeUp": "Time is over", "timeUpDialog": "Time is up", "victoryLbl": "VICTORY", "congratulationsLbl": "Congratulations", "defeatLbl": "DEFEAT", "betterNextLbl": "Better luck next time", "winnerLbl": "Passed", "youLossLbl": "Failed", "matchDrawLbl": "MATCH DRAW", "looserLbl": "<PERSON><PERSON><PERSON>", "playAgainBtn": "PLAY AGAIN", "nextLevelBtn": "NEXT LEVEL", "shareScoreBtn": "SHARE YOUR SCORE", "myScoreLbl": "My Quiz Score", "reviewAnsBtn": "REVIEW ANSWERS", "anotherOpponentBtn": "ANOTHER OPPONENT", "homeBtn": "HOME", "notesLbl": "Notes", "yourAnsLbl": "Your Answer", "correctAndLbl": "Correct Answer ", "theRemainingAnswer": "The Remaining Answers", "reviewAnswerLbl": "Review Answer", "practiceMode": "Practice Mode", "examList": "Test List", "oneToOneLbl": "ONE TO ONE", "privateRoomLbl": "PRIVATE ROOM", "publicRoomLbl": "PUBLIC ROOM", "attemptedLbl": "Answered", "unAttemptedLbl": "No answer selected", "selfChallenge": "Self Challenge", "selectNoQusLbl": "Select Number Of Questions", "selectTimeLbl": "Select Time Period In Minutes", "levelLbl": "Level", "noBookmarkQueLbl": "No bookmarked questions", "playBookmarkBtn": "Play Bookmark", "coinsLbl": "Coins", "offerLbl": "Offer", "storeLbl": "Store", "title1": "EXAM PREPARATION", "title2": "REAL EXAM", "title3": "LEARNING ASSISTANT", "description1": "Big database questions maintained and updated with answers", "description2": "Experience studying and taking exams like the real exam", "description3": "Support and help you study effectively and achieve the best results", "description": "Explanation", "dailyLbl": "Daily", "monthLbl": "Month", "allTimeLbl": "All time", "referAndEarn": "REFER AND EARN", "referFrdLbl": "Invite your friends to join and earn coins as reward.", "yourRefCOdeLbl": "YOUR REFERRAL CODE", "referCodeCopyMsg": "Referral code copied", "shareNowLbl": "Share Now", "quizFanLbl": "Quiz Fan", "completeSubTitle": "Complete 10 game Complete 10 game ", "rewardsLbl": "Rewards", "quizLbl": "The ScrumPass Exam Tool App", "quizExitLbl": "Are you sure, you want to exit?", "fontSizeLbl": "Font Size", "soundLbl": "Sound", "vibrationLbl": "Vibration", "settingLbl": "Settings", "quizZone": "Test Mode", "dailyQuiz": "Daily Quiz", "groupPlay": "Group Battle", "battleQuiz": "1 v/s 1 Battle", "contest": "Contest", "guessTheWord": "Guess The Word", "funAndLearn": "Fun 'N' Learn", "trueAndFalse": "True / False", "desQuizZone": "Select your favorite zone to play", "desDailyQuiz": "Daily basic new quiz game", "desGroupPlay": "It's a group quiz battle", "desBattleQuiz": "Battle with one on one", "desContest": "Play quiz contest", "desGuessTheWord": "Fun vocabulary game", "desFunAndLearn": "it's like a Comprehension game", "desTrueAndFalse": "Quiz with True / False format", "noCoinsMsg": "No enough coins to play contest", "defaultErrorMessage": "Something went wrong. Please try again later", "invalid-email": "<PERSON><PERSON> is invalid", "user-disabled:": "Email or password is incorrect", "user-not-found": "User not found", "wrong-password": "Password is incorrect", "account-exists-with-different-credential": "Account already exist", "invalid-credential": "Credential is invalid", "operation-not-allowed": "Operation not allowed for this credential", "invalid-verification-code": "Verification code is invalid", "invalid-verification-id": "Verification id is invalid", "noInternet": "No Internet", "dataNotFound": "Unable to find data", "invalidHash": "Invalid hash", "fillAllData": "Please fill all data", "fileUploadFail": "Unable to upload image", "dailyQuizAlreadyPlayed": "Already played", "noData": "No Data", "noMatchesPlayed": "No matches played, yet", "noUpcomingContest": "No upcoming contest", "noContest": "No contest", "notPlayedContest": "Not played contest", "contestAlreadyPlayed": "Already played", "roomAlreadyCreated": "Room already created", "unauthorizedAccess": "Unauthorized access", "levelLocked": "Level is locked", "updateBookmarkFailure": "Unable to update bookmark status", "lifeLineUsed": "Already used this lifeline", "notEnoughCoins": "Not enough coins", "notesNotAvailable": "Notes not available", "selectAllValues": "Please select all values", "canNotStartGame": "Can not start game", "roomCodeInvalid": "Roomcode is invalid", "gameStarted": "Game already started", "roomIsFull": "Room is full", "uploadingBtn": "Uploading...", "rankLbl": "Rank", "scoreLbl": "Score", "otpNotMatchMsg": "enter valid Otp", "unabaleToCreateRoom": "Unable to create room", "unableToFindRoom": "Unable to find room", "unableToJoinRoom": "Unable to join room", "unableToSubmitAnswer": "Unable to submit answer", "iHaveInviteCode": "I have an invite code", "cancel": "Cancel", "reportLbl": "Report", "reportQuestion": "Report", "enterReason": "Enter your reason", "letsStart": "Let's Start", "showAdsLbl": "Would like to watch ad to get more coins?", "email-already-in-use": "Email already in use", "weak-password": "Password is weak", "verifyEmail": "Please verify your email", "createRoom": "Create Room", "joinRoom": "Join Room", "accountDeactivated": "Your account has been deactivated by the admin", "currentlyNotAvailable": "Currently not available", "notificationLbl": "Notification", "notAvailable": "Not Available", "coinStore": "Coin Store", "purchaseError": "Unable to make a purchase", "productsFetchedFailure": "Failed to get products", "noProducts": "No products available", "inAppPurchaseUnavailable": "In-app purchase is not available", "coinsBoughtSuccess": "Coins bought successfully", "theme": "Theme", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "selectCategory": "Select Category", "selectSubCategory": "Select Subcategory", "language": "Language", "account": "Account", "aboutQuizApp": "About", "chat": "Cha<PERSON>", "messages": "Messages", "emojis": "Emojis", "badges": "Badges", "mathMania": "Math Mania", "audioQuestions": "Audio Questions", "desAudioQuestions": "Quiz with audio", "randomLbl": "Random Battle", "playWithFrdLbl": "Play With Friends", "desMathMania": "It's math quiz", "showOptions": "Show Options", "enterRoomCodeHere": "Enter room code here", "currentCoins": "Current Coins", "pleaseSelectCategory": "Please select category", "moreThanZeroCoins": "Please enter more than zero coins", "updateApplication": "There is an update available. Please update to use this app", "update": "Update", "warning": "Warning", "failedToGetAppUrl": "Failed to get app url, Please update manually", "youWin": "You win", "letsPlay": "Let's Play", "needMore": "Need more", "correctAnswerToUnlock": "correct ansewer to unlock", "get": "Get", "coinsUnlockingByBadge": "coin(s) by unlocking this badge", "totalRewardsEarned": "Total Rewards Earned", "totalTime": "Total test time", "averageEach": "Each Question", "byUnlocking": "By Unlocking", "scratchHere": "Scratch Here!", "noRewards": "No Rewards", "questions": "Questions", "back": "Back", "exam": "Exam <PERSON>", "desExam": "Take exam", "hint": "Hint", "tournament": "Tournament", "desTournament": "Play exiciting tournament", "myRank": "My Rank", "alreadyInExam": "You are already in exam room. Can't give exam", "enterValidExamKey": "Please enter valid exam key", "enterExamKey": "Enter exam key", "examResult": "<PERSON><PERSON>", "examDuration": "Duration", "completedIn": "Completed In", "totalQuestions": "Total Questions", "obtainedMarks": "Obtained Marks", "mark": "Marked Questions", "total": "Total", "recent": "Recent Score", "average": "Average Score", "totalQuiz": "Test done", "correct": "Correct", "incorrect": "Incorrect", "youLeftTheExam": "You left the exam", "noExamForToday": "No exam for today", "haveNotCompletedExam": "Have not completed any exam yet", "iAgreeWithExamRules": "I agree with exam rules", "pleaseAcceptExamRules": "Please accept exam rules", "examRules": "Exam <PERSON>s", "viewAllRules": "View All Rules", "statisticsLabel": "Statistics", "hello": "Hello", "collectedBadges": "Collected Badges", "quizDetails": "Test Details", "quizDetailsExam": "<PERSON><PERSON>", "quizDetailsPractice": "Test Details", "questionDetails": "Question Details", "battleStatistics": "Battle Statistics", "viewAll": "View All", "played": "Played", "won": "Won", "lost": "Lost", "deleteAccount": "Delete Account", "requires-recent-login": "To delete account sign-in again and delete account immediate.", "deleteAccountConfirmation": "Are you sure to delete your account?", "deletingAccount": "Deleting Account...", "accountDeletedSuccessfully": "Account deleted successfully", "coinHistory": "Coin History", "wallet": "Wallet", "request": "Request", "transaction": "Transaction", "redeemableAmount": "Redeemable Amount", "totalCoins": "Total Coins", "redeemNow": "Redeem Now", "minimumRedeemableAmount": "Minimum redeemable amount is", "notEnoughCoinsToRedeemAmount": "You don't have enough coins to redeem this amount", "totalEarnings": "Total Earnings", "redeemRequest": "Redeem Request", "wonQuizZone": "Won Quiz", "wonBattle": "Won Battle", "usedSkiplifeline": "Used Skip lifeline", "usedAudiencePolllifeline": "Used Audience-poll lifeline", "usedResetTimerlifeline": "Used Reset-timer lifeline", "used5050lifeline": "Used 50-50 lifeline", "usedHintLifeline": "Used Hint lifeline", "referredCodeToFriend": "Referred Code To Friend", "rewardByScratchingCard": "Reward By Scratching Card", "boughtCoins": "Bought Coins", "watchedRewardAd": "Watched Reward Ad", "wonAudioQuiz": "Won Audio Quiz", "wonDailyQuiz": "Won Daily Quiz", "wonTrueFalse": "Won True / False Quiz", "wonFunNLearn": "Won Fun N Learn Quiz", "wonGuessTheWord": "Won Guess The Word Quiz", "wonGroupBattle": "Won Group Battle", "playedGroupBattle": "Played Group Battle", "wonContest": "Won Contest", "playedContest": "Played Contest", "playedBattle": "Played Battle", "reversedByAdmin": "Reversed By <PERSON><PERSON>", "usedReferCode": "Used Refer Code", "welcomeBonus": "Welcome Bonus", "pending": "Pending", "completed": "Completed", "wrongDetails": "Wrong Details", "wrongAnswers": "Wrong Questions", "wrongQuestions": "Wrong Questions", "coinsWillBeDeducted": "Coins will be deducted", "selectPayoutOption": "Select payout option", "successfullyRequested": "Successfully requested", "trackRequest": "Track Request", "payoutMethod": "Payout method", "requesting": "Requesting...", "makeRequest": "Make Request", "pleaseFillAllData": "Please fill all data", "changePayoutMethod": "Change payout method", "noTransactions": "No Transactions", "accountHasBeenDeactive": "Your account has been deactive! please contact admin", "canNotMakeRequest": "You have already made a payment request. Please wait for 48 hours after you made the previous request", "appUnderMaintenance": "We apologize for the inconvenience, but we are performing some maintenance. We will back soon", "youWillGet": "You will get", "theyWillGet": "They will get", "noLimit": "No Limit", "minute": "Minutes", "wonMathQuiz": "Won Math Quiz", "groupLbl": "Group", "practiceModeLbl": "Practice Mode", "examModeLbl": "Exam <PERSON>", "reviewQuestionLbl": "Review Questions", "statisticsLbl": "Statistics", "practiceAnytimeLbl": "Practice Anytime", "comingSoon": "Coming Soon", "desReviewQuestion": "Close the gap", "markedQuestions": "Bookmarked Questions", "correctAnswers": "Correct Answers", "unanswerQuestions": "No answer selected", "desStatistics": "Analysis your study", "desReport": "Mentor Support", "processing": "Pending", "updatedDate": "Updated Date", "status": "Status", "response": "Response", "reportContent": "Report Content", "submitExam": "Submit", "confirmSubmitQuiz": "Do you really want to finish the Test?", "selectCertificate": "Select Certificate", "selectCertificateMsg": "Please select certificate", "selectExamDate": "Select Exam Date", "accessPremium": "Pass The Exam Easily", "unlockFeature": "Premium Version", "questionReview": "Review Question", "unlock": "Unlock", "allTheExams": "all the exams", "answersWith": "Answers with", "detailedExplain": "detailed explanations", "fully": "Fully", "updateYear": "updated for %1", "term": "Terms", "redeemCode": "Redeem Code", "startTrial": "Start 3-Day Free Trial", "thankForPurchase": "You have unlocked premium feature", "cancelAnyTime": "Cancel your subscription at any time", "restorePurchase": "Restore purchase", "alreadyPaid": "Already Paid?", "months": "Months", "month": "Month", "errorDialog": "An error occurred, please try again later", "enterRedeemCode": "Please enter your redeem code", "yourCode": "Your code", "unlockAllExams": "Unlock all the exams", "certificateLbl": "Certificate", "save": "Save", "emptyField": "Field can't be empty", "send": "Send", "reportSuccess": "<PERSON><PERSON><PERSON> sent successfully", "seconds": "Seconds", "hours": "Hours", "hour": "Hour", "closed": "Closed", "numWrong": "Number of incorrect answers", "hintSearchInput": "Search", "versionLbl": "Version: ", "resendMail": "Resend", "resendMailSuccess": "Sent, please check your inbox", "delete": "Delete questions", "sendMailFail": "Sending mail failed, please try again later", "needActive": "Please verify your account via email!", "userDisabledLbl": "Your account has been disabled", "tooManyRequest": "Too many request. Try again later", "discussion": "Discussion", "deletedQuiz": "Deleted Test", "trialDescription": "- The free trial is available only once.\n- Subscription begins automatically after the free trial ends.\n- You can cancel your subscription in the App Store or Play Store.\n- If the subscription is canceled 24 hours before the end of the current use period, the subscription will end automatically after the use period ends.", "changeCertificate": "Change Certificate", "changeCertificateSuccess": "Change Certificate Success"}