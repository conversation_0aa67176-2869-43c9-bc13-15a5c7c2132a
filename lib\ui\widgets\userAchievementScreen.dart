import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';

class UserAchievementContainer extends StatelessWidget {
  final String title;
  final String value;
  const UserAchievementContainer(
      {Key? key, required this.title, required this.value})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        value == 'No Data'
            ? Center(
                child: Container(
                  height: 20,
                  width: 20,
                  child: CircularProgressContainer(
                    useWhiteLoader: false,
                  ),
                ),
              )
            : Text(
                value,
                style: TextStyle(
                  fontSize: 14.0,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
        Flexible(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.18,
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12.0,
                color: Theme.of(context).primaryColor.withOpacity(0.6),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
