import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/exam/examRepository.dart';
import 'package:flutterquiz/features/exam/models/exam.dart';
import 'package:flutterquiz/features/exam/models/examResult.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/ui/screens/exam/qlist_model.dart';
import 'package:flutterquiz/utils/answerEncryption.dart';

abstract class ExamState {}

class ExamInitial extends ExamState {}

class ExamFetchInProgress extends ExamState {}

class ExamFetchFailure extends ExamState {
  final String errorMessage;

  ExamFetchFailure(this.errorMessage);
}

class ExamFetchSuccess extends ExamState {
  final List<Question> questions;
  final Qlist exam;

  ExamFetchSuccess({required this.exam, required this.questions});
}

class ExamCubit extends Cubit<ExamState> {
  final ExamRepository _examRepository;

  ExamCubit(this._examRepository) : super(ExamInitial());

  void updateState(ExamState newState) {
    emit(newState);
  }

  void startExam({required Qlist exam, required String userId}) async {
    emit(ExamFetchInProgress());
    //
    try {
      //fetch question

      List<Question> questions =
          await _examRepository.getExamMouduleQuestions(examId: exam.quid);

      //

      //check if user can give exam or not
      //if user is in exam then it will throw 103 error means fill all data
      // await _examRepository.updateExamStatusToInExam(
      //     examModuleId: exam.quid, userId: userId);
      // await _examRepository.examLocalDataSource.addExamModuleId(exam.quid);
      emit(
          ExamFetchSuccess(exam: exam, questions: arrangeQuestions(questions)));
    } catch (e) {
      emit(ExamFetchFailure(e.toString()));
    }
  }

  List<Question> arrangeQuestions(List<Question> questions) {
    List<Question> arrangedQuestions = [];

    List<String> marks =
        questions.map((question) => question.marks!).toSet().toList();
    //sort marks
    marks.sort((first, second) => first.compareTo(second));

    //arrange questions from low to high mrak
    marks.forEach((questionMark) {
      arrangedQuestions.addAll(
          questions.where((element) => element.marks == questionMark).toList());
    });

    return arrangedQuestions;
  }

  int getQuetionIndexById(String questionId) {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess)
          .questions
          .indexWhere((element) => element.id == questionId);
    }
    return 0;
  }

  //submitted AnswerId will contain -1, 0 or optionId (a,b,c,d,e)
  void updateQuestionWithAnswer(String questionId, String submittedAnswerId) {
    if (state is ExamFetchSuccess) {
      //fethcing questions that need to update
      List<Question> updatedQuestions = (state as ExamFetchSuccess).questions;
      //fetching index of question that need to update with submittedAnswer
      int questionIndex =
          updatedQuestions.indexWhere((element) => element.id == questionId);
      //update question at given questionIndex with submittedAnswerId
      updatedQuestions[questionIndex] = updatedQuestions[questionIndex]
          .updateQuestionWithAnswer(submittedAnswerId: submittedAnswerId);

      emit(ExamFetchSuccess(
          exam: (state as ExamFetchSuccess).exam, questions: updatedQuestions));
    }
  }

  void updateQuestionBookmark(String questionId, bool bookmark) {
    if (state is ExamFetchSuccess) {
      //fethcing questions that need to update
      List<Question> updatedQuestions = (state as ExamFetchSuccess).questions;
      //fetching index of question that need to update with submittedAnswer
      int questionIndex =
          updatedQuestions.indexWhere((element) => element.id == questionId);
      //update question at given questionIndex with submittedAnswerId
      updatedQuestions[questionIndex] = updatedQuestions[questionIndex]
          .updateQuestionBookmark(bookmark: bookmark);

      emit(ExamFetchSuccess(
          exam: (state as ExamFetchSuccess).exam, questions: updatedQuestions));
    }
  }

  List<Question> getQuestions() {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess).questions;
    }
    return [];
  }

  Qlist getExam() {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess).exam;
    }
    return Qlist.fromJson("");
  }

  // bool canUserSubmitAnswerAgainInExam() {
  //   return getExam().answerAgain == "1";
  // }

  Future submitResult(
      {required String userId,
      required String totalDuration,
      required bool rulesViolated,
      required List<String> capturedQuestionIds,
      required String email,
      required int startTime}) async {
    if (state is ExamFetchSuccess) {
      List prefix = ["a", "b", "c", "d", "e", "f", "g"];
      List<Statistics> markStatistics = [];
      List answerIds = [];
      List questionIds = [];
      List scoreIndividual = [];
      List bookmarkIndex = [];
      int scoreObtained = 0;
      Map data = {};
      getUniqueQuestionMark().forEach((mark) {
        List<Question> questions = getQuestionsByMark(mark);
        int correctAnswers = 0;
        int questionIndex = 0;
        for (Question val in questions) {
          double score = 0;
          List answerIdList = [];
          answerIdList = val.submittedAnswerId.split(',');
          answerIdList.removeWhere((element) => element == "");
          for (var id in answerIdList) {
            if (val.correctAnswer?.contains(id) ?? false) {
              score += 1 / val.correctAnswer!.length;
            }
          }

          if (score > 0.9) correctAnswers++;
          int index = 0;
          List answer = [];
          for (String prefixVal in prefix) {
            if (answerIdList.contains(prefixVal)) {
              answer.add(val.options?[index].oid);
            }
            index++;
          }
          answerIds.add(answer);
          if (answerIdList.isEmpty) {
            scoreIndividual.add(0);
          } else {
            scoreIndividual.add(score > 0.9 ? 1 : 2);
          }

          if (val.bookmark) bookmarkIndex.add(questionIndex);
          questionIds.add(val.id);
          questionIndex++;
        }
        scoreObtained = correctAnswers;
        final bookmark = bookmarkIndex.join(',');
        final exam = getExam();
        final percentage = (correctAnswers / questions.length) * 100;
        data['score_individual'] = scoreIndividual.join(',');
        data['r_qids'] = questionIds;
        data['score_obtained'] = scoreObtained;
        data['total_time'] = totalDuration;
        data['answers_id'] = answerIds;
        data['bookmark_index'] = bookmark;
        data['quizId'] = exam.quid;
        data['percentage'] = percentage.toString();
        data['status'] =
            percentage >= double.parse(exam.pass_percentage) ? "Pass" : "Fail";
        data['name'] = exam.quiz_name;
        data['uid'] = userId;
        data['username'] = email;
        data['start_time'] = startTime;
        data['end_time'] = DateTime.now().millisecondsSinceEpoch ~/ 1000;

        Statistics statistics = Statistics(
            mark: mark,
            correctAnswer: correctAnswers.toString(),
            incorrect: (questions.length - correctAnswers).toString());
        markStatistics.add(statistics);
      });

      //
      markStatistics.forEach((element) {
        print(element.toJson());
      });

      var response = await _examRepository.submitExamResult(
          capturedQuestionIds: capturedQuestionIds,
          rulesViolated: rulesViolated,
          obtainedMarks: "0",
          examModuleId: (state as ExamFetchSuccess).exam.quid,
          userId: userId,
          totalDuration: totalDuration,
          data: data,
          statistics: markStatistics.map((e) => e.toJson()).toList());
      return response;
      // _examRepository.examLocalDataSource
      //     .removeExamModuleId((state as ExamFetchSuccess).exam.quid);
    }
  }

  int correctAnswers(String userId) {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess)
          .questions
          .where((element) =>
              element.submittedAnswerId ==
              AnswerEncryption.decryptCorrectAnswer(
                  rawKey: userId, correctAnswer: element.correctAnswer!))
          .toList()
          .length;
    }
    return 0;
  }

  int incorrectAnswers(String userId) {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess).questions.length -
          correctAnswers(userId);
    }
    return 0;
  }

  int obtainedMarks(String userId) {
    if (state is ExamFetchSuccess) {
      final correctAnswers = (state as ExamFetchSuccess)
          .questions
          .where((element) =>
              element.submittedAnswerId ==
              AnswerEncryption.decryptCorrectAnswer(
                  rawKey: userId, correctAnswer: element.correctAnswer!))
          .toList();
      int obtainedMark = 0;

      correctAnswers.forEach((element) {
        // obtainedMark = obtainedMark + int.parse(element.marks ?? "0");
      });

      return obtainedMark;
    }
    return 0;
  }

  List<Question> getQuestionsByMark(String questionMark) {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess)
          .questions
          .where((question) => question.marks == questionMark)
          .toList();
    }
    return [];
  }

  List<String> getUniqueQuestionMark() {
    if (state is ExamFetchSuccess) {
      return (state as ExamFetchSuccess)
          .questions
          .map((question) => question.marks!)
          .toSet()
          .toList();
    }
    return [];
  }

  void completePendingExams({required String userId}) {
    _examRepository.completePendingExams(userId: userId);
  }
}
