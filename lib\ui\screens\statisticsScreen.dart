import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';

import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/models/userProfile.dart';
import 'package:flutterquiz/features/statistic/cubits/statisticsCubit.dart';
import 'package:flutterquiz/features/statistic/models/statisticModel.dart';
import 'package:flutterquiz/features/statistic/statisticRepository.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/widgets/badgesIconContainer.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';

import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/ui/widgets/roundedAppbar.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StatisticsScreen extends StatefulWidget {
  StatisticsScreen({Key? key}) : super(key: key);

  @override
  _StatisticsScreenState createState() => _StatisticsScreenState();

  static Route<StatisticsScreen> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
        builder: (_) => BlocProvider<StatisticCubit>(
            child: StatisticsScreen(),
            create: (_) => StatisticCubit(StatisticRepository())));
  }
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final statisticsDetailsContainerHeightPercentage = 0.145;
  final statisticsDetailsContainerBorderRadius = 20.0;
  final statisticsDetailsTitleFontsize = 16.0;
  final showTotalBadgesCounter = 4;
  String total_time = '';
  String average_each_question = '';
  String num_question = '';
  String bookmark_count = '';
  String num_wrong_answers = '';
  String num_quiz_pass = '';
  Future<void> getUserInfor() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var userInfo = preferences.getString('userInfo');
    if (userInfo != '') {
      var jsonApi = json.decode(userInfo!);
      num_quiz_pass = jsonApi['num_quiz_pass'].toString();
      num_wrong_answers = jsonApi['num_wrong_answers'].toString();
      bookmark_count = jsonApi['bookmark_count'].toString();
      num_question = jsonApi['num_question'].toString();
      average_each_question = jsonApi['average_each_question'].toString();
      total_time = jsonApi['total_time'].toString();
    } else {
      num_quiz_pass = "0";
      num_wrong_answers = "0";
      bookmark_count = "0";
      num_question = "0";
      average_each_question = "No Data";
      total_time = "0";
    }
  }

  String convertSecondsToHoursMinutesAndSeconds(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60; // Số phút còn lại sau khi tính giờ
    int remainingSeconds = seconds % 60; // Số giây còn lại

    String hoursStr = hours.toString();
    String minutesStr = minutes.toString();
    String secondsStr = remainingSeconds.toString();

    String timeDetail = "";

    if (seconds == 0) {
      return "0 " +
          AppLocalization.of(context)!.getTranslatedValues("seconds")!;
    } else if (hours > 0) {
      timeDetail = '$hoursStr ' +
          AppLocalization.of(context)!
              .getTranslatedValues(hours > 1 ? "hour" : "hours")!;
      if (minutes > 0) {
        timeDetail += ' $minutesStr ' +
            AppLocalization.of(context)!.getTranslatedValues("minute")!;
      }
    } else if (minutes > 0) {
      timeDetail = '$minutesStr ' +
          AppLocalization.of(context)!.getTranslatedValues("minute")!;
      ;
      timeDetail += ' $secondsStr ' +
          AppLocalization.of(context)!.getTranslatedValues("seconds")!;
    } else {
      timeDetail = '$secondsStr ' +
          AppLocalization.of(context)!.getTranslatedValues("seconds")!;
    }

    return timeDetail;
  }

  @override
  void initState() {
    super.initState();
    getUserInfor();
    Future.delayed(Duration.zero, () {
      context
          .read<StatisticCubit>()
          .getStatisticWithBattle(context.read<UserDetailsCubit>().getUserId());
    });
  }

  /* Widget _buildCollectedBadgesContainer() {
    return BlocBuilder<BadgesCubit, BadgesState>(
      bloc: context.read<BadgesCubit>(),
      builder: (context, state) {
        final child = state is BadgesFetchSuccess
            ? context.read<BadgesCubit>().getUnlockedBadges().isEmpty
                ? Container()
                : Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 5.0,
                          ),
                          Text(
                            AppLocalization.of(context)!
                                .getTranslatedValues(collectedBadgesKey)!,
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                                fontSize: statisticsDetailsTitleFontsize),
                          ),
                          Spacer(),
                          context
                                      .read<BadgesCubit>()
                                      .getUnlockedBadges()
                                      .length >
                                  showTotalBadgesCounter
                              ? GestureDetector(
                                  onTap: () {
                                    Navigator.of(context)
                                        .pushNamed(Routes.badges);
                                  },
                                  child: Text(
                                    AppLocalization.of(context)!
                                        .getTranslatedValues(viewAllKey)!,
                                    style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                )
                              : Container(),
                          SizedBox(
                            width: 5.0,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10.0,
                      ),
                      Container(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: (context
                                          .read<BadgesCubit>()
                                          .getUnlockedBadges()
                                          .length <
                                      showTotalBadgesCounter
                                  ? context
                                      .read<BadgesCubit>()
                                      .getUnlockedBadges()
                                  : context
                                      .read<BadgesCubit>()
                                      .getUnlockedBadges()
                                      .sublist(0, showTotalBadgesCounter))
                              .map(
                                (badge) => Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 5.0),
                                  child: BadgesIconContainer(
                                    addTopPadding: false,
                                    badge: badge,
                                    constraints: BoxConstraints(
                                        maxHeight: MediaQuery.of(context)
                                                .size
                                                .height *
                                            statisticsDetailsContainerHeightPercentage,
                                        maxWidth:
                                            MediaQuery.of(context).size.width *
                                                (0.2)),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                        height: MediaQuery.of(context).size.height *
                            (statisticsDetailsContainerHeightPercentage),
                        decoration: BoxDecoration(
                            boxShadow: [
                              UiUtils.buildBoxShadow(
                                  blurRadius: 3.0,
                                  color: Colors.black.withOpacity(0.2),
                                  offset: Offset(2.5, 2.5)),
                            ],
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(
                                statisticsDetailsContainerBorderRadius)),
                      ),
                    ],
                  )
            : Container();

        return AnimatedSwitcher(
            child: child, duration: Duration(milliseconds: 500));
      },
    );
  } */

  //Details in column form data and label of the data
  Widget _buildStatisticsDetailsContainer(
      {required String data, required String dataLabel}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          data,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontSize: statisticsDetailsTitleFontsize,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          dataLabel,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildQuizDetailsContainer(String title, String data1, String label1,
      String data2, String label2, String data3, String label3) {
    UserProfile userProfile = context.read<UserDetailsCubit>().getUserProfile();
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 5.0,
            ),
            Text(
              title,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: statisticsDetailsTitleFontsize),
            ),
          ],
        ),
        SizedBox(
          height: 10.0,
        ),
        Container(
          child: LayoutBuilder(builder: (context, constraints) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 10.0),
                  child: Container(
                    height: constraints.maxHeight * (0.65),
                    width: constraints.maxWidth * (0.25),
                    child: _buildStatisticsDetailsContainer(
                        data: data1, dataLabel: label1),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  decoration: BoxDecoration(
                      border: Border(
                    right: BorderSide(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                    left: BorderSide(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                  )),
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: data2, dataLabel: label2),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: Container(
                    height: constraints.maxHeight * (0.65),
                    width: constraints.maxWidth * (0.25),
                    child: _buildStatisticsDetailsContainer(
                        data: data3, dataLabel: label3),
                  ),
                ),
              ],
            );
          }),
          height: MediaQuery.of(context).size.height *
              (statisticsDetailsContainerHeightPercentage),
          decoration: BoxDecoration(
              boxShadow: [
                UiUtils.buildBoxShadow(
                    blurRadius: 3.0,
                    color: Colors.black.withOpacity(0.2),
                    offset: Offset(2.5, 2.5)),
              ],
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(
                  statisticsDetailsContainerBorderRadius)),
        ),
      ],
    );
  }

  Widget _buildTimeDetailsContainer(
      String title, String data1, String label1, String data2, String label2) {
    UserProfile userProfile = context.read<UserDetailsCubit>().getUserProfile();
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 5.0,
            ),
            Text(
              title,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: statisticsDetailsTitleFontsize),
            ),
          ],
        ),
        SizedBox(
          height: 10.0,
        ),
        Container(
          child: LayoutBuilder(builder: (context, constraints) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 10.0),
                  child: Container(
                    height: constraints.maxHeight * (0.65),
                    width: constraints.maxWidth * (0.45),
                    child: _buildStatisticsDetailsContainer(
                        data: data1, dataLabel: label1),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.fromLTRB(0, 20, 0, 20),
                  child: VerticalDivider(
                    thickness: 1,
                    color: Theme.of(context).primaryColor.withOpacity(0.5),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: Container(
                    height: constraints.maxHeight * (0.65),
                    width: constraints.maxWidth * (0.3),
                    child: _buildStatisticsDetailsContainer(
                        data: data2, dataLabel: label2),
                  ),
                ),
              ],
            );
          }),
          height: MediaQuery.of(context).size.height *
                  (statisticsDetailsContainerHeightPercentage) +
              10,
          decoration: BoxDecoration(
              boxShadow: [
                UiUtils.buildBoxShadow(
                    blurRadius: 3.0,
                    color: Colors.black.withOpacity(0.2),
                    offset: Offset(2.5, 2.5)),
              ],
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(
                  statisticsDetailsContainerBorderRadius)),
        ),
      ],
    );
  }

  Widget _buildQuestionDetailsContainer() {
    StatisticModel statisticModel =
        context.read<StatisticCubit>().getStatisticsDetails();
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 5.0,
            ),
            Text(
              AppLocalization.of(context)!
                  .getTranslatedValues(questionDetailsKey)!,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: statisticsDetailsTitleFontsize),
            ),
          ],
        ),
        SizedBox(
          height: 10.0,
        ),
        Container(
          child: LayoutBuilder(builder: (context, constraints) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: statisticModel.answeredQuestions,
                      dataLabel: AppLocalization.of(context)!
                          .getTranslatedValues(attemptedLbl)!),
                ),
                Container(
                  decoration: BoxDecoration(
                      border: Border(
                    right: BorderSide(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                    left: BorderSide(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                  )),
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: statisticModel.correctAnswers,
                      dataLabel: AppLocalization.of(context)!
                          .getTranslatedValues(correctKey)!),
                ),
                Container(
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: (int.parse(statisticModel.answeredQuestions) -
                              int.parse(statisticModel.correctAnswers))
                          .toString(),
                      dataLabel: AppLocalization.of(context)!
                          .getTranslatedValues(incorrectKey)!),
                ),
              ],
            );
          }),
          height: MediaQuery.of(context).size.height *
              (statisticsDetailsContainerHeightPercentage),
          decoration: BoxDecoration(
              boxShadow: [
                UiUtils.buildBoxShadow(
                    blurRadius: 3.0,
                    color: Colors.black.withOpacity(0.2),
                    offset: Offset(2.5, 2.5)),
              ],
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(
                  statisticsDetailsContainerBorderRadius)),
        ),
      ],
    );
  }

  Widget _buildBattleStatisticsContainer() {
    StatisticModel statisticModel =
        context.read<StatisticCubit>().getStatisticsDetails();
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 5.0,
            ),
            Text(
              AppLocalization.of(context)!
                  .getTranslatedValues(battleStatisticsKey)!,
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: statisticsDetailsTitleFontsize),
            ),
          ],
        ),
        SizedBox(
          height: 10.0,
        ),
        Container(
          child: LayoutBuilder(builder: (context, constraints) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: statisticModel.calculatePlayedBattles().toString(),
                      dataLabel: AppLocalization.of(context)!
                          .getTranslatedValues(playedKey)!),
                ),
                Container(
                  decoration: BoxDecoration(
                      border: Border(
                    right: BorderSide(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                    left: BorderSide(
                      color: Theme.of(context).primaryColor.withOpacity(0.5),
                    ),
                  )),
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: statisticModel.battleVictories,
                      dataLabel: AppLocalization.of(context)!
                          .getTranslatedValues(wonKey)!),
                ),
                Container(
                  height: constraints.maxHeight * (0.65),
                  width: constraints.maxWidth * (0.3),
                  child: _buildStatisticsDetailsContainer(
                      data: statisticModel.battleLoose,
                      dataLabel: AppLocalization.of(context)!
                          .getTranslatedValues(lostKey)!),
                ),
              ],
            );
          }),
          height: MediaQuery.of(context).size.height *
              (statisticsDetailsContainerHeightPercentage),
          decoration: BoxDecoration(
              boxShadow: [
                UiUtils.buildBoxShadow(
                    blurRadius: 3.0,
                    color: Colors.black.withOpacity(0.2),
                    offset: Offset(2.5, 2.5)),
              ],
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(
                  statisticsDetailsContainerBorderRadius)),
        ),
      ],
    );
  }

  Widget _buildStatisticsContainer(
      {required bool showQuestionAndBattleStatistics}) {
    UserProfile userProfile = context.read<UserDetailsCubit>().getUserProfile();

    return SingleChildScrollView(
      padding: EdgeInsets.only(
          right: MediaQuery.of(context).size.width * (0.05),
          left: MediaQuery.of(context).size.width * (0.05),
          top: MediaQuery.of(context).size.height *
              (UiUtils.appBarHeightPercentage)),
      child: Column(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * (0.025),
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                  MediaQuery.of(context).size.width * (0.18)),
              border: Border.all(
                color: Theme.of(context).primaryColor,
              ),
            ),
            padding: const EdgeInsets.all(8.0),
            child: Container(
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image:
                          CachedNetworkImageProvider(userProfile.profileUrl!)),
                  shape: BoxShape.circle),
            ),
            height: MediaQuery.of(context).size.width * (0.36),
            width: MediaQuery.of(context).size.width * (0.36),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * (0.02),
          ),
          Container(
            alignment: Alignment.center,
            child: Text(
              "${AppLocalization.of(context)!.getTranslatedValues(helloKey)!}, ${userProfile.name}",
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(
            height: 5.0,
          ),
          Container(
            alignment: Alignment.center,
            child: Text(
              context.read<AuthCubit>().getAuthProvider() == AuthProvider.mobile
                  ? userProfile.mobileNumber!
                  : userProfile.email!,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * (0.125),
            ),
            child: Divider(
              thickness: 1.5,
              height: 30.0,
              color: Theme.of(context).primaryColor,
            ),
          ),
          //_buildCollectedBadgesContainer(),
          SizedBox(
            height: 20.0,
          ),
          _buildQuizDetailsContainer(
              AppLocalization.of(context)!.getTranslatedValues(quizDetailsKey)!,
              Common.totalQuiz,
              AppLocalization.of(context)!.getTranslatedValues("totalQuiz")!,
              num_quiz_pass,
              AppLocalization.of(context)!.getTranslatedValues("PassTime")!,
              Common.average + "%",
              AppLocalization.of(context)!.getTranslatedValues("average")!),
          SizedBox(
            height: 20.0,
          ),
          GestureDetector(
            onTap: () =>
                Navigator.of(context).pushNamed(Routes.reviewQuestions),
            child: _buildQuizDetailsContainer(
                AppLocalization.of(context)!
                    .getTranslatedValues("questionDetails")!,
                num_question,
                AppLocalization.of(context)!
                    .getTranslatedValues("questionDone")!,
                num_wrong_answers,
                AppLocalization.of(context)!
                    .getTranslatedValues("wrongAnswers")!,
                bookmark_count,
                AppLocalization.of(context)!.getTranslatedValues("mark")!),
          ),
          SizedBox(
            height: 20.0,
          ),
          _buildTimeDetailsContainer(
            AppLocalization.of(context)!.getTranslatedValues("timeDetails")!,
            total_time != "0" && total_time != "null"
                ? convertSecondsToHoursMinutesAndSeconds(int.parse(total_time))
                : "0" +
                    " ${AppLocalization.of(context)!.getTranslatedValues("minute")!}",
            AppLocalization.of(context)!.getTranslatedValues("totalTime")!,
            average_each_question != "No Data"
                ? (int.parse(average_each_question)).toString() +
                    " ${AppLocalization.of(context)!.getTranslatedValues("seconds")!}"
                : AppLocalization.of(context)!.getTranslatedValues("noData")!,
            AppLocalization.of(context)!.getTranslatedValues("averageEach")!,
          ),
          SizedBox(
            height: 20.0,
          ),
          showQuestionAndBattleStatistics
              ? Column(
                  children: [
                    _buildQuestionDetailsContainer(),
                    SizedBox(
                      height: 20.0,
                    ),
                    _buildBattleStatisticsContainer(),
                    SizedBox(
                      height: 30,
                    ),
                  ],
                )
              : Container()
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageBackgroundGradientContainer(),
          BlocConsumer<StatisticCubit, StatisticState>(
              listener: (context, state) {
            if (state is StatisticFetchFailure) {
              /* if (state.errorMessageCode == unauthorizedAccessCode) {
                UiUtils.showAlreadyLoggedInDialog(context: context);
              } */
            }
          }, builder: (context, state) {
            if (state is StatisticFetchSuccess) {
              return Align(
                alignment: Alignment.topCenter,
                child: _buildStatisticsContainer(
                  showQuestionAndBattleStatistics: true,
                ),
              );
            }
            if (state is StatisticFetchFailure) {
              return Align(
                alignment: Alignment.topCenter,
                child: _buildStatisticsContainer(
                  showQuestionAndBattleStatistics: false,
                ),
              );
            }

            return Center(
              child: CircularProgressContainer(
                useWhiteLoader: false,
              ),
            );
          }),
          Align(
            alignment: Alignment.topCenter,
            child: RoundedAppbar(
                title: AppLocalization.of(context)!
                    .getTranslatedValues(statisticsLabelKey)!),
          ),
        ],
      ),
    );
  }
}
