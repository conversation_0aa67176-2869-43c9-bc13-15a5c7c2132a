import 'package:flutter_test/flutter_test.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

void main() {
  test("Test coins redeem equation of payment request", () {
    //

    expect(
        UiUtils.calculateDeductedCoinsForRedeemableAmount(
            userEnteredAmount: 50, amount: 5, coins: 10),
        100);
  });

  test("Test amount user will get based on coins of payment request", () {
    //

    expect(UiUtils.calculateAmountPerCoins(userCoins: 50, amount: 5, coins: 10),
        25);
  });
}
