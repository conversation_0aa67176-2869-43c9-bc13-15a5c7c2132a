<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000424">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: Switch to ios build lane" time="0.000288">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: Switch to ios sh_on_root lane" time="0.000174">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: cd /Users/<USER>/Work/Project/scrumpass-exam-tool &amp;&amp; flutter build ipa" time="836.278808">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: Switch to ios archive lane" time="0.000581">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="5: update_code_signing_settings" time="0.147445">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="6: update_project_team" time="0.142723">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="7: build_app" time="729.65077">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="8: firebase_app_distribution" time="16.907746">
        
      </testcase>
    
  </testsuite>
</testsuites>
