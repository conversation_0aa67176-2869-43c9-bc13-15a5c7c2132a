import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/widgets/customBackButton.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class RoundedAppbar extends StatelessWidget {
  final String title;
  final Widget? trailingWidget;
  final bool? removeSnackBars;
  final Color? appBarColor;
  final Color? appTextAndIconColor;
  RoundedAppbar(
      {Key? key,
      required this.title,
      this.trailingWidget,
      this.removeSnackBars,
      this.appBarColor,
      this.appTextAndIconColor})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 15.0),
      child: Stack(
        children: [
          Positioned.fill(
            top: 15,
            left: 25,
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: CustomBackButton(
                removeSnackBars: removeSnackBars,
                iconColor:
                    appTextAndIconColor ?? Theme.of(context).primaryColor,
              ),
            ),
          ),
          Positioned.fill(
            top: 15,
            child: Align(
              alignment: Alignment.center,
              child: Text(
                "$title",
                //textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold,
                    color:
                        appTextAndIconColor ?? Theme.of(context).primaryColor),
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.bottomEnd,
            child: trailingWidget ?? Container(),
          ),
        ],
      ),
      height:
          MediaQuery.of(context).size.height * UiUtils.appBarHeightPercentage,
      decoration: BoxDecoration(
          boxShadow: [UiUtils.buildAppbarShadow()],
          color: appBarColor ?? Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.0),
              bottomRight: Radius.circular(20.0))),
    );
  }
}
