import 'dart:convert';

class UserModel {
  final double recent;
  final double average;
  final double num_quiz;
  UserModel({
    required this.recent,
    required this.average,
    required this.num_quiz,
  });

  factory UserModel.fromMap(Map<String, dynamic> json) => UserModel(
        recent: json["recent"],
        average: json["average"],
        num_quiz: json["num_quiz"],
      );
  Map<String, dynamic> toMap() {
    return {
      'recent': recent,
      'average': average,
      'num_quiz': num_quiz,
    };
  }

  String toJson() => json.encode(toMap());

  factory UserModel.fromJson(String source) =>
      UserModel.fromMap(json.decode(source));
}
