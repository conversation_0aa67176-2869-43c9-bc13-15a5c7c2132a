import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/settings/settingsLocalDataSource.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppLocalizationState {
  final Locale language;
  AppLocalizationState(this.language);
}

final String defaultLocale = Platform.localeName;

class AppLocalizationCubit extends Cubit<AppLocalizationState> {
  final SettingsLocalDataSource settingsLocalDataSource;
  AppLocalizationCubit(this.settingsLocalDataSource)
      : super(AppLocalizationState(
            UiUtils.getLocaleFromLanguageCode(defaultLanguageCode))) {
    changeLanguage(settingsLocalDataSource.languageCode()!);
    languageCheck();
  }
  Future<void> languageCheck() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var lang = preferences.getString('language');
    if (lang == '' || lang == null) {
      if (defaultLocale.contains("vi")) {
        changeLanguage("vi");
      } else {
        changeLanguage("en");
      }
    }
  }

  void changeLanguage(String languageCode) {
    settingsLocalDataSource.setLanguageCode(languageCode);
    emit(AppLocalizationState(UiUtils.getLocaleFromLanguageCode(languageCode)));
  }
}
