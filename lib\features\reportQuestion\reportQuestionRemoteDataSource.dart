import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionException.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/utils/apiBodyParameterLabels.dart';
import 'package:flutterquiz/utils/apiUtils.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';

class ReportQuestionRemoteDataSource {
  /*
   access_key:8525
        question_id:115
        user_id:1
        message: Any reporting message
  
  
   */
  Future<dynamic> reportQuestion(
      {required String questionId,
      required String message,
      required String userId}) async {
    try {
      Map<String, String> body = {
        accessValueKey: accessValue,
        questionIdKey: questionId,
        messageKey: message,
        userIdKey: userId
      };

      final response = await dio.post(reportQuestionUrl,
          data: FormData.fromMap(body),
          options: Options(headers: await ApiUtils.getHeaders()));

      final responseJson = response.data;

      if (responseJson['error']) {
        throw ReportQuestionException(
            errorMessageCode: responseJson['message']); //error
      }
      return responseJson['data'];
    } on SocketException catch (_) {
      throw ReportQuestionException(errorMessageCode: noInternetCode);
    } on ReportQuestionException catch (e) {
      throw ReportQuestionException(errorMessageCode: e.toString());
    } catch (e) {
      throw ReportQuestionException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  Future<dynamic> reportQuestionList({required String userId}) async {
    try {
      Map<String, String> body = {accessValueKey: accessValue, "uid": userId};

      final response = await dio.post(reportQuestionListUrl,
          data: FormData.fromMap(body),
          options: Options(headers: await ApiUtils.getHeaders()));

      final responseJson = response.data;

      if (responseJson['error']) {
        throw ReportQuestionException(
            errorMessageCode: responseJson['message']); //error
      }
      return responseJson['data'];
    } on SocketException catch (_) {
      throw ReportQuestionException(errorMessageCode: noInternetCode);
    } on ReportQuestionException catch (e) {
      throw ReportQuestionException(errorMessageCode: e.toString());
    } catch (e) {
      throw ReportQuestionException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  Future<dynamic> reportQuestionDetail({required String id}) async {
    try {
      Map<String, String> body = {accessValueKey: accessValue, "id": id};
      final response = await dio.post(reportQuestionDetailUrl,
          data: FormData.fromMap(body),
          options: Options(headers: await ApiUtils.getHeaders()));

      final responseJson = response.data;

      if (responseJson['error']) {
        throw ReportQuestionException(
            errorMessageCode: responseJson['message']); //error
      }
      return responseJson['data'];
    } on SocketException catch (_) {
      throw ReportQuestionException(errorMessageCode: noInternetCode);
    } on ReportQuestionException catch (e) {
      throw ReportQuestionException(errorMessageCode: e.toString());
    } catch (e) {
      throw ReportQuestionException(errorMessageCode: defaultErrorMessageCode);
    }
  }
}
