import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';

import '../../styles/payment/app_colors.dart';
import '../../styles/payment/app_styles.dart';

class ErrorDialog extends StatelessWidget {
  const ErrorDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Future(() {
      showDialog<String>(
        context: context,
        builder: (BuildContext context) => Dialog(
          // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
          insetPadding: EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
              child: Column(
                children: [
                  Container(
                    width: 196,
                    height: 139,
                    decoration: BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("assets/images/error_dialog.png"),
                      fit: BoxFit.fill,
                    )),
                  ),
                  Text(
                    AppLocalization.of(context)!
                        .getTranslatedValues("errorDialog")!,
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              Navigator.of(context).pop();
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                "OK",
                                style: AppStyles.primaryButton,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor: Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          )
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      );
    });
    return Scaffold(body: Center());
  }
}
