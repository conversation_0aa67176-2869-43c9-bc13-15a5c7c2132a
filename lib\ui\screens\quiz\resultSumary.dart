import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/exam/cubits/examCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/questionsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/resultCubit.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionCubit.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';
import 'package:flutterquiz/features/systemConfig/model/result_status.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/exam.dart';
import 'package:flutterquiz/ui/screens/exam/examScreen.dart';
import 'package:flutterquiz/ui/screens/exam/qlist_model.dart';
import 'package:flutterquiz/ui/screens/inAppPurchase.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/radialResultContainer.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/ui/styles/theme/examThemeCubit.dart';
import 'package:flutterquiz/ui/widgets/circularImageContainer.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../features/exam/cubits/completedExamsCubit.dart';
import '../../../features/exam/cubits/examsCubit.dart';

class ResultSummaryScreen extends StatefulWidget {
  final String rid;
  final String quid;
  final String quiz_name;
  final String pass_percentage;
  final String duration;
  final bool retry;
  final bool backButtonStatus;
  final ExamTheme examTheme;
  String quiz_duration;

  ResultSummaryScreen(
      {Key? key,
      required this.rid,
      required this.quiz_name,
      required this.pass_percentage,
      required this.duration,
      required this.quid,
      required this.backButtonStatus,
      this.examTheme = ExamTheme.Elite,
      this.quiz_duration = "",
      this.retry = true})
      : super(key: key);
  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments as Map?;
    //arguments will map and keys of the map are following
    //questions and guessTheWordQuestions
    return CupertinoPageRoute(
        builder: (_) => MultiBlocProvider(
                providers: [
                  BlocProvider<UpdateBookmarkCubit>(
                    create: (context) =>
                        UpdateBookmarkCubit(BookmarkRepository()),
                  ),
                  BlocProvider<ReportQuestionCubit>(
                      create: (_) =>
                          ReportQuestionCubit(ReportQuestionRepository())),
                  BlocProvider<ResultCubit>(
                      create: (_) => ResultCubit(QuizRepository())),
                ],
                child: ResultSummaryScreen(
                  rid: arguments!['rid'],
                  quid: arguments['quid'],
                  backButtonStatus: arguments['backButtonStatus'],
                  quiz_name: arguments['quiz_name'] ?? "",
                  pass_percentage: arguments['pass_percentage'],
                  duration: arguments['duration'] ?? "",
                  retry: arguments['retry'] ?? true,
                  examTheme: arguments['examTheme'] ?? ExamTheme.Elite,
                  quiz_duration: arguments['quiz_duration'] == null
                      ? ""
                      : arguments['quiz_duration'],
                )));
  }

  @override
  _ResultScreenState createState() => _ResultScreenState();
}

class _ResultScreenState extends State<ResultSummaryScreen> {
  void _clearResumeQuiz() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String resumeQuiz = prefs.getString('resumeQuiz') ?? '';
    if (resumeQuiz != '') {
      prefs.remove('answered_' + resumeQuiz);
      prefs.remove('bookmarked_' + resumeQuiz);
      prefs.remove('answeredText_' + resumeQuiz);
      prefs.remove('timeDoQuiz_' + resumeQuiz);
      prefs.remove('timeStartQuiz_' + resumeQuiz);
      prefs.remove('answeredId_' + resumeQuiz);
      prefs.remove('resumeQuiz');
      prefs.remove('questionResume');
    }
  }

  PageController? _pageController;
  bool _isWinner = true;
  int rightAns = 0;
  int wrongAns = 0;
  int noq = 0;
  List<ResultStatus> resultStatus = [];
  @override
  void initState() {
    _pageController = PageController();
    getResultData();
    super.initState();
  }

  void getResultData() {
    context.read<ResultCubit>().getResultDetail(widget.rid);
  }

  int getResultLength() {
    return resultStatus.length;
  }

  final ScreenshotController screenshotController = ScreenshotController();
  Widget _buildResultContainer(BuildContext context) {
    return Screenshot(
      controller: screenshotController,
      child: Container(
        height: MediaQuery.of(context).size.height * (0.6),
        width: MediaQuery.of(context).size.width * (0.85),
        decoration: BoxDecoration(
          boxShadow: [UiUtils.buildBoxShadow()],
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: _buildResultDetails(context),
      ),
    );
  }

  Widget _buildResultDetails(BuildContext context) {
    final userProfileUrl =
        context.read<UserDetailsCubit>().getUserProfile().profileUrl ?? "";

    return _buildIndividualResultContainer(userProfileUrl);
  }

  Widget _buildResultButtons(BuildContext context) {
    double betweenButoonSpace = 15.0;
    ExamTheme examTheme = context.read<ExamThemeCubit>().state.appTheme;
    return Column(
      children: [
        SizedBox(
          height: betweenButoonSpace,
        ),
        _buildReviewAnswersButton(),
        if (widget.retry) ...{
          _buildRetryButton(),
          SizedBox(
            height: betweenButoonSpace,
          )
        },
        _buildButton(
            AppLocalization.of(context)!.getTranslatedValues("homeBtn")!, () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              Routes.home, (Route<dynamic> route) => false);
        }, context),
        // if (!Common.premium &&
        //     Common.registeredFromApp &&
        //     !Common.inPremiumGroup) ...{
        //   SizedBox(
        //     height: betweenButoonSpace,
        //   ),
        //   _buildButton(
        //       AppLocalization.of(context)!
        //           .getTranslatedValues("unlockAllExams")!
        //           .toUpperCase(), () {
        //     Navigator.push(
        //       context,
        //       MaterialPageRoute(builder: (context) => PaymentPage()),
        //     );
        //   }, context)
        // },
      ],
    );
  }

  Widget _buildRetryButton() {
    return widget.quid == "0"
        ? SizedBox()
        : _buildButton(
            AppLocalization.of(context)!
                .getTranslatedValues("retryLbl")!
                .toUpperCase(), () async {
            _clearResumeQuiz();
            // if (widget.exam != null) {
            //   context.read<ExamCubit>().startExam(
            //       exam: widget.exam!,
            //       userId: context.read<UserDetailsCubit>().getUserId());
            // } else {
            widget.examTheme == ExamTheme.Scrum
                ? Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => ExamSimulator(
                            idQuiz: widget.quid,
                            quizName: widget.quiz_name,
                            passPercent: int.parse(widget.pass_percentage),
                            duration: widget.quiz_duration != ""
                                ? int.parse(widget.quiz_duration)
                                : int.parse(widget.duration))))
                : _buildNavigatorPushToRetryPractice();
          }, context);
  }

  void _buildNavigatorPushToRetryPractice() {
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (context) => ExamScreen(
                quizDuration: widget.quiz_duration,
                exam: Qlist(
                    quid: widget.quid,
                    quiz_name: widget.quiz_name,
                    noq: "noq",
                    duration: widget.duration,
                    pass_percentage: widget.pass_percentage,
                    start_date: "",
                    end_date: "end_date",
                    description: "description"),
                userId: context.read<UserDetailsCubit>().getUserId())));
  }

  Widget _buildPlayAgainButton() {
    return _buildButton(
        AppLocalization.of(context)!.getTranslatedValues("nextLevelBtn")!,
        () {},
        context);
  }

  Widget _buildButton(
      String buttonTitle, Function onTap, BuildContext context) {
    return CustomRoundedButton(
      widthPercentage: 0.85,
      backgroundColor: Theme.of(context).primaryColor,
      buttonTitle: buttonTitle,
      radius: 7.5,
      elevation: 5.0,
      showBorder: false,
      fontWeight: FontWeight.bold,
      height: 50.0,
      titleColor: Theme.of(context).colorScheme.surface,
      onTap: onTap,
      textSize: 17.0,
    );
  }

  Widget _buildReviewAnswersButton() {
    return Column(
      children: [
        _buildButton(
            AppLocalization.of(context)!
                .getTranslatedValues("reviewAnsBtn")!
                .toUpperCase(), () {
          Navigator.of(context).pushNamed(Routes.reviewAnswers,
              arguments: {"rid": widget.rid, "quizId": widget.quid});
          //
        }, context),
        SizedBox(
          height: 15.0,
        )
      ],
    );
  }

  Widget _buildGreetingMessage(String title, String message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 15.0,
        ),
        Stack(children: [
          widget.backButtonStatus == true
              ? Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 15.0,
                    ),
                    child: InkWell(
                        onTap: () {
                          //onPageBackCalls();
                          Navigator.pop(context);
                        },
                        child: Container(
                            padding: EdgeInsets.all(5.0),
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.transparent)),
                            child: Icon(
                              Icons.arrow_back_ios,
                              color: Theme.of(context).colorScheme.surface,
                            ))),
                  ))
              : Container(),
          Container(
              alignment: Alignment.center,
              child: Text(
                "$message",
                style: TextStyle(
                    fontSize: 19.0,
                    color: Theme.of(context).colorScheme.surface),
              )),
        ]),
        SizedBox(
          height: 5.0,
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 5.0),
          alignment: Alignment.center,
          child: Text("$title",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 25.0 * MediaQuery.of(context).textScaleFactor * 1.25,
                color: Theme.of(context).colorScheme.surface,
              )),
        )
      ],
    );
  }

  Widget _buildResultDataWithIconContainer(
      String title, String icon, EdgeInsetsGeometry margin) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(10.0)),
      width: MediaQuery.of(context).size.width * (0.2125),
      height: 30.0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(UiUtils.getImagePath(icon),
              color: Theme.of(context).colorScheme.secondary),
          SizedBox(
            width: 5,
          ),
          Text(
            title,
            style: TextStyle(color: Theme.of(context).colorScheme.secondary),
          ),
        ],
      ),
      alignment: Alignment.center,
    );
  }

  bool showCoinsAndScore() {
    return false;
  }

  Widget _buildIndividualResultContainer(String userProfileUrl) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Align(
          alignment: Alignment.center,
          child: SvgPicture.asset(
            /* widget.quizType == QuizTypes.exam
                ? UiUtils.getImagePath("celebration.svg")
                : */
            _isWinner
                ? UiUtils.getImagePath("celebration.svg")
                : UiUtils.getImagePath("celebration_loss.svg"),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: LayoutBuilder(
            builder: (context, constraints) {
              double verticalSpacePercentage = 0.0;
              double profileRadiusPercentage = 0.0;

              double radialSizePercentage = 0.0;
              if (constraints.maxHeight <
                  UiUtils.profileHeightBreakPointResultScreen) {
                verticalSpacePercentage = 0.015;
                profileRadiusPercentage = 0.35; //test in
                radialSizePercentage = 0.6;
              } else {
                verticalSpacePercentage = 0.035;
                profileRadiusPercentage = 0.375;

                radialSizePercentage = 0.525;
              }

              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _isWinner
                      ? _buildGreetingMessage(
                          AppLocalization.of(context)!
                              .getTranslatedValues("victoryLbl")!,
                          AppLocalization.of(context)!
                              .getTranslatedValues("congratulationsLbl")!)
                      : _buildGreetingMessage(
                          AppLocalization.of(context)!
                              .getTranslatedValues("defeatLbl")!,
                          AppLocalization.of(context)!
                              .getTranslatedValues("betterNextLbl")!),
                  SizedBox(
                    height: constraints.maxHeight * verticalSpacePercentage,
                  ),
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Center(
                        child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            height: constraints.maxHeight *
                                profileRadiusPercentage),
                      ),
                      Center(
                        child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                            ),
                            height: constraints.maxHeight *
                                (profileRadiusPercentage - 0.025)),
                      ),
                      Center(
                        child: CircularImageContainer(
                            imagePath: userProfileUrl,
                            height: constraints.maxHeight *
                                (profileRadiusPercentage - 0.05),
                            width: constraints.maxWidth *
                                (profileRadiusPercentage - 0.05 + 0.15)),
                      ),
                    ],
                  ),
                  /* widget.quizType! == QuizTypes.exam
                      ? Transform.translate(
                          offset: Offset(0, -30.0),
                          child: Text(
                            "${widget.obtainedMarks}/${widget.exam!.totalMarks} ${AppLocalization.of(context)!.getTranslatedValues(markKey)!}",
                            style: TextStyle(
                              fontSize: 22.0 *
                                  MediaQuery.of(context).textScaleFactor *
                                  (1.1),
                              fontWeight: FontWeight.w400,
                              color: Theme.of(context)
                                  .backgroundColor, //Theme.of(context).colorScheme.surface,
                            ),
                          ),
                        )
                      : */
                  Padding(
                    padding: widget.backButtonStatus
                        ? EdgeInsets.only(top: 5.0)
                        : EdgeInsets.only(top: 10.0),
                    child: Text(
                      _isWinner
                          ? AppLocalization.of(context)!
                              .getTranslatedValues("winnerLbl")!
                          : AppLocalization.of(context)!
                              .getTranslatedValues("youLossLbl")!,
                      style: TextStyle(
                        fontSize: 25.0 *
                            MediaQuery.of(context).textScaleFactor *
                            (1.1),
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context)
                            .colorScheme.surface, //Theme.of(context).colorScheme.surface,
                      ),
                    ),
                  )
                ],
              );
            },
          ),
        ),

        //incorrect answer
        Align(
          alignment: AlignmentDirectional.bottomStart,
          child: _buildResultDataWithIconContainer(
              /*  widget.quizType == QuizTypes.exam
                  ? "${widget.incorrectExamAnswers}/${totalQuestions()}"
                  : "${totalQuestions() - correctAnswer()}/${totalQuestions()}" */
              "${wrongAns}/${noq}",
              "wrong.svg",
              EdgeInsetsDirectional.only(
                  start: 15.0, bottom: showCoinsAndScore() ? 20.0 : 30.0)),
        ),
        //correct answer
        /* showCoinsAndScore()
            ? Align(
                alignment: AlignmentDirectional.bottomStart,
                child: _buildResultDataWithIconContainer(
                    /* "${correctAnswer()}/${totalQuestions()}" */ "5/20",
                    "correct.svg",
                    EdgeInsetsDirectional.only(start: 15.0, bottom: 60.0)),
              )
            : */
        Align(
          alignment: Alignment.bottomRight,
          child: _buildResultDataWithIconContainer(
              /* "${correctAnswer()}/${totalQuestions()}" */ "${rightAns}/${noq}",
              "correct.svg",
              EdgeInsetsDirectional.only(end: 15.0, bottom: 30.0)),
        ),

        //points
        /* showCoinsAndScore()
            ? Align(
                alignment: AlignmentDirectional.bottomEnd,
                child: _buildResultDataWithIconContainer(
                    /* "${widget.myPoints}" */ "50",
                    "score.svg",
                    EdgeInsetsDirectional.only(end: 15.0, bottom: 60.0)),
              )
            : Container(), */
        //earned coins
        showCoinsAndScore()
            ? Align(
                alignment: AlignmentDirectional.bottomEnd,
                child: _buildResultDataWithIconContainer(
                    /* "$_earnedCoins" */ "1",
                    "earnedCoin.svg",
                    EdgeInsetsDirectional.only(end: 15.0, bottom: 20.0)),
              )
            : Container(),

        //build radils percentage container
        /* widget.quizType! == QuizTypes.exam
            ? Container()
            : */
        Align(
          alignment: Alignment.bottomCenter,
          child: LayoutBuilder(builder: (context, constraints) {
            double radialSizePercentage = 0.0;
            if (constraints.maxHeight <
                UiUtils.profileHeightBreakPointResultScreen) {
              radialSizePercentage = 0.4;
            } else {
              radialSizePercentage = 0.325;
            }
            return Transform.translate(
              offset: Offset(0.0, 15.0), //
              child: RadialPercentageResultContainer(
                circleColor: Theme.of(context).colorScheme.secondary,
                arcColor: Theme.of(context).colorScheme.surface,
                arcStrokeWidth: 10.0,
                circleStrokeWidth: 10.0,
                radiusPercentage: 0.27,
                percentage: winPercentage(),
                timeTakenToCompleteQuizInSeconds:
                    int.parse(resultStatus[0].total_time.toString()),
                size: Size(
                    constraints.maxHeight * radialSizePercentage,
                    constraints.maxHeight *
                        radialSizePercentage), //150.0 , 150.0
              ),
            );
          }),
        ),
      ],
    );
  }

  double winPercentage() {
    return double.parse(resultStatus[0].percentage_obtained!);
  }

  Future<bool> onBackPressedHandler() {
    /* Navigator.of(context)
        .pushNamedAndRemoveUntil(Routes.home, (Route<dynamic> route) => false); */
    Navigator.pop(context);
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<ResultCubit, ResultState>(builder: (context, state) {
        if (state is ResultIntial) {
          // context.read<QuestionsCubit>().getResultQuestion(widget.rid);
          print('init');
          return CircularProgressContainer(
            useWhiteLoader: false,
          );
        } else if (state is ResultFetchInProgress) {
          print('in progress');
          context.read<ResultCubit>().getResultDetail(widget.rid);
          return Center(
              child: CircularProgressContainer(
            useWhiteLoader: false,
          ));
        } else if (state is ResultResultFetchSuccess) {
          resultStatus = state.resultStatus;
          if (resultStatus[0].result_status == "Fail") {
            _isWinner = false;
          } else {
            _isWinner = true;
          }
          List<String> strarray =
              resultStatus[0].score_individual.toString().split(",");
          rightAns = 0;
          wrongAns = 0;
          for (int i = 0; i < strarray.length; i++) {
            if (strarray[i] == "1") {
              rightAns++;
            }
            if (strarray[i] == "2") {
              wrongAns++;
            }
          }
          List<String> noqArr = resultStatus[0].r_qids.toString().split(",");
          noq = noqArr.length;
          return Stack(
            children: [
              PageBackgroundGradientContainer(),
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 50.0,
                    ),
                    Center(child: _buildResultContainer(context)),
                    SizedBox(
                      height: 30.0,
                    ),
                    _buildResultButtons(context),
                    SizedBox(
                      height: 50.0,
                    ),
                  ],
                ),
              ),
            ],
          );
        } else {
          inspect(state);
          return Container();
        }
      }),
    );
  }
}
