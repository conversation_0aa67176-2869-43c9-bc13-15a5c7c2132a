import 'dart:convert';

class UserLoginModel {
  final String status;
  final String? message;
  final Map? user;
  UserLoginModel({
    this.status = "",
    this.message = "",
    this.user = const {},
  });

  factory UserLoginModel.fromMap(Map<String, dynamic> json) => UserLoginModel(
        status: json["status"] ?? "",
        message: json["message"] ?? "",
        user: json["user"] ?? {},
      );
  Map<String, dynamic> toMap() {
    return {
      'status': status,
      'message': message,
      'user': user,
    };
  }

  String toJson() => json.encode(toMap());

  factory UserLoginModel.fromJson(String source) =>
      UserLoginModel.fromMap(json.decode(source));
}
