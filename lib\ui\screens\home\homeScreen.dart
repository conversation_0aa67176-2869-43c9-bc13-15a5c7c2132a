import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/gestures.dart';
import 'package:flutterquiz/features/ads/interstitialAdCubit.dart';
import 'package:flutterquiz/features/auth/auhtException.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/battleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/multiUserBattleRoomCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementLocalDataSource.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/globals.dart';

import 'package:flutterquiz/ui/screens/battle/widgets/randomOrPlayFrdDialog.dart';
import 'package:flutterquiz/ui/screens/battle/widgets/roomDialog.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/appUnderMaintenanceDialog.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/menuBottomSheetContainer.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/userDisabled.dart';
import 'package:flutterquiz/ui/screens/inAppPurchase.dart';
import 'package:flutterquiz/ui/screens/profile/widgets/editProfileFieldBottomSheetContainer.dart';
import 'package:flutterquiz/utils/apiUtils.dart';
import 'package:flutterquiz/utils/constants.dart';

import 'package:http/http.dart' as http;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/auth/cubits/referAndEarnCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/models/userProfile.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/quizTypeContainer.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/updateAppContainer.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/ui/widgets/userAchievementScreen.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/quizTypes.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeScreen extends StatefulWidget {
  HomeScreen({
    Key? key,
  }) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
        builder: (context) => MultiBlocProvider(
              providers: [
                BlocProvider<ReferAndEarnCubit>(
                  create: (_) => ReferAndEarnCubit(AuthRepository()),
                ),
                BlocProvider<UpdateUserDetailCubit>(
                  create: (context) =>
                      UpdateUserDetailCubit(ProfileManagementRepository()),
                ),
              ],
              child: HomeScreen(),
            ));
  }
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  final double quizTypeWidthPercentage = 0.4;
  late double quizTypeTopMargin = 0.0;
  final double quizTypeHorizontalMarginPercentage = 0.08;
  final List<int> maxHeightQuizTypeIndexes = [0, 3, 4, 7, 8];

  final double quizTypeBetweenVerticalSpacing = 0.02;

  late List<QuizType> _quizTypes = quizTypes;

  late AnimationController profileAnimationController;
  late AnimationController selfChallengeAnimationController;

  late Animation<Offset> profileSlideAnimation;

  late Animation<Offset> selfChallengeSlideAnimation;

  late AnimationController firstAnimationController;
  late Animation<double> firstAnimation;
  late AnimationController secondAnimationController;
  late Animation<double> secondAnimation;

  String url = getUserInfor().url;
  String appkey = getUserInfor().appkey;
  bool? dragUP;
  int currentMenu = 1;
  int retry = 0;

  Future<void> requestKey() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Accquire token";
      /* if (token == null || token == '') { */
      var jwt = await attempPost(info);
      print(
          "getKey------------------------------------------------------------1");
      preferences.setString('token', jwt);
    }
  }

  Future<String> attempPost(String info) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= "";
    final response = await http.post(Uri.parse(url),
        body: {"key": appkey, "token": token, "info": info});

    if (response.statusCode == 200) {
      preferences.setString('token', response.body);
      return response.body;
    } else {
      //status = false;
      return "";
    }
  }

  Future<void> getListQuiz() async {
    print('getList');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      /* if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      } */
      if (Common.username != '') {
        preferences.setString('username', Common.username);
      }
      String user = preferences.getString('username') ?? '';
      print("getList----------------------------------2");
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {'user': user, 'exam': '0'};
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
            }));
        if (response.statusCode == 200) {
          preferences.setString('quizList', response.data.toString());
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListQuiz, 200);
      }
    }
  }

  Future<void> getListQuizExam() async {
    print('getListExam');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      /* if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      } */
      if (Common.username != '') {
        preferences.setString('username', Common.username);
      }
      String user = preferences.getString('username') ?? '';
      print("getList----------------------------------2");
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {'user': user, 'exam': '1'};
      try {
        final response = await dio.post(url,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
            }));
        if (response.statusCode == 200) {
          preferences.setString('quizList_exam', response.data.toString());
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListQuizExam, 200);
      }
    }
  }

  Future<void> getListResult() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user, 'exam': '0'};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_result_list_by_user"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('result', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListResult, 200);
      }
    }
  }

  Future<void> getListResultExam() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user, 'exam': '1'};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_result_list_by_user"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('result_exam', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListResult, 200);
      }
    }
  }

  Future<void> getListWrongAns() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getWrongAns----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_wrong_answered"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('wrongAnsApi', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListWrongAns, 200);
      }
    }
  }

  Future<void> getListMarkedAns() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getMarkedAns----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_marked_question"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('markedAnsApi', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListMarkedAns, 200);
      }
    }
  }

  retryFuture(future, delay) {
    if (retry < 3) {
      retry++;
      Future.delayed(Duration(milliseconds: delay)).then((value) {
        future();
      });
    } else {
      retry = 0;
    }
  }

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  String totalQuiz = '0';
  String average = '0';
  String recent = '0';
  Future<void> getUserInforApi() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');

      String info = await GetDeviceInfo() + "-User Info Request";
      Map data = {'user': user};
      try {
        final response = await dio.post(apiUrl + "get_user_info",
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
            }));
        if (response.statusCode == 200) {
          preferences.setString('userInfo', response.data.toString());
          if (response.data == "") {
            setState(() {
              Common.totalQuiz = "0";
              Common.average = "0";
              Common.recent = "0";
            });
          } else {
            final parsed = jsonDecode(response.data);
            setState(() {
              totalQuiz = parsed['num_quiz'].toString();
              average = parsed['average'].toString();
              recent = parsed['recent'].toString();
              Common.totalQuiz = totalQuiz;
              Common.average = average;
              Common.recent = recent;
            });
          }
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getUserInforApi, 200);
      }
    }
  }

  getVersion() {
    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      String version = packageInfo.version;
      Common.version = version;
    });
  }

  @override
  void initState() {
    getVersion();
    initAnimations();
    // checkPremium();
    showAppUnderMaintenanceDialog();
    checkUserStatus();
    setQuizMenu();
    _initLocalNotification();
    checkForUpdates();
    setupInteractedMessage();
    createAds();
    requestKey();
    getListQuiz();
    getListQuizExam();
    getListResult();
    getListResultExam();
    getUserInforApi();
    getListWrongAns();
    getListMarkedAns();
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    print(examUrl);
    print('registered from app: ${Common.registeredFromApp}');
    print('premium: ${Common.premium}');
  }

  Future<void> checkUserStatus() async {
    final authCubit = context.read<AuthCubit>();
    final status = await authCubit.getUserScrumPassStatus();
    if (status == false) {
      showDialog(context: context, builder: (_) => UserDisabledDialog());
    }
  }

  Future<void> checkPremium() async {
    print('check premium');
    try {
      SharedPreferences preferences = await SharedPreferences.getInstance();
      final response = await dio.post(checkPremiumUrl,
          data: FormData.fromMap({
            'email': preferences.get('username'),
            'premium': Common.premium ? "1" : "0"
          }),
          options: Options(headers: await ApiUtils.getHeaders()));
      final responseJson = jsonDecode(response.data);
      Common.registeredFromApp =
          responseJson['data']['registered_from_app'] == 1 ? true : false;
      Common.inPremiumGroup =
          responseJson['data']['in_premium_group'] == 1 ? true : false;
      print('done check premium');
    } on SocketException catch (_) {
      throw AuthException(errorMessageCode: noInternetCode);
    } catch (e) {
      throw AuthException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  void initAnimations() {
    //
    profileAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 85));
    selfChallengeAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 85));

    profileSlideAnimation =
        Tween<Offset>(begin: Offset.zero, end: Offset(0.0, -0.0415)).animate(
            CurvedAnimation(
                parent: profileAnimationController, curve: Curves.easeIn));

    selfChallengeSlideAnimation =
        Tween<Offset>(begin: Offset.zero, end: Offset(0.0, -0.0415)).animate(
            CurvedAnimation(
                parent: selfChallengeAnimationController,
                curve: Curves.easeIn));

    firstAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 400));
    firstAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
            parent: firstAnimationController, curve: Curves.easeInOut));
    secondAnimationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 400));
    secondAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
            parent: secondAnimationController, curve: Curves.easeInOut));
  }

  void createAds() {
    Future.delayed(Duration.zero, () {
      context.read<InterstitialAdCubit>().createInterstitialAd(context);
    });
  }

  void showAppUnderMaintenanceDialog() {
    Future.delayed(Duration.zero, () {
      if (context.read<SystemConfigCubit>().appUnderMaintenance()) {
        showDialog(
            context: context, builder: (_) => AppUnderMaintenanceDialog());
      }
    });
  }

  void _initLocalNotification() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(onDidReceiveLocalNotification:
            (int id, String? title, String? body, String? payLoad) {
      print("For ios version <= 9 notification will be shown here");
    });

    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
    _requestPermissionsForIos();
  }

  Future<void> _requestPermissionsForIos() async {
    if (Platform.isIOS) {
      flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions();
    }
  }

  void setQuizMenu() {
    Future.delayed(Duration.zero, () {
      final systemCubit = context.read<SystemConfigCubit>();
      quizTypeTopMargin = systemCubit.isSelfChallengeEnable() ? 0.425 : 0.29;
      if (systemCubit.getIsContestAvailable() == "0") {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.contest);
      }
      if (systemCubit.getIsDailyQuizAvailable() == "0") {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.dailyQuiz);
      }
      if (!systemCubit.getIsAudioQuestionAvailable()) {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.audioQuestions);
      }
      if (systemCubit.getIsFunNLearnAvailable() == "0") {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.funAndLearn);
      } else {
        int index = _quizTypes.indexWhere(
            (element) => element.quizTypeEnum == QuizTypes.funAndLearn);
        _quizTypes[index].title = "reportQuestion";
        _quizTypes[index].description = "desReport";
      }
      if (!systemCubit.getIsGuessTheWordAvailable()) {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.guessTheWord);
      } else {
        int index = _quizTypes.indexWhere(
            (element) => element.quizTypeEnum == QuizTypes.guessTheWord);
        _quizTypes[index].title = "aboutUs";
        _quizTypes[index].description = "scrumpass";
      }
      if (!systemCubit.isMathQuizAvailable()) {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.mathMania);
      }
      if (systemCubit.getIsExamAvailable() == "0") {
        _quizTypes
            .removeWhere((element) => element.quizTypeEnum == QuizTypes.exam);
      } else {
        int index = _quizTypes
            .indexWhere((element) => element.quizTypeEnum == QuizTypes.exam);
        _quizTypes[index].title = "exam";
      }
      if (systemCubit.getReviewQuestionAvailable() == "0") {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.reviewQuestion);
      } else {
        int index = _quizTypes.indexWhere(
            (element) => element.quizTypeEnum == QuizTypes.reviewQuestion);
        _quizTypes[index].title = "reviewQuestionLbl";
        _quizTypes[index].description = "desReviewQuestion";
      }
      if (systemCubit.getStatisticsAvailable() == "0") {
        _quizTypes.removeWhere(
            (element) => element.quizTypeEnum == QuizTypes.statistics);
      } else {
        int index = _quizTypes.indexWhere(
            (element) => element.quizTypeEnum == QuizTypes.statistics);
        _quizTypes[index].description = "statisticLabel";
      }
      List titleQuizType = systemCubit.getMenuOrder() ?? [];

      List<QuizType> _tempQuizType = [];
      for (String title in titleQuizType) {
        int index = _quizTypes.indexWhere((element) =>
            element.quizTypeEnum == getQuizTypeEnumFromTitle(title));
        if (index != -1) {
          _tempQuizType.add(_quizTypes[index]);
        }
      }
      _quizTypes = _tempQuizType;
      setState(() {});
    });
  }

  late bool showUpdateContainer = false;

  void checkForUpdates() async {
    await Future.delayed(Duration.zero);
    if (context.read<SystemConfigCubit>().isForceUpdateEnable()) {
      try {
        bool forceUpdate = await UiUtils.forceUpdate(
            context.read<SystemConfigCubit>().getAppVersion());

        if (forceUpdate) {
          setState(() {
            showUpdateContainer = true;
          });
        }
      } catch (e) {
        print(e.toString());
      }
    }
  }

  Future<void> setupInteractedMessage() async {
    //
    if (Platform.isIOS) {
      await FirebaseMessaging.instance
          .requestPermission(announcement: true, provisional: true);
    }

    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
    // handle background notification
    FirebaseMessaging.onBackgroundMessage(UiUtils.onBackgroundMessage);
    //handle foreground notification
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print("Notification arrives : $message");
      var data = message.data;

      var title = data['title'].toString();
      var body = data['body'].toString();
      var type = data['type'].toString();

      var image = data['image'];

      //if notification type is badges then update badges in cubit list
      if (type == "badges") {
        String badgeType = data['badge_type'];
        Future.delayed(Duration.zero, () {
          context.read<BadgesCubit>().unlockBadge(badgeType);
        });
      }

      if (type == "payment_request") {
        Future.delayed(Duration.zero, () {
          context.read<UserDetailsCubit>().updateCoins(
                addCoin: true,
                coins: int.parse(data['coins'].toString()),
              );
        });
      }

      //payload is some data you want to pass in local notification
      image != null
          ? generateImageNotification(title, body, image, type, type)
          : generateSimpleNotification(title, body, type);
    });
  }

  // notification type is category then move to category screen
  Future<void> _handleMessage(RemoteMessage message) async {
    try {
      if (message.data['type'] == 'category') {
        Navigator.of(context).pushNamed(Routes.category,
            arguments: {"quizType": QuizTypes.quizZone});
      } else if (message.data['type'] == 'badges') {
        //if user open app by tapping
        UiUtils.updateBadgesLocally(context);
        Navigator.of(context).pushNamed(Routes.badges);
      } else if (message.data['type'] == "payment_request") {
        //UiUtils.needToUpdateCoinsLocally(context);
        Navigator.of(context).pushNamed(Routes.wallet);
      }
    } catch (e) {
      print(e);
    }
  }

  void _onTapLocalNotification(String? payload) async {
    //
    String type = payload ?? "";
    if (type == "badges") {
      Navigator.of(context).pushNamed(Routes.badges);
    } else if (type == "category") {
      Navigator.of(context).pushNamed(
        Routes.category,
      );
    } else if (type == "payment_request") {
      Navigator.of(context).pushNamed(Routes.wallet);
    }
  }

  Future<void> generateImageNotification(String title, String msg, String image,
      String payloads, String type) async {
    var largeIconPath = await _downloadAndSaveFile(image, 'largeIcon');
    var bigPicturePath = await _downloadAndSaveFile(image, 'bigPicture');
    var bigPictureStyleInformation = BigPictureStyleInformation(
        FilePathAndroidBitmap(bigPicturePath),
        hideExpandedLargeIcon: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: msg,
        htmlFormatSummaryText: true);
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      'com.wrteam.flutterquiz', //channel id
      'flutterquiz', //channel name
      channelDescription: 'flutterquiz',
      largeIcon: FilePathAndroidBitmap(largeIconPath),
      styleInformation: bigPictureStyleInformation,
    );
    var platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);
    await flutterLocalNotificationsPlugin
        .show(0, title, msg, platformChannelSpecifics, payload: payloads);
  }

  Future<String> _downloadAndSaveFile(String url, String fileName) async {
    final Directory directory = await getApplicationDocumentsDirectory();
    final String filePath = '${directory.path}/$fileName';
    final http.Response response = await http.get(Uri.parse(url));
    final File file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);
    return filePath;
  }

  // notification on foreground
  Future<void> generateSimpleNotification(
      String title, String body, String payloads) async {
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'com.wrteam.flutterquiz', //channel id
        'flutterquiz', //channel name
        channelDescription: 'flutterquiz',
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'ticker');
    const DarwinNotificationDetails iosNotificationDetails =
        DarwinNotificationDetails();

    var platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics, iOS: iosNotificationDetails);
    await flutterLocalNotificationsPlugin
        .show(0, title, body, platformChannelSpecifics, payload: payloads);
  }

  void showUpdateNameBottomSheet() {
    final updateUserDetailCubit = context.read<UpdateUserDetailCubit>();
    showModalBottomSheet(
        isDismissible: true,
        enableDrag: true,
        isScrollControlled: true,
        elevation: 5.0,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        )),
        context: context,
        builder: (context) {
          return EditProfileFieldBottomSheetContainer(
              canCloseBottomSheet: false,
              fieldTitle: nameLbl,
              fieldValue: context.read<UserDetailsCubit>().getUserName(),
              numericKeyboardEnable: false,
              updateUserDetailCubit: updateUserDetailCubit);
        });
  }

  @override
  void dispose() {
    ProfileManagementLocalDataSource.updateReversedCoins(0);

    profileAnimationController.dispose();
    selfChallengeAnimationController.dispose();
    firstAnimationController.dispose();
    secondAnimationController.dispose();

    WidgetsBinding.instance!.removeObserver(this);

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    //show you left the game
    if (state == AppLifecycleState.resumed) {
      UiUtils.needToUpdateCoinsLocally(context);
    } else {
      ProfileManagementLocalDataSource.updateReversedCoins(0);
    }
  }

  double _getTopMarginForQuizTypeContainer(int quizTypeIndex) {
    double topMarginPercentage = quizTypeTopMargin;
    int baseCondition = quizTypeIndex % 2 == 0 ? 0 : 1;
    for (int i = quizTypeIndex; i > baseCondition; i = i - 2) {
      //
      double topQuizTypeHeight = maxHeightQuizTypeIndexes.contains(i - 2)
          ? UiUtils.quizTypeMaxHeightPercentage
          : UiUtils.quizTypeMinHeightPercentage;

      topMarginPercentage = topMarginPercentage +
          quizTypeBetweenVerticalSpacing +
          topQuizTypeHeight;
    }
    return topMarginPercentage;
  }

  void startAnimation() async {
    selfChallengeAnimationController.forward().then((value) async {
      await profileAnimationController.forward();
      await selfChallengeAnimationController.reverse();
      profileAnimationController.reverse();
    });
  }

  void _navigateToQuizZone(int containerNumber) {
    //container number will be [1,2,3,4] if self chellenge is enable
    //container number will be [1,2,3,4,5,6] if self chellenge is not enable

    if (currentMenu == 1) {
      if (containerNumber == 1) {
        _onQuizTypeContainerTap(0);
      } else if (containerNumber == 2) {
        _onQuizTypeContainerTap(1);
      } else if (containerNumber == 3) {
        _onQuizTypeContainerTap(2);
      } else {
        if (context.read<SystemConfigCubit>().isSelfChallengeEnable()) {
          if (_quizTypes.length >= 4) {
            _onQuizTypeContainerTap(3);
          }
          return;
        }

        if (containerNumber == 4) {
          if (_quizTypes.length >= 4) {
            _onQuizTypeContainerTap(3);
          }
        } else if (containerNumber == 5) {
          if (_quizTypes.length >= 5) {
            _onQuizTypeContainerTap(4);
          }
        } else if (containerNumber == 6) {
          if (_quizTypes.length >= 6) {
            _onQuizTypeContainerTap(5);
          }
        }
      }
    } else if (currentMenu == 2) {
      //determine
      if (containerNumber == 1) {
        if (_quizTypes.length >= 5) {
          _onQuizTypeContainerTap(4);
        }
      } else if (containerNumber == 2) {
        if (_quizTypes.length >= 6) {
          _onQuizTypeContainerTap(5);
        }
      } else if (containerNumber == 3) {
        if (_quizTypes.length >= 7) {
          _onQuizTypeContainerTap(6);
        }
      } else {
        //if self challenge is enable
        if (context.read<SystemConfigCubit>().isSelfChallengeEnable()) {
          if (_quizTypes.length >= 8) {
            _onQuizTypeContainerTap(7);
            return;
          }
          return;
        }

        if (containerNumber == 4) {
          if (_quizTypes.length >= 8) {
            _onQuizTypeContainerTap(7);
          }
        } else if (containerNumber == 5) {
          if (_quizTypes.length >= 9) {
            _onQuizTypeContainerTap(8);
          }
        } else if (containerNumber == 6) {
          if (_quizTypes.length >= 10) {
            _onQuizTypeContainerTap(9);
          }
        }
      }
    } else {
      //for menu 3
      if (containerNumber == 1) {
        if (_quizTypes.length >= 9) {
          _onQuizTypeContainerTap(8);
        }
      } else if (containerNumber == 2) {
        if (_quizTypes.length >= 10) {
          _onQuizTypeContainerTap(9);
        }
      } else if (containerNumber == 3) {
        if (_quizTypes.length >= 11) {
          _onQuizTypeContainerTap(10);
        }
      } else {
        if (_quizTypes.length == 12) {
          _onQuizTypeContainerTap(11);
        }
      }
    }
  }

  void _onQuizTypeContainerTap(int quizTypeIndex) {
    if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.dailyQuiz) {
      if (context.read<SystemConfigCubit>().getIsDailyQuizAvailable() == "1") {
        Navigator.of(context).pushNamed(Routes.quiz, arguments: {
          "quizType": QuizTypes.dailyQuiz,
          "numberOfPlayer": 1,
          "quizName": "Daily Quiz"
        });
      } else {
        UiUtils.setSnackbar(
            AppLocalization.of(context)!
                .getTranslatedValues(currentlyNotAvailableKey)!,
            context,
            false);
      }
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.quizZone) {
      Navigator.of(context).pushNamed(Routes.category,
          arguments: {"quizType": QuizTypes.quizZone});
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.selfChallenge) {
      Navigator.of(context).pushNamed(Routes.selfChallenge);
    } //
    else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.battle) {
      //
      context.read<BattleRoomCubit>().updateState(BattleRoomInitial());
      context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());

      showDialog(
        context: context,
        builder: (context) => MultiBlocProvider(providers: [
          BlocProvider<UpdateScoreAndCoinsCubit>(
              create: (_) =>
                  UpdateScoreAndCoinsCubit(ProfileManagementRepository())),
        ], child: RandomOrPlayFrdDialog()),
      );
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.trueAndFalse) {
      Navigator.of(context).pushNamed(Routes.quiz, arguments: {
        "quizType": QuizTypes.trueAndFalse,
        "numberOfPlayer": 1,
        "quizName": "True & False"
      });
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.guessTheWord) {
      Navigator.of(context).pushNamed(Routes.aboutApp);
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.groupPlay) {
      context
          .read<MultiUserBattleRoomCubit>()
          .updateState(MultiUserBattleRoomInitial());

      context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());
      //
      showDialog(
          context: context,
          builder: (context) => MultiBlocProvider(providers: [
                BlocProvider<UpdateScoreAndCoinsCubit>(
                    create: (_) => UpdateScoreAndCoinsCubit(
                        ProfileManagementRepository())),
              ], child: RoomDialog(quizType: QuizTypes.groupPlay)));
      //
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.contest) {
      if (context.read<SystemConfigCubit>().getIsContestAvailable() == "1") {
        Navigator.of(context).pushNamed(Routes.contest);
      } else {
        UiUtils.setSnackbar(
            AppLocalization.of(context)!
                .getTranslatedValues(currentlyNotAvailableKey)!,
            context,
            false);
      }
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.guessTheWord) {
      /* Navigator.of(context).pushNamed(Routes.category,
          arguments: {"quizType": QuizTypes.guessTheWord}); */
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.audioQuestions) {
      Navigator.of(context).pushNamed(Routes.category,
          arguments: {"quizType": QuizTypes.audioQuestions});
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.exam) {
      //update exam status to exam initial
      // context.read<ExamCubit>().updateState(ExamInitial());
      Navigator.of(context)
          .pushNamed(Routes.exams)
          .then((value) => {setState(() {})});
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.mathMania) {
      Navigator.of(context).pushNamed(Routes.category,
          arguments: {"quizType": QuizTypes.mathMania});
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.reviewQuestion) {
      Navigator.of(context).pushNamed(Routes.reviewQuestions);
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum == QuizTypes.statistics) {
      if (context.read<SystemConfigCubit>().getStatisticsAvailable() == "1") {
        Navigator.of(context).pushNamed(Routes.statistics);
      } else {
        UiUtils.setSnackbar(
            AppLocalization.of(context)!
                .getTranslatedValues(currentlyNotAvailableKey)!,
            context,
            false);
      }
    } else if (_quizTypes[quizTypeIndex].quizTypeEnum ==
        QuizTypes.funAndLearn) {
      if (context.read<SystemConfigCubit>().getIsFunNLearnAvailable() == "1") {
        Navigator.of(context).pushNamed(Routes.report);
      } else {
        UiUtils.setSnackbar(
            AppLocalization.of(context)!
                .getTranslatedValues(currentlyNotAvailableKey)!,
            context,
            false);
      }
    }
  }

  Widget _buildProfileContainer(double statusBarPadding) {
    return Align(
      alignment: Alignment.topCenter,
      child: GestureDetector(
        onTap: () {
          //
          Navigator.of(context).pushNamed(Routes.profile);
        },
        child: SlideTransition(
          position: profileSlideAnimation,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10.0),
            child: BlocBuilder<UserDetailsCubit, UserDetailsState>(
              bloc: context.read<UserDetailsCubit>(),
              builder: (context, state) {
                if (state is UserDetailsFetchSuccess) {
                  return Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: Theme.of(context).primaryColor,
                        radius: 37.5,
                        backgroundImage: CachedNetworkImageProvider(
                            state.userProfile.profileUrl!),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * (0.0175),
                      ),
                      Flexible(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  state.userProfile.name ?? "",
                                  style: TextStyle(
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  state.userProfile.email!.isEmpty
                                      ? state.userProfile.mobileNumber!
                                      : state.userProfile.email!,
                                  style: TextStyle(
                                    fontSize: 13.0,
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.6),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(
                                  height: constraints.maxHeight * (0.05),
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    UserAchievementContainer(
                                        title: AppLocalization.of(context)!
                                            .getTranslatedValues("totalQuiz")!,
                                        value: Common.totalQuiz),
                                    SizedBox(
                                      width: 5,
                                    ),
                                    UserAchievementContainer(
                                        title: AppLocalization.of(context)!
                                            .getTranslatedValues("recent")!,
                                        value: Common.totalQuiz == "No Data"
                                            ? "No Data"
                                            : Common.recent + "%"),
                                    SizedBox(
                                      width: 5,
                                    ),
                                    UserAchievementContainer(
                                        title: AppLocalization.of(context)!
                                            .getTranslatedValues("average")!,
                                        value: Common.totalQuiz == "No Data"
                                            ? "No Data"
                                            : Common.average + "%"),
                                  ], //
                                ),
                              ],
                            );
                          },
                        ),
                      )
                    ],
                  );
                }
                return Container();
              },
            ),
            margin: EdgeInsets.only(
                top: MediaQuery.of(context).size.height * (0.085) +
                    statusBarPadding),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              //gradient: UiUtils.buildLinerGradient([Theme.of(context).primaryColor, Theme.of(context).colorScheme.secondary], Alignment.topCenter, Alignment.bottomCenter),
              boxShadow: [
                UiUtils.buildBoxShadow(offset: Offset(5, 5), blurRadius: 10.0),
              ],
              borderRadius: BorderRadius.circular(30.0),
            ),
            width: MediaQuery.of(context).size.width * (0.84),
            height: MediaQuery.of(context).size.height * (0.16),
          ),
        ),
      ),
    );
  }

  Widget _buildSelfChallenge(double statusBarPadding) {
    return GestureDetector(
      onTap: () {
        context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());
        context.read<SubCategoryCubit>().updateState(SubCategoryInitial());
        Navigator.of(context)
            .pushNamed(Routes.practice)
            .then((value) => {setState(() {})});
      },
      child: Align(
        alignment: Alignment.topCenter,
        child: SlideTransition(
          position: selfChallengeSlideAnimation,
          child: Container(
            margin: EdgeInsets.only(
                top: MediaQuery.of(context).size.height * 0.28 +
                    statusBarPadding),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,

              //gradient: UiUtils.buildLinerGradient([Theme.of(context).colorScheme.secondary, Theme.of(context).primaryColor], Alignment.centerLeft, Alignment.centerRight),

              boxShadow: [
                UiUtils.buildBoxShadow(
                    offset: Offset(5.0, 5.0), blurRadius: 10.0)
              ],
              borderRadius: BorderRadius.circular(20.0),
            ),
            width: MediaQuery.of(context).size.width * (0.84),
            height: MediaQuery.of(context).size.height * (0.1),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20.0),
              child: Stack(
                clipBehavior: Clip.hardEdge,
                children: [
                  Align(
                    alignment: AlignmentDirectional.centerStart,
                    child: Container(
                      margin: EdgeInsetsDirectional.only(start: 20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            AppLocalization.of(context)!
                                .getTranslatedValues(practiceModeLbl)!,
                            style: TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          SizedBox(
                            height: 1.0,
                          ),
                          Text(
                            AppLocalization.of(context)!
                                .getTranslatedValues(practiceAnytimeLbl)!,
                            style: TextStyle(
                              fontSize: 14.0,
                              color: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: AlignmentDirectional.centerEnd,
                    child: Transform.scale(
                        scale: 0.55,
                        child: SvgPicture.asset(
                            "assets/images/selfchallenge_icon.svg")),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuizType(int quizTypeIndex, double statusBarPadding) {
    double quizTypeHorizontalMargin =
        MediaQuery.of(context).size.width * quizTypeHorizontalMarginPercentage;
    double topMarginPercentage = quizTypeTopMargin;

    if (quizTypeIndex - 2 < 0) {
      topMarginPercentage = quizTypeTopMargin;
    } else {
      topMarginPercentage = _getTopMarginForQuizTypeContainer(quizTypeIndex);
    }

    bool isLeft = quizTypeIndex % 2 == 0;

    if (quizTypeIndex < 4) {
      return AnimatedBuilder(
        builder: (context, child) {
          return Positioned(
            top: (MediaQuery.of(context).size.height * topMarginPercentage) +
                statusBarPadding,
            left: isLeft ? quizTypeHorizontalMargin : null,
            right: isLeft ? null : quizTypeHorizontalMargin,
            child: SlideTransition(
              position: firstAnimation.drive<Offset>(Tween<Offset>(
                  begin: Offset.zero, end: Offset(isLeft ? -1.0 : 1.0, 0))),
              child: FadeTransition(
                opacity: firstAnimation
                    .drive<double>(Tween<double>(begin: 1.0, end: 0.0)),
                child: child!,
              ),
            ),
          );
        },
        animation: firstAnimationController,
        child: QuizTypeContainer(
          quizType: _quizTypes[quizTypeIndex],
          widthPercentage: quizTypeWidthPercentage,
          heightPercentage: maxHeightQuizTypeIndexes.contains(quizTypeIndex)
              ? UiUtils.quizTypeMaxHeightPercentage
              : UiUtils.quizTypeMinHeightPercentage,
        ),
      );
    } else if (quizTypeIndex < 8) {
      return AnimatedBuilder(
        builder: (context, child) {
          double endMarginPercentage = quizTypeTopMargin;
          //change static number to length of menu
          if (quizTypeIndex == 6 || quizTypeIndex == 7) {
            double previousTopQuizTypeHeight =
                maxHeightQuizTypeIndexes.contains(quizTypeIndex - 2)
                    ? UiUtils.quizTypeMaxHeightPercentage
                    : UiUtils.quizTypeMinHeightPercentage;
            endMarginPercentage = quizTypeTopMargin +
                quizTypeBetweenVerticalSpacing +
                previousTopQuizTypeHeight;
          }

          double topPositionPercentage = firstAnimation
              .drive<double>(
                  Tween(begin: topMarginPercentage, end: endMarginPercentage))
              .value;

          return Positioned(
            top: (MediaQuery.of(context).size.height * topPositionPercentage) +
                statusBarPadding,
            left: isLeft ? quizTypeHorizontalMargin : null,
            right: isLeft ? null : quizTypeHorizontalMargin,
            child: SlideTransition(
              position: secondAnimation.drive<Offset>(Tween<Offset>(
                  begin: Offset.zero, end: Offset(isLeft ? -1.0 : 1.0, 0))),
              child: FadeTransition(
                opacity: secondAnimation
                    .drive<double>(Tween<double>(begin: 1.0, end: 0.0)),
                child: child!,
              ),
            ),
          );
        },
        animation: firstAnimationController,
        child: QuizTypeContainer(
          quizType: _quizTypes[quizTypeIndex],
          widthPercentage: quizTypeWidthPercentage,
          heightPercentage: maxHeightQuizTypeIndexes.contains(quizTypeIndex)
              ? UiUtils.quizTypeMaxHeightPercentage
              : UiUtils.quizTypeMinHeightPercentage,
        ),
      );
    } else {
      return AnimatedBuilder(
        animation: firstAnimationController,
        builder: (context, child) {
          return AnimatedBuilder(
            builder: (context, child) {
              double firstEndMarginPercentage =
                  _getTopMarginForQuizTypeContainer(quizTypeIndex - 4);
              double topPositionPercentage = 0.0;

              topPositionPercentage = firstAnimation
                  .drive<double>(Tween(
                      begin: topMarginPercentage,
                      end: firstEndMarginPercentage))
                  .value;

              double previousTopQuizTypeHeight = 0;
              if (quizTypeIndex == 10 || quizTypeIndex == 11) {
                previousTopQuizTypeHeight =
                    maxHeightQuizTypeIndexes.contains(quizTypeIndex - 2)
                        ? UiUtils.quizTypeMaxHeightPercentage
                        : UiUtils.quizTypeMinHeightPercentage;
                previousTopQuizTypeHeight =
                    quizTypeBetweenVerticalSpacing + previousTopQuizTypeHeight;
              }
              topPositionPercentage = topPositionPercentage -
                  (firstEndMarginPercentage -
                          quizTypeTopMargin -
                          previousTopQuizTypeHeight) *
                      (secondAnimation
                          .drive<double>(Tween(begin: 0.0, end: 1.0))
                          .value);

              return Positioned(
                top: (MediaQuery.of(context).size.height *
                        topPositionPercentage) +
                    statusBarPadding,
                left: isLeft ? quizTypeHorizontalMargin : null,
                right: isLeft ? null : quizTypeHorizontalMargin,
                child: child!,
              );
            },
            animation: secondAnimationController,
            child: QuizTypeContainer(
              quizType: _quizTypes[quizTypeIndex],
              widthPercentage: quizTypeWidthPercentage,
              heightPercentage: maxHeightQuizTypeIndexes.contains(quizTypeIndex)
                  ? UiUtils.quizTypeMaxHeightPercentage
                  : UiUtils.quizTypeMinHeightPercentage,
            ),
          );
        },
      );
    }
  }

  List<Widget> _buildQuizTypes(double statusBarPadding) {
    List<Widget> children = [];
    for (int i = 0; i < _quizTypes.length; i++) {
      children.add(_buildQuizType(i, statusBarPadding));
    }
    return children;
  }

  Widget _buildTopMenu(double statusBarPadding) {
    return Align(
      alignment: Alignment.topRight,
      child: Padding(
        padding: EdgeInsets.only(top: statusBarPadding + 5.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Container(
            //   width: 45,
            //   height: 40,
            //   decoration: BoxDecoration(
            //     color: Theme.of(context).colorScheme.surface,
            //     boxShadow: [
            //       UiUtils.buildBoxShadow(
            //           offset: Offset(5, 5), blurRadius: 10.0),
            //     ],
            //     borderRadius: BorderRadius.circular(15.0),
            //   ),
            //   child: IconButton(
            //     onPressed: () {
            //       Navigator.of(context).pushNamed(Routes.leaderBoard);
            //     },
            //     icon: SvgPicture.asset(
            //       UiUtils.getImagePath("leaderboard_dark.svg"),
            //     ),
            //   ),
            // ),
            // if (!Common.premium &&
            //     Common.registeredFromApp &&
            //     !Common.inPremiumGroup)
            //   Container(
            //     width: 45,
            //     height: 40,
            //     decoration: BoxDecoration(
            //       color: Theme.of(context).colorScheme.surface,
            //       boxShadow: [
            //         UiUtils.buildBoxShadow(
            //             offset: Offset(5, 5), blurRadius: 10.0),
            //       ],
            //       borderRadius: BorderRadius.circular(15.0),
            //     ),
            //     child: IconButton(
            //       onPressed: () {
            //         Navigator.push(
            //           context,
            //           MaterialPageRoute(builder: (context) => PaymentPage()),
            //         );
            //       },
            //       icon: Icon(
            //         Icons.workspace_premium,
            //         color: Theme.of(context).primaryColor,
            //       ),
            //     ),
            //   ),
            // SizedBox(
            //   width: 12.5,
            // ),
            Container(
              width: 45,
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  UiUtils.buildBoxShadow(
                      offset: Offset(5, 5), blurRadius: 10.0),
                ],
                borderRadius: BorderRadius.circular(15.0),
              ),
              child: IconButton(
                onPressed: () {
                  showModalBottomSheet(
                      isDismissible: true,
                      enableDrag: true,
                      isScrollControlled: true,
                      elevation: 5.0,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.0),
                        topRight: Radius.circular(20.0),
                      )),
                      context: context,
                      builder: (context) {
                        return MenuBottomSheetContainer();
                      });
                },
                icon: Icon(
                  Icons.menu,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width * (0.085),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopMenuContainer(double statusBarPadding) {
    return Positioned(
      top: MediaQuery.of(context).size.height * (quizTypeTopMargin) +
          statusBarPadding,
      child: GestureDetector(
        onTap: () {},
        onTapUp: (tapDownDetails) {
          double firstTapStartDx = quizTypeHorizontalMarginPercentage *
              MediaQuery.of(context).size.width;
          double topTapStartDy =
              quizTypeTopMargin * MediaQuery.of(context).size.height +
                  statusBarPadding;

          double secondTapStartDx = MediaQuery.of(context).size.width -
              MediaQuery.of(context).size.width *
                  (quizTypeWidthPercentage +
                      quizTypeHorizontalMarginPercentage);

          double thirdTapStartDy = MediaQuery.of(context).size.height *
                  (quizTypeBetweenVerticalSpacing +
                      quizTypeTopMargin +
                      UiUtils.quizTypeMaxHeightPercentage) +
              statusBarPadding;
          double fourthTapStartDy = MediaQuery.of(context).size.height *
                  (quizTypeBetweenVerticalSpacing +
                      quizTypeTopMargin +
                      UiUtils.quizTypeMinHeightPercentage) +
              statusBarPadding;

          double fifthTapStartDy = thirdTapStartDy +
              MediaQuery.of(context).size.height *
                  (quizTypeBetweenVerticalSpacing +
                      UiUtils.quizTypeMinHeightPercentage);

          double sixTapStartDy = fourthTapStartDy +
              MediaQuery.of(context).size.height *
                  (quizTypeBetweenVerticalSpacing +
                      UiUtils.quizTypeMaxHeightPercentage);

          if (tapDownDetails.globalPosition.dx >= firstTapStartDx &&
              tapDownDetails.globalPosition.dx <=
                  (firstTapStartDx +
                      MediaQuery.of(context).size.width *
                          quizTypeWidthPercentage)) {
            //
            if (tapDownDetails.globalPosition.dy >= topTapStartDy &&
                tapDownDetails.globalPosition.dy <=
                    (topTapStartDy +
                        (MediaQuery.of(context).size.height *
                            UiUtils.quizTypeMaxHeightPercentage))) {
              _navigateToQuizZone(1);
            } else if (tapDownDetails.globalPosition.dy >= thirdTapStartDy &&
                tapDownDetails.globalPosition.dy <=
                    (thirdTapStartDy +
                        (MediaQuery.of(context).size.height *
                            UiUtils.quizTypeMinHeightPercentage))) {
              _navigateToQuizZone(3);
            } else {
              if (!context.read<SystemConfigCubit>().isSelfChallengeEnable()) {
                if (tapDownDetails.globalPosition.dy >= fifthTapStartDy &&
                    tapDownDetails.globalPosition.dy <=
                        (fifthTapStartDy +
                            (MediaQuery.of(context).size.height *
                                UiUtils.quizTypeMaxHeightPercentage))) {
                  _navigateToQuizZone(5);
                }
              }
            }
          } else if (tapDownDetails.globalPosition.dx >= secondTapStartDx &&
              tapDownDetails.globalPosition.dx <=
                  (secondTapStartDx +
                      MediaQuery.of(context).size.width *
                          quizTypeWidthPercentage)) {
            if (tapDownDetails.globalPosition.dy >= topTapStartDy &&
                tapDownDetails.globalPosition.dy <=
                    (topTapStartDy +
                        (MediaQuery.of(context).size.height *
                            UiUtils.quizTypeMinHeightPercentage))) {
              _navigateToQuizZone(2);
            } else if (tapDownDetails.globalPosition.dy >= fourthTapStartDy &&
                tapDownDetails.globalPosition.dy <=
                    (fourthTapStartDy +
                        (MediaQuery.of(context).size.height *
                            UiUtils.quizTypeMaxHeightPercentage))) {
              _navigateToQuizZone(4);
            } else {
              if (!context.read<SystemConfigCubit>().isSelfChallengeEnable()) {
                if (tapDownDetails.globalPosition.dy >= sixTapStartDy &&
                    tapDownDetails.globalPosition.dy <=
                        (sixTapStartDy +
                            (MediaQuery.of(context).size.height *
                                UiUtils.quizTypeMinHeightPercentage))) {
                  _navigateToQuizZone(6);
                }
              }
            }
          }
        },
        dragStartBehavior: DragStartBehavior.start,
        onVerticalDragUpdate: (dragUpdateDetails) {
          //
          if (_quizTypes.length <= 4) {
            return;
          }

          if (currentMenu == 1) {
            //when firstMenu is selected
            double dragged = dragUpdateDetails.primaryDelta! /
                MediaQuery.of(context).size.height;
            firstAnimationController.value =
                firstAnimationController.value - 2.50 * dragged;
          } else if (currentMenu == 2) {
            //when second _quizTypes
            if (dragUP == null) {
              if (dragUpdateDetails.primaryDelta! < 0) {
                if (_quizTypes.length > 8) {
                  dragUP = true;
                }
              } else if (dragUpdateDetails.primaryDelta! > 0) {
                dragUP = false;
              } else {}
            }

            //
            if (dragUP != null) {
              if (dragUP!) {
                double dragged = dragUpdateDetails.primaryDelta! /
                    MediaQuery.of(context).size.height;
                secondAnimationController.value =
                    secondAnimationController.value - 2.50 * dragged;
              } else {
                double dragged = dragUpdateDetails.primaryDelta! /
                    MediaQuery.of(context).size.height;
                firstAnimationController.value =
                    firstAnimationController.value - 2.50 * dragged;
              }
            }
          } else {
            double dragged = dragUpdateDetails.primaryDelta! /
                MediaQuery.of(context).size.height;
            secondAnimationController.value =
                secondAnimationController.value - 2.50 * dragged;
          }
        },
        onVerticalDragEnd: (dragEndDetails) async {
          if (currentMenu == 1) {
            if (firstAnimationController.value > 0.3) {
              currentMenu = 2;
              await firstAnimationController.forward();
              startAnimation();
            } else {
              firstAnimationController.reverse();
              currentMenu = 1;
            }
          } else if (currentMenu == 2) {
            //when currentMenu is 2 then handle this condition
            if (dragUP != null) {
              if (dragUP!) {
                if (secondAnimationController.value > 0.3) {
                  currentMenu = 3;
                  await secondAnimationController.forward();
                  startAnimation();
                } else {
                  secondAnimationController.reverse();
                  currentMenu = 2;
                }
              } else {
                if (firstAnimationController.value > 0.7) {
                  firstAnimationController.forward();
                  currentMenu = 2;
                } else {
                  firstAnimationController.reverse();
                  currentMenu = 1;
                }
              }
            }

            dragUP = null;
          } else {
            //
            if (secondAnimationController.value > 0.7) {
              secondAnimationController.forward();
              currentMenu = 3;
            } else {
              secondAnimationController.reverse();
              currentMenu = 2;
            }
          }
        },
        child: Container(
          color: Colors.transparent,
          width: MediaQuery.of(context).size.width,
          height:
              MediaQuery.of(context).size.height * (1.0 - quizTypeTopMargin),
        ),
      ),
    );
  }

  Widget _buildHomeScreen(List<Widget> children) {
    return Stack(
      children: [
        PageBackgroundGradientContainer(),
        ...children,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final statusBarPadding = MediaQuery.of(context).padding.top;
    return Scaffold(
      body: BlocConsumer<UserDetailsCubit, UserDetailsState>(
        listener: (context, state) {
          if (state is UserDetailsFetchSuccess) {
            UiUtils.fetchBookmarkAndBadges(
                context: context, userId: state.userProfile.userId!);

            if (state.userProfile.name!.isEmpty) {
              showUpdateNameBottomSheet();
            } else if (state.userProfile.profileUrl!.isEmpty) {
              Navigator.of(context)
                  .pushNamed(Routes.selectProfile, arguments: false);
            }
          } else if (state is UserDetailsFetchFailure) {
            if (state.errorMessage == unauthorizedAccessCode) {
              UiUtils.showAlreadyLoggedInDialog(context: context);
            }
          }
        },
        bloc: context.read<UserDetailsCubit>(),
        builder: (context, state) {
          if (state is UserDetailsFetchInProgress ||
              state is UserDetailsInitial) {
            return _buildHomeScreen([
              Center(
                child: CircularProgressContainer(
                  useWhiteLoader: false,
                ),
              )
            ]);
          }
          if (state is UserDetailsFetchFailure) {
            return _buildHomeScreen([
              ErrorContainer(
                showBackButton: true,
                errorMessage: AppLocalization.of(context)!.getTranslatedValues(
                    convertErrorCodeToLanguageKey(state.errorMessage))!,
                onTapRetry: () {
                  context.read<UserDetailsCubit>().fetchUserDetails(
                      context.read<AuthCubit>().getUserFirebaseId());
                },
                showErrorImage: true,
                errorMessageColor: Theme.of(context).primaryColor,
              )
            ]);
          }

          UserProfile userProfile =
              (state as UserDetailsFetchSuccess).userProfile;
          if (userProfile.status == "0") {
            return _buildHomeScreen([
              ErrorContainer(
                showBackButton: true,
                errorMessage: AppLocalization.of(context)!
                    .getTranslatedValues(accountDeactivatedKey)!,
                onTapRetry: () {
                  context.read<UserDetailsCubit>().fetchUserDetails(
                      context.read<AuthCubit>().getUserFirebaseId());
                },
                showErrorImage: true,
                errorMessageColor: Theme.of(context).primaryColor,
              )
            ]);
          }

          return _buildHomeScreen([
            _buildTopMenu(statusBarPadding),
            _buildProfileContainer(statusBarPadding),
            context.read<SystemConfigCubit>().isSelfChallengeEnable()
                ? _buildSelfChallenge(statusBarPadding)
                : SizedBox(),
            ..._buildQuizTypes(statusBarPadding),
            _buildTopMenuContainer(statusBarPadding),
            showUpdateContainer ? UpdateAppContainer() : Container(),
          ]);
        },
      ),
    );
  }
}
