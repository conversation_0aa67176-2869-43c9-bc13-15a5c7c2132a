import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/ui/styles/colors.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';

class TimeOutDialog extends StatefulWidget {
  final bool isSubmit;
  const TimeOutDialog({this.isSubmit = false, Key? key}) : super(key: key);

  @override
  State<TimeOutDialog> createState() => _TimeOutDialogState();
}

class _TimeOutDialogState extends State<TimeOutDialog> {
  void onPressed(BuildContext context) {}

  @override
  Widget build(BuildContext context) {
    return Container(
      child: AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        content: widget.isSubmit
            ? CircularProgressContainer(useWhiteLoader: false)
            : Text(
                AppLocalization.of(context)!
                    .getTranslatedValues("timeUpDialog")!,
                style:
                    TextStyle(color: Theme.of(context).colorScheme.secondary),
              ),
        actions: [
          if (!widget.isSubmit)
            CupertinoButton(
                child: Text(
                    AppLocalization.of(context)!
                        .getTranslatedValues("okayLbl")!,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                    )),
                onPressed: () {
                  Navigator.of(context).pop();
                }),
        ],
      ),
    );
  }
}
