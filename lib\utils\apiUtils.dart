import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/auth/authLocalDataSource.dart';
import 'package:flutterquiz/features/auth/authRemoteDataSource.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class ApiUtils {
  String url = getUserInfor().url;
  static Future<Map<String, String>> getHeaders() async {
    String jwtToken = AuthLocalDataSource.getJwtToken();

    if (jwtToken.isEmpty) {
      try {
        jwtToken = await AuthRemoteDataSource().getJWTTokenOfUser(
            firebaseId: AuthLocalDataSource.getUserFirebaseId(),
            type: AuthLocalDataSource.getAuthType());
        await AuthLocalDataSource.setJwtToken(jwtToken);
      } catch (e) {}
    }

    return {"Authorization": 'Bearer $jwtToken'};
  }

  Future<String> attempPost(String info) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= "";
    final response = await http.post(Uri.parse(url),
        body: {"key": appkey, "token": token, "info": info});

    if (response.statusCode == 200) {
      preferences.setString('token', response.body);
      return response.body;
    } else {
      //status = false;
      return "";
    }
  }

  Future<void> getListWrongAns(BuildContext context) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getWrongAns----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_wrong_answered"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('wrongAnsApi', response.body);
        }
      } catch (e) {
        attempPost(info);
      }
    }
  }

  Future<void> getListMarkedAns(BuildContext context) async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getMarkedAns----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_marked_question"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('markedAnsApi', response.body);
        }
      } catch (e) {
        attempPost(info);
      }
    }
  }
}
