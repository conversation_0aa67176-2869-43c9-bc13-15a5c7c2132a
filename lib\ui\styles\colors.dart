import 'package:flutter/material.dart';

final Color primaryColor = Color(0xffF98367);
final Color secondaryColor = Color(0xff434B5D); //work as secondary color
final Color backgroundColor = Colors.white;
final Color pageBackgroundColor = Color(0xFFFFEEEA);
final Color canvasColor = Color(0xffFFF4DE);

final Color darkPrimaryColor = Color(0xffF88166);
final Color darkSecondaryColor = Colors.white; //work as secondary color
final Color darkBackgroundColor = Color(0xff393939);
final Color darkPageBackgroundColor = Color(0xFF3D3D3D);
final Color darkCanvasColor = Color(0xff5A5A5A);

//it will be same for both light and dark theme
final Color badgeLockedColor = Colors.grey;
final Color addCoinColor = Colors.green;
final Color hurryUpTimerColor = Colors.red;//timer color when 25% time left

