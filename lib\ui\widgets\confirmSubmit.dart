import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/ui/styles/colors.dart';

class ConfirmSubmitDailog extends StatelessWidget {
  final Function? onTapYes;
  const ConfirmSubmitDailog({Key? key, this.onTapYes}) : super(key: key);

  void onPressed(BuildContext context) {}

  @override
  Widget build(BuildContext context) {
    return Container(
      child: AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        content: Text(
          AppLocalization.of(context)!
              .getTranslatedValues("confirmSubmitQuiz")!,
          style: TextStyle(color: Theme.of(context).colorScheme.secondary),
        ),
        actions: [
          CupertinoButton(
              child: Text(
                AppLocalization.of(context)!.getTranslatedValues("agreeLbl")!,
                style: TextStyle(
                  color: primaryColor,
                ),
              ),
              onPressed: () {
                if (onTapYes != null) {
                  onTapYes!();
                } else {
                  Navigator.of(context).pop();

                  Navigator.of(context).pop();
                }
              }),
          CupertinoButton(
              child: Text(
                  AppLocalization.of(context)!.getTranslatedValues("cancel")!,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  )),
              onPressed: () {
                Navigator.of(context).pop();
              }),
        ],
      ),
    );
  }
}
