class Report {
  String? reportId;
  String? qid;
  String? uid;
  String? quid;
  String? email;
  String? reportQuestion;
  String? reportDetail;
  List? response;
  String? reportResponse;
  String? type;
  String? app;
  String? status;
  String? feedbackId;
  String? createdDate;
  String? updatedDate;

  Report({
    this.reportId,
    this.qid,
    this.uid,
    this.quid,
    this.email,
    this.reportQuestion,
    this.reportDetail,
    this.reportResponse,
    this.type,
    this.app,
    this.status,
    this.feedbackId,
    this.createdDate,
    this.updatedDate,
    this.response,
  });

  Report.fromJson(Map<String, dynamic> json) {
    this.reportId = json["report_id"];
    this.qid = json["qid"];
    this.uid = json["uid"];
    this.quid = json["quid"];
    this.email = json["email"];
    this.reportQuestion = json["report_question"];
    this.reportDetail = json["report_detail"];
    this.reportResponse = json["report_response"];
    this.type = json["type"];
    this.app = json["app"];
    this.status = json["status"];
    this.feedbackId = json["feedback_id"];
    this.createdDate = json["created_date"];
    this.updatedDate = json["updated_date"];
    this.response = json['response'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["report_id"] = this.reportId;
    data["qid"] = this.qid;
    data["uid"] = this.uid;
    data["quid"] = this.quid;
    data["email"] = this.email;
    data["report_question"] = this.reportQuestion;
    data["report_detail"] = this.reportDetail;
    data["report_response"] = this.reportResponse;
    data["type"] = this.type;
    data["app"] = this.app;
    data["status"] = this.status;
    data["feedback_id"] = this.feedbackId;
    data["created_date"] = this.createdDate;
    data["updated_date"] = this.updatedDate;
    data['response'] = this.response;
    return data;
  }
}
