import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_urls.dart';
import 'package:flutterquiz/ui/screens/model/question_api_model.dart';
import 'package:flutterquiz/utils/dio.dart';
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert' as json;

import 'package:internet_connection_checker/internet_connection_checker.dart';

final String appkey = getUserInfor().appkey;
String url = getUserInfor().url;
final dio = NewDio().getDio();

class ApiServices {
  int retryCount = 0;
  String question = '';
  Future<List<Question>> fetchQuestion(id) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      await dio
          .post(ApiUrls().API_QUIZ_BY_ID + id,
              data: FormData.fromMap({"key": appkey, "token": token, "info": "Do Quiz"}))
          .timeout(const Duration(seconds: 30))
          .then((response) {
            final String jsonBody = response.data;
            final int statusCode = response.statusCode ?? 0;

            if (statusCode != 200 || jsonBody == null) {
              throw new Exception("Api Error");
            } else {
              preferences.setString('question_' + id, jsonBody);
            }
          });
    }
    question = preferences.getString('question_' + id) ?? '';
    final JsonDecoder _decoder = new JsonDecoder();
    final useListContainer = _decoder.convert(question);
    final List questionList = useListContainer;
    return questionList.map((e) => new Question.fromJson(e)).toList();
  }

  Future<List<dynamic>> sendResult(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    String rid = '';
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "- Send Result";
      print(data);
      final response =
          await dio.post(ApiUrls().API_SAVE_RESULT, data: FormData.fromMap({
        "key": appkey,
        "token": token,
        "info": info,
        "data": data,
        "platform": Common.platform
      }));
      print('send result, retry: ${retryCount}');
      if (response.statusCode == 200 && response.data.contains('success')) {
        sendDatatSucceed = true;
        rid = response.data.replaceAll("success", "");
        retryCount = 0;
      } else if (sendDatatSucceed == false && retryCount < 5) {
        retryFuture(sendResult, data, 500);
        retryCount++;
      }
    }
    List result = [];
    result.add(sendDatatSucceed);
    result.add(rid);
    return result;
  }

  Future<dynamic> checkVersion() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    if (connect) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo() + "- Check version";
      final response =
          await dio.post(ApiUrls().API_CHECK_VERSION, data: FormData.fromMap({
        "key": appkey,
        "token": token,
        "info": info,
        "version": version,
        "appid": Common.appid
      }));
      print('check version, retry: ${retryCount}');
      if (response.statusCode == 200 && response.data != null) {
        sendDatatSucceed = true;
        retryCount = 0;
        return response.data;
      } else if (sendDatatSucceed == false && retryCount < 5) {
        retryFuture(checkVersion, "", 500);
        retryCount++;
      }
      throw new Exception("Api Error");
    } else {
      return false;
    }
  }

  Future<dynamic> savePurchaseInfo(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo() + "- Save Purchase Info";
      final response = await dio.post(ApiUrls().API_SAVE_PURCHASE,
          data: FormData.fromMap({
            "key": appkey,
            "token": token,
            "info": info,
            "data": data,
            "appid": Common.appid
          }));
      print('save purchase info, retry: ${retryCount}');
      if (response.statusCode == 200 && response.data != null) {
        sendDatatSucceed = true;
        retryCount = 0;
        if (response.data == "success") {
          return true;
        } else {
          return false;
        }
      } else if (sendDatatSucceed == false && retryCount < 5) {
        retryFuture(checkVersion, "", 500);
        retryCount++;
      }
      throw new Exception("Api Error");
    } else {
      return false;
    }
  }

  Future<String> Request_key() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker().hasConnection;
    var jwt = '';
    if (connect) {
      var token = preferences.getString('token');
      String info = await GetDeviceInfo() + "-Accquire token";
      if (token == null) {
        jwt = await attempPost(info);
        preferences.setString('token', jwt);
      }
    }
    return jwt;
  }

  Future<String> attempPost(String info) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= "";
    final response = await dio.post(url,
        data: FormData.fromMap({"key": appkey, "token": token, "info": info}));

    if (response.statusCode == 200) {
      //print(response.body);
      return response.data;
    } else {
      throw new Exception("Api Error");
    }
  }

  retryFuture(future, data, delay) {
    Future.delayed(Duration(milliseconds: delay), () {
      future(data);
    });
  }

  Future<void> getListQuiz() async {
    print('getList');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      /* if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      } */
      if (Common.username != '') {
        preferences.setString('username', Common.username);
      }
      String user = preferences.getString('username') ?? '';
      print("getList----------------------------------2");
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {'user': user, 'exam': '0'};
      try {
        final response = await dio.post(url, data: FormData.fromMap({
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data),
        }));
        if (response.statusCode == 200) {
          preferences.setString('quizList', response.data.toString());
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        // if (loadRemoteDatatSucceed == false) retryFuture(getListQuiz, 200);
      }
    }
  }

  Future<void> getListQuizExam() async {
    print('getListExam');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      /* if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      } */
      if (Common.username != '') {
        preferences.setString('username', Common.username);
      }
      String user = preferences.getString('username') ?? '';
      print("getList----------------------------------2");
      String info = await GetDeviceInfo() + "-Quiz List Request";
      Map data = {'user': user, 'exam': '1'};
      try {
        final response = await dio.post(url, data: FormData.fromMap({
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data),
        }));
        if (response.statusCode == 200) {
          preferences.setString('quizList_exam', response.data.toString());
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        // if (loadRemoteDatatSucceed == false) retryFuture(getListQuiz, 200);
      }
    }
  }

  Future<void> getListResult() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user, 'exam': '0'};
      try {
        final response = await dio
            .post(apiUrl + "get_result_list_by_user", data: FormData.fromMap({
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        }));
        if (response.statusCode == 200) {
          preferences.setString('result', response.data);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        // if (loadRemoteDatatSucceed == false) retryFuture(getListResult, 200);
      }
    }
  }

  Future<void> getListResultExam() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user, 'exam': '1'};
      try {
        final response = await dio
            .post(apiUrl + "get_result_list_by_user", data: FormData.fromMap({
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        }));
        if (response.statusCode == 200) {
          preferences.setString('result_exam', response.data);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        // if (loadRemoteDatatSucceed == false) retryFuture(getListResult, 200);
      }
    }
  }

  Future<dynamic> sendErrorReport(data) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool sendDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect && kReleaseMode) {
      var token = preferences.getString('token');
      token ??= await Request_key();
      String info = await GetDeviceInfo();
      try {
        final response = await dio.post(ApiUrls().API_SEND_ERROR,
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": data,
              "appid": Common.appid
            }));
        if (response.statusCode == 200 && response.data != null) {
          sendDatatSucceed = true;
          retryCount = 0;
          if (response.data == "success") {
            return true;
          } else {
            return false;
          }
        } else if (sendDatatSucceed == false && retryCount < 5) {
          retryFuture(sendErrorReport, data, 500);
          retryCount++;
        }
      } catch (e) {
        print(e);
      }
    } else {
      return false;
    }
  }

  Future<void> getUserInforApi() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');

      String info = await GetDeviceInfo() + "-User Info Request";
      Map data = {'user': user};
      try {
        final response = await dio.post(apiUrl + "get_user_info",
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
            }));
        if (response.statusCode == 200) {
          preferences.setString('userInfo', response.data.toString());
          if (response.data == "") {
              Common.totalQuiz = "0";
              Common.average = "0";
              Common.recent = "0";
          } else {
            final parsed = jsonDecode(response.data);
              final totalQuiz = parsed['num_quiz'].toString();
              final average = parsed['average'].toString();
              final recent = parsed['recent'].toString();
              Common.totalQuiz = totalQuiz;
              Common.average = average;
              Common.recent = recent;
          }
        }
      } catch (e) {
      }
    }
  }
}
