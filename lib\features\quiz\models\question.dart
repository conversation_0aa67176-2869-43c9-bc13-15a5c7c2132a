import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/correctAnswer.dart';

class Question {
  final String? question;
  final String? id;
  final String? categoryId;
  final String? subcategoryId;
  final String? imageUrl;
  final String? level;
  final List? correctAnswer;
  final String? note;
  final String? languageId;
  final String submittedAnswerId;
  final String?
      questionType; //multiple option if type is 1, binary options type 2
  final List<AnswerOption>? answerOptions;
  final bool attempted;
  final String? audio;
  final String? audioType;
  final String? marks;
  List<Options>? options;
  final bool bookmark;
  // String? correct;
  // List<int>? correctIndex;

  Question({
    this.questionType,
    this.answerOptions,
    this.correctAnswer,
    this.id,
    this.languageId,
    this.level,
    this.note,
    this.question,
    this.categoryId,
    this.imageUrl,
    this.subcategoryId,
    this.audio,
    this.audioType,
    this.attempted = false,
    this.submittedAnswerId = "",
    this.marks,
    this.options,
    this.bookmark = false,
    // this.correct,
    // this.correctIndex
  });

  static Question fromJson(Map questionJson) {
    print(questionJson);
    //answer options is fix up to e and correct answer
    //identified this optionId (ex. a)
    List<String> optionIds = ["a", "b", "c", "d", "e", "f", "g", "h"];
    List<AnswerOption> options = [];

    //creating answerOption model
    optionIds.forEach((optionId) {
      String optionTitle = questionJson["option$optionId"] != null
          ? questionJson["option$optionId"].toString()
          : "";
      if (optionTitle.isNotEmpty) {
        options.add(AnswerOption(id: optionId, title: optionTitle));
      }
    });
    // options.shuffle();

    return Question(
      id: questionJson['id'],
      categoryId: questionJson['category'] ?? "",
      imageUrl: questionJson['image'],
      languageId: questionJson['language_id'],
      subcategoryId: questionJson['subcategory'] ?? "",
      correctAnswer: questionJson['correct_answer'] ?? "",
      level: questionJson['level'] ?? "",
      question: questionJson['question'],
      note: questionJson['note'] ?? "",
      questionType: questionJson['type'] ?? "",
      audio: questionJson['audio'] ?? "",
      audioType: questionJson['audio_type'] ?? "",
      marks: questionJson['marks'] ?? "",
      answerOptions: options,
      options: (questionJson["options"] as List)
          .map((e) => Options.fromJson(e))
          .toList(),
      // correct: questionJson['correct'] ?? "",
      // correctIndex: List<int>.from(questionJson["correct_index"])
    );
  }

  static Question fromBookmarkJson(Map questionJson) {
    //answer options is fix up to e and correct answer
    //identified this optionId (ex. a)
    List<String> optionIds = ["a", "b", "c", "d", "e", "f", "g", "h"];
    List<AnswerOption> options = [];

    //creating answerOption model
    optionIds.forEach((optionId) {
      String optionTitle = questionJson["option$optionId"].toString();
      final index = optionIds.indexWhere((element) => element == optionId);
      final resOptions = (questionJson["options"] as List)
          .map((e) => Options.fromJson(e))
          .toList();
      String oid = resOptions[index].oid;
      String score = resOptions[index].score ?? "";
      if (optionTitle.isNotEmpty || optionTitle != "null") {
        options.add(AnswerOption(
            id: optionId, title: optionTitle, oid: oid, score: score));
      }
    });
    // options.shuffle();

    return Question(
      id: questionJson['question_id'],
      categoryId: questionJson['category'] ?? "",
      imageUrl: questionJson['image'],
      languageId: questionJson['language_id'],
      subcategoryId: questionJson['subcategory'] ?? "",
      correctAnswer: questionJson['correct_answer'] ?? "",
      level: questionJson['level'] ?? "",
      question: questionJson['question'],
      note: questionJson['note'] ?? "",
      questionType: questionJson['type'] ?? "",
      audio: questionJson['audio'] ?? "",
      audioType: questionJson['audio_type'] ?? "",
      marks: questionJson['marks'] ?? "",
      answerOptions: options,
      options: (questionJson["options"] as List)
          .map((e) => Options.fromJson(e))
          .toList(),
      // correct: questionJson['correct'] ?? "",
      // correctIndex: List<int>.from(questionJson["correct_index"])
    );
  }

  Question updateQuestionWithAnswer({required String submittedAnswerId}) {
    String answerId = submittedAnswerId;
    if (this.questionType == "multi") {
      List answerIdList = [];
      answerIdList = this.submittedAnswerId.split(',');
      if (answerIdList.contains(submittedAnswerId)) {
        answerIdList.remove(submittedAnswerId);
      } else {
        answerIdList.add(submittedAnswerId);
      }
      answerIdList.removeWhere((element) => element == "");
      answerId = answerIdList.join(',');
    }

    return Question(
        marks: this.marks,
        submittedAnswerId: answerId,
        audio: this.audio,
        audioType: this.audioType,
        answerOptions: this.answerOptions,
        attempted: submittedAnswerId.isEmpty ? false : true,
        categoryId: this.categoryId,
        correctAnswer: this.correctAnswer,
        id: this.id,
        imageUrl: this.imageUrl,
        languageId: this.languageId,
        level: this.level,
        note: this.note,
        question: this.question,
        questionType: this.questionType,
        subcategoryId: this.subcategoryId,
        options: this.options,
        bookmark: this.bookmark
        // correct: this.correct,
        // correctIndex: this.correctIndex
        );
  }

  Question updateQuestionBookmark({required bool bookmark}) {
    return Question(
        marks: this.marks,
        submittedAnswerId: this.submittedAnswerId,
        audio: this.audio,
        audioType: this.audioType,
        answerOptions: this.answerOptions,
        attempted: this.attempted,
        categoryId: this.categoryId,
        correctAnswer: this.correctAnswer,
        id: this.id,
        imageUrl: this.imageUrl,
        languageId: this.languageId,
        level: this.level,
        note: this.note,
        question: this.question,
        questionType: this.questionType,
        subcategoryId: this.subcategoryId,
        options: this.options,
        bookmark: bookmark
        // correct: this.correct,
        // correctIndex: this.correctIndex
        );
  }

  Question copyWith({String? submittedAnswer, bool? attempted}) {
    return Question(
        marks: this.marks,
        submittedAnswerId: submittedAnswer ?? this.submittedAnswerId,
        answerOptions: this.answerOptions,
        audio: this.audio,
        audioType: this.audioType,
        attempted: attempted ?? this.attempted,
        categoryId: this.categoryId,
        correctAnswer: this.correctAnswer,
        id: this.id,
        imageUrl: this.imageUrl,
        languageId: this.languageId,
        level: this.level,
        note: this.note,
        question: this.question,
        questionType: this.questionType,
        subcategoryId: this.subcategoryId);
  }
}

class Options {
  late String oid;
  String? qid;
  late String qOption;
  dynamic? qOptionMatch;
  String? qOption1;
  String? score;
  String? qOptionMatch1;

  Options(
      {required this.oid,
      this.qid,
      required this.qOption,
      this.qOptionMatch,
      this.qOption1,
      this.score,
      this.qOptionMatch1});

  Options.fromJson(Map<String, dynamic> json) {
    if (json["oid"] is String) this.oid = json["oid"];
    if (json["qid"] is String) this.qid = json["qid"];
    if (json["q_option"] is String) this.qOption = json["q_option"];
    this.qOptionMatch = json["q_option_match"];
    if (json["q_option1"] is String) this.qOption1 = json["q_option1"];
    if (json["score"] is String) this.score = json["score"];
    if (json["q_option_match1"] is String)
      this.qOptionMatch1 = json["q_option_match1"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["oid"] = this.oid;
    data["qid"] = this.qid;
    data["q_option"] = this.qOption;
    data["q_option_match"] = this.qOptionMatch;
    data["q_option1"] = this.qOption1;
    data["score"] = this.score;
    data["q_option_match1"] = this.qOptionMatch1;
    return data;
  }
}
