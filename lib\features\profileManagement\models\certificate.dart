class Certificate {
  final String id;
  final String name;
  final bool isCurrent;

  Certificate({
    required this.id,
    required this.name,
    required this.isCurrent,
  });

  factory Certificate.fromJson(Map<String, dynamic> json) {
    return Certificate(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      isCurrent: json['is_current'] == '1' || json['is_current'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'is_current': isCurrent,
    };
  }

  Certificate copyWith({
    String? id,
    String? name,
    bool? isCurrent,
  }) {
    return Certificate(
      id: id ?? this.id,
      name: name ?? this.name,
      isCurrent: isCurrent ?? this.isCurrent,
    );
  }
}
