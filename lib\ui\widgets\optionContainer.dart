import 'package:assets_audio_player/assets_audio_player.dart';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/globals.dart';
// Import package
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class OptionContainer extends StatefulWidget {
  final Function hasSubmittedAnswerForCurrentQuestion;
  final Function submitAnswer;
  final AnswerOption answerOption;
  final BoxConstraints constraints;
  final String correctOptionId;
  final String submittedAnswerId;
  final bool showAudiencePoll;
  final int? audiencePollPercentage;
  final String? questionType;
  final bool showAnswerCorrectness;
  final QuizTypes quizType;
  final double? fontSize;
  OptionContainer(
      {Key? key,
      required this.quizType,
      required this.showAnswerCorrectness,
      required this.showAudiencePoll,
      required this.hasSubmittedAnswerForCurrentQuestion,
      required this.constraints,
      required this.answerOption,
      required this.correctOptionId,
      required this.submitAnswer,
      required this.submittedAnswerId,
      this.audiencePollPercentage,
      this.fontSize,
      this.questionType})
      : super(key: key);

  @override
  _OptionContainerState createState() => _OptionContainerState();
}

class _OptionContainerState extends State<OptionContainer>
    with TickerProviderStateMixin {
  late AnimationController animationController =
      AnimationController(vsync: this, duration: Duration(milliseconds: 90));
  late Animation<double> animation = Tween<double>(begin: 0.0, end: 1.0)
      .animate(CurvedAnimation(
          parent: animationController, curve: Curves.easeInQuad));

  late AnimationController topContainerAnimationController =
      AnimationController(vsync: this, duration: Duration(milliseconds: 180));
  late Animation<double> topContainerOpacityAnimation =
      Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
    parent: topContainerAnimationController,
    curve: Interval(0.0, 0.25, curve: Curves.easeInQuad),
  ));

  late Animation<double> topContainerAnimation =
      Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
          parent: topContainerAnimationController,
          curve: Interval(0.0, 0.5, curve: Curves.easeInQuad)));

  late Animation<double> answerCorrectnessAnimation =
      Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
          parent: topContainerAnimationController,
          curve: Interval(0.5, 1.0, curve: Curves.easeInQuad)));

  late double heightPercentage = 0.105;
  late AssetsAudioPlayer assetsAudioPlayer = AssetsAudioPlayer();

  late TextSpan textSpan = TextSpan(
      text: widget.answerOption.removeHtmlOption,
      style: TextStyle(
          color: Theme.of(context).colorScheme.surface,
          height: 1.0,
          fontSize: 16.0));

  @override
  void dispose() {
    animationController.dispose();
    topContainerAnimationController.dispose();
    assetsAudioPlayer.dispose();
    super.dispose();
  }

  void playSound(String trackName) async {
    if (context.read<SettingsCubit>().getSettings().sound) {
      if (assetsAudioPlayer.isPlaying.value) {
        await assetsAudioPlayer.stop();
      }
      await assetsAudioPlayer.open(Audio("$trackName"));
      await assetsAudioPlayer.play();
    }
  }

  void playVibrate() async {
    if (context.read<SettingsCubit>().getSettings().vibration) {
      UiUtils.vibrate();
    }
  }

  int calculateMaxLines() {
    TextPainter textPainter =
        TextPainter(text: textSpan, textDirection: Directionality.of(context));

    textPainter.layout(
      maxWidth: widget.constraints.maxWidth * (0.85),
    );

    return textPainter.computeLineMetrics().length;
  }

  Color _buildOptionBackgroundColor() {
    if (widget.showAnswerCorrectness) {
      return Theme.of(context).colorScheme.secondary;
    }
    if (widget.hasSubmittedAnswerForCurrentQuestion() &&
        widget.submittedAnswerId.split(',').contains(widget.answerOption.id)) {
      return Theme.of(context).primaryColor;
    }
    return Theme.of(context).colorScheme.secondary;
  }

  void _onTapOptionContainer() {
    if (widget.showAnswerCorrectness) {
      //if user has submitted the answer then do not show correctness of the answer
      if (!widget.hasSubmittedAnswerForCurrentQuestion()) {
        widget.submitAnswer(widget.answerOption.id);

        topContainerAnimationController.forward();

        //play sound
        if (widget.correctOptionId == widget.answerOption.id) {
          playSound(correctAnswerSoundTrack);
        } else {
          playSound(wrongAnswerSoundTrack);
        }
        playVibrate();
      }
    } else {
      var test = widget.answerOption.title;
      var test2 = test!.replaceAll("\n", " ");
      widget.submitAnswer(widget.answerOption.id);
      //play sound
      playSound(clickEventSoundTrack);
      playVibrate();
    }
  }

  Widget _buildOptionDetails(double optionWidth) {
    heightPercentage = 0.15;
    int maxLines = calculateMaxLines();

    heightPercentage = maxLines > 2
        ? (heightPercentage + (0.032 * (maxLines - 2)))
        : heightPercentage * 0.8;

    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: animation.drive(Tween<double>(begin: 1.0, end: 0.9)).value,
          child: child,
        );
      },
      child: Container(
        margin: EdgeInsets.only(top: widget.constraints.maxHeight * (0.015)),
        //height: widget.constraints.maxHeight * heightPercentage,
        width: optionWidth,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20.0),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.0, vertical: 0),
                color: _buildOptionBackgroundColor(),
                alignment: AlignmentDirectional.centerStart,
                child:
                    //if question type is 1 means render latex question
                    widget.quizType == QuizTypes.mathMania
                        ? TeXView(
                            child: TeXViewInkWell(
                                rippleEffect: false,
                                onTap: (_) {
                                  //
                                  _onTapOptionContainer();
                                },
                                child: TeXViewDocument(
                                  widget.answerOption.title!,
                                ),
                                id: widget.answerOption.id!),
                            style: TeXViewStyle(
                                contentColor:
                                    Theme.of(context).colorScheme.surface,
                                backgroundColor:
                                    Theme.of(context).colorScheme.secondary,
                                sizeUnit: TeXViewSizeUnit.pixels,
                                textAlign: TeXViewTextAlign.center,
                                fontStyle: TeXViewFontStyle(fontSize: 21)),
                          )
                        : Padding(
                            padding: const EdgeInsets.symmetric(vertical: 15.0),
                            child: Row(
                              children: [
                                widget.questionType == "single"
                                    ? (widget.hasSubmittedAnswerForCurrentQuestion() &&
                                            widget.submittedAnswerId
                                                .split(',')
                                                .contains(
                                                    widget.answerOption.id))
                                        ? Icon(
                                            Icons.radio_button_checked_rounded,
                                            color: Colors.white,
                                          )
                                        : Icon(
                                            Icons.radio_button_off_rounded,
                                            color: Colors.grey,
                                          )
                                    : (widget.hasSubmittedAnswerForCurrentQuestion() &&
                                            widget.submittedAnswerId
                                                .split(',')
                                                .contains(
                                                    widget.answerOption.id))
                                        ? Icon(
                                            Icons.check_box,
                                            color: Colors.white,
                                          )
                                        : Icon(
                                            Icons
                                                .check_box_outline_blank_rounded,
                                            color: Colors.grey,
                                          ),
                                SizedBox(
                                  width: 12,
                                ),
                                // Flexible(child: RichText(text: textSpan)),
                                Flexible(
                                    child: HtmlWidget(
                                  widget.answerOption.title?.replaceAll(
                                          '../../', Common.apiDomain) ??
                                      "",
                                  textStyle: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.surface,
                                      height: 1.0,
                                      fontSize: widget.fontSize ?? 16.0),
                                  onTapImage: (p0) {
                                    print(p0.sources.first.url);
                                    final imageProvider =
                                        Image.network(p0.sources.first.url)
                                            .image;
                                    showImageViewer(context, imageProvider,
                                        backgroundColor:
                                            Colors.black.withOpacity(0.8),
                                        useSafeArea: true,
                                        doubleTapZoomable: true);
                                  },
                                )),
                              ],
                            ),
                          ),
              ),
              widget.showAnswerCorrectness
                  ? IgnorePointer(
                      ignoring: true,
                      child: AnimatedBuilder(
                        builder: (context, child) {
                          final height = topContainerAnimation
                              .drive(Tween<double>(
                                  begin: 0.085, end: heightPercentage))
                              .value;
                          final width = topContainerAnimation
                              .drive(Tween<double>(begin: 0.2, end: 1.0))
                              .value;

                          final borderRadius = topContainerAnimation
                              .drive(Tween<double>(begin: 40.0, end: 20))
                              .value;

                          return Opacity(
                            opacity: topContainerOpacityAnimation.value,
                            child: Container(
                              alignment: Alignment.center,
                              child: Transform.scale(
                                scale: answerCorrectnessAnimation.value,
                                child: Opacity(
                                  opacity: answerCorrectnessAnimation.value,
                                  child: widget.answerOption.id ==
                                          widget.correctOptionId
                                      ? Icon(Icons.check,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .surface)
                                      : Icon(Icons.close,
                                          color: Theme.of(context)
                                              .colorScheme.surface),
                                ),
                              ),
                              decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius:
                                      BorderRadius.circular(borderRadius)),
                              width: optionWidth * width,
                              height: widget.constraints.maxHeight * height,
                            ),
                          );
                        },
                        animation: topContainerAnimationController,
                      ),
                    )
                  : Container()
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    textSpan = TextSpan(
        text: widget.answerOption.removeHtmlOption,
        style: TextStyle(
            color: Theme.of(context).colorScheme.surface,
            height: 1.0,
            fontSize: 16.0));
    return GestureDetector(
      onTapCancel: () {
        animationController.reverse();
      },
      onTap: () async {
        animationController.reverse();
        _onTapOptionContainer();
      },
      onTapDown: (_) {
        //
        animationController.forward();
      },
      child: widget.showAudiencePoll
          ? Row(
              children: [
                _buildOptionDetails(widget.constraints.maxWidth * (0.85)),
                SizedBox(
                  width: 4,
                ),
                Text(
                  "${widget.audiencePollPercentage}%",
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.secondary,
                      fontSize: 16.0),
                ),
              ],
            )
          : _buildOptionDetails(widget.constraints.maxWidth),
    );
  }
}
