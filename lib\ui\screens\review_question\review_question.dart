import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/ui/styles/payment/app_colors.dart';
import 'package:flutterquiz/ui/screens/review_question/review_content.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:http/http.dart' as http;
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/guessTheWordBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/models/guessTheWordQuestion.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/helper.dart';
import 'package:flutterquiz/ui/widgets/customBackButton.dart';

import 'package:flutterquiz/ui/widgets/customListTile.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/utils/answerEncryption.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';

import '../../styles/theme/themeCubit.dart';

class ReviewScreen extends StatefulWidget implements PreferredSizeWidget {
  final double height = 56.0;
  final Function(String)? onSearchTextChanged;
  bool isVisibleSearchBox;

  ReviewScreen(
      {Key? key, this.onSearchTextChanged, this.isVisibleSearchBox = false})
      : super(key: key);

  @override
  Size get preferredSize => Size.fromHeight(height);
  @override
  State<ReviewScreen> createState() => _BookmarkScreenState();
}

class _BookmarkScreenState extends State<ReviewScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<dynamic> filteredList = [];
  int _selectedTabIndex = 0;
  bool isFirstTime = true;
  int _currentSelectedTab = 1;
  List bookmark = [];
  List<dynamic> wrongquestions = [];
  List bookmarkString = [];
  static int numWrongQuestion = 0;
  static int numBookmarkedQuestion = 0;
  int _bottomSheetIndex = 0;
  bool showDel = false;
  var arrDel = [];
  Future<void> _removeItems() async {
    context.loaderOverlay.show();
    bookmark.removeWhere((element) {
      return arrDel.contains(element["qid"]);
    });
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('markedAnsApi', jsonEncode(bookmark));
    numBookmarkedQuestion = numBookmarkedQuestion - arrDel.length;
    await deleteBookmarkApi();
    filteredList = bookmark;
    arrDel = [];
    showDel = false;
    context.loaderOverlay.hide();
  }

  Future<void> changeUserStatistic() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var userInfo = preferences.getString('userInfo');
    var infoList = jsonDecode(userInfo!);
    int newCount = infoList["bookmark_count"] - arrDel.length;
    infoList["bookmark_count"] = newCount;
    preferences.setString('userInfo', jsonEncode(infoList));
  }

  Future<void> deleteBookmarkApi() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    String appkey = getUserInfor().appkey;
    String strDel = arrDel.join(',');
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }
      print(user.toString());
      String info = await GetDeviceInfo() + "-Delete bookmark questions";
      Map data = {'user': user, 'strDel': strDel};
      String jsonData = jsonEncode(data);
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "deleteBookmarkApi"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonData
        });
        if (response.statusCode == 200) {
          //inspect(response.body);
          await changeUserStatistic();
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        if (loadRemoteDatatSucceed == false)
          retryFuture(deleteBookmarkApi, 200);
      }
    }
  }

  retryFuture(future, delay) {
    Future.delayed(Duration(milliseconds: delay), () {
      future();
    });
  }

  Future<dynamic> getLocalData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String incorrectString = await prefs.getString('wrongAnsApi') ?? "";
    if (incorrectString != "") wrongquestions = jsonDecode(incorrectString);
    final String bookmarkString = await prefs.getString('markedAnsApi') ?? "";
    if (bookmarkString != "") bookmark = jsonDecode(bookmarkString);

    /* if (myAns != "") mySavedAns = jsonDecode(myAns); */
    if (filteredList.isEmpty) {
      if (isFirstTime) {
        filteredList = _selectedTabIndex == 0 ? wrongquestions : bookmark;
        isFirstTime = false;
        numBookmarkedQuestion = bookmark.length;
      }
      return false;
    }

    return true;
  }

  Future<dynamic> setNum() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String incorrectString = await prefs.getString('wrongAnsApi') ?? "";
    if (incorrectString != "") wrongquestions = jsonDecode(incorrectString);
    numWrongQuestion = wrongquestions.length;
    final String bookmarkString = await prefs.getString('markedAnsApi') ?? "";
    if (bookmarkString != "") bookmark = jsonDecode(bookmarkString);
    numBookmarkedQuestion = bookmark.length;
    /* if (myAns != "") mySavedAns = jsonDecode(myAns); */
    setState(() {});
  }

  String removeBrTags(String input) {
    // Loại bỏ tất cả các thẻ <br> trừ khi chúng đứng trước các từ dạng 1. 2. 3., 1) 2) 3), a) b) c), a. b. c.
    String output = input.replaceAllMapped(
      RegExp(r'<br>(?!(?:[^<]*?(?:\d+\.|\w\)|Option) ))'),
      (match) => '', // Return an empty string as the replacement
    );

    // Loại bỏ các ký tự xuống dòng và khoảng trắng thừa
    output = output.replaceAll('\n', '').trim();

    return output;
  }

  _fetchListQuestion() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String incorrectString =
        await prefs.getString('incorrectQuestion') ?? "";
    if (incorrectString != "")
      wrongquestions = jsonDecode(incorrectString);
    else
      wrongquestions = [];
    return wrongquestions;
  }

  void _handleTabSelection(int index) {
    setState(() {
      arrDel = [];
      showDel = false;
      _selectedTabIndex = index;
      if (index == 1) {
        filteredList = bookmark;
        numWrongQuestion = wrongquestions.length;
      } else {
        filteredList = wrongquestions;
        numBookmarkedQuestion = bookmark.length;
      }
      widget.isVisibleSearchBox = false;
    });
  }

  @override
  void initState() {
    super.initState();
    setNum();
  }

  void _onSearchTextChanged() {
    if (widget.onSearchTextChanged != null) {
      widget.onSearchTextChanged!(_searchController.text);
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchTextChanged);
    _searchController.dispose();
    super.dispose();
  }

  String searchText = '';

  void onChange(String searchText) {
    if (searchText.isEmpty) {
      setState(() {
        if (_selectedTabIndex == 0) {
          numWrongQuestion = filteredList.length;
        } else {
          numBookmarkedQuestion = filteredList.length;
        }
      });
      return;
    }

    filteredList = _selectedTabIndex == 0 ? wrongquestions : bookmark;

    List<Map<String, dynamic>> tempFilteredList = [];
    for (int i = 0; i < filteredList.length; i++) {
      String question = filteredList[i]['question'] ?? '';
      if (question.toLowerCase().contains(searchText.toLowerCase())) {
        tempFilteredList.add(filteredList[i]);
      }
    }

    setState(() {
      filteredList = [];
      filteredList = tempFilteredList;
      if (_selectedTabIndex == 0) {
        numWrongQuestion = filteredList.length;
      } else {
        numBookmarkedQuestion = filteredList.length;
      }
    });
  }
  // Bottom Sheet Cubit

  void openBottomSheetBookMarkCubit({
    required Map<String, dynamic> question,
  }) {
    showModalBottomSheet(
        isDismissible: true,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25.0),
          topRight: Radius.circular(25.0),
        )),
        backgroundColor: Theme.of(context).colorScheme.surface,
        isScrollControlled: true,
        context: context,
        builder: (_) {
          return ConstrainedBox(
            constraints: new BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height -
                  (MediaQuery.of(context).size.height *
                      (UiUtils.appBarHeightPercentage + 0.06)),
            ),
            //heightFactor: 1 - UiUtils.appBarHeightPercentage,
            child: ReviewContent(model: question, numberQuestion: 0),
          );
        });
  }

// Bottom Sheet Questions
  void openBottomSheet(
      {required List<dynamic> listQuestions, required int firstIndex}) {
    _bottomSheetIndex = firstIndex;
    showModalBottomSheet(
        isDismissible: true,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25.0),
          topRight: Radius.circular(25.0),
        )),
        backgroundColor: Theme.of(context).colorScheme.surface,
        isScrollControlled: true,
        context: context,
        builder: (_) {
          return ConstrainedBox(
            constraints: new BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height -
                  (MediaQuery.of(context).size.height *
                      (UiUtils.appBarHeightPercentage + 0.06)),
            ),
            child: Swiper(
              control: SwiperControl(
                iconNext: Icons.arrow_forward_ios,
                iconPrevious: Icons.arrow_back_ios,
                size: 20.0, // Arrow button size
                color: Theme.of(context).primaryColor, // Arrow button color
              ),
              index: firstIndex,
              itemBuilder: (BuildContext context, int index) {
                String rightAns = '';
                String myAnswers = '';
                String remainingAnswer = '';
                String myAnswerDisplay = '';
                var tets = listQuestions[index]['count'].toString();
                String qid = listQuestions[index]['qid'];
                if (tets == "null") {
                  wrongquestions.forEach((element) {
                    if (element['qid'] == qid) {
                      tets = element['count'].toString();
                    }
                  });
                }

                for (var i = 0;
                    i < listQuestions[index]['options'].length;
                    i++) {
                  if (double.parse(
                          listQuestions[index]['options'][i]['score']) >
                      0) {
                    rightAns += "\n- " +
                        Helper().parseHtmlString(
                            listQuestions[index]['options'][i]['q_option']);
                  }
                  if (listQuestions[index]['userAns'] != null &&
                      listQuestions[index]['userAns'].contains(
                          listQuestions[index]['options'][i]['oid'])) {
                    myAnswers += "\n- " +
                        Helper().parseHtmlString(
                            listQuestions[index]['options'][i]['q_option']);
                    myAnswerDisplay += Helper().parseHtmlString(
                            listQuestions[index]['options'][i]['q_option']) +
                        " - ";
                  }

                  if (listQuestions[index]['userAns'] != null &&
                      !listQuestions[index]['userAns'].contains(
                          listQuestions[index]['options'][i]['oid']) &&
                      double.parse(
                              listQuestions[index]['options'][i]['score']) ==
                          0) {
                    remainingAnswer += "\n- " +
                        Helper().parseHtmlString(
                            listQuestions[index]['options'][i]['q_option']);
                  }
                }
                QuizModel quizModel = QuizModel(
                    question: listQuestions[index]['question'] ?? "",
                    yourAnswer: myAnswers,
                    correctAnswer: rightAns,
                    remainingAnswer: remainingAnswer,
                    description: listQuestions[index]['description'] ?? "",
                    numWrong: tets);
                return ReviewContent(
                  model: listQuestions[index],
                  numberQuestion: index + 1,
                );
              },
              indicatorLayout: PageIndicatorLayout.COLOR,
              autoplay: false,
              itemCount: listQuestions.length,
            ),
            //heightFactor: 1 - UiUtils.appBarHeightPercentage,
          );
        });
  }

  Widget _buildTabContainer(String title, int index) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentSelectedTab = index;
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Text(
          title,
          style: TextStyle(
            color: Theme.of(context)
                .primaryColor
                .withOpacity(_currentSelectedTab == index ? 1.0 : 0.5),
            fontSize: 15.5,
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.only(bottom: 15),
      child: Stack(
        children: [
          Positioned.fill(
            top: 15,
            left: 25,
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: CustomBackButton(
                removeSnackBars: false,
                iconColor: Theme.of(context).primaryColor,
              ),
            ),
          ),
          Positioned.fill(
            top: 15,
            child: Align(
              alignment: Alignment.center,
              child: Text(
                  AppLocalization.of(context)!
                      .getTranslatedValues('reviewQuestionLbl')!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold)),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.bottomCenter,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTabContainer(
                    "${AppLocalization.of(context)!.getTranslatedValues("wrongQuestions")} (${numWrongQuestion})",
                    1),
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.01,
                ),
                _buildTabContainer(
                    "${AppLocalization.of(context)!.getTranslatedValues("markedQuestions")} (${numBookmarkedQuestion})",
                    2),
              ],
            ),
          ),
        ],
      ),
      height: MediaQuery.of(context).size.height *
          (UiUtils.appBarHeightPercentage + 0.04),
      decoration: BoxDecoration(
          boxShadow: [UiUtils.buildAppbarShadow()],
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.0),
              bottomRight: Radius.circular(20.0))),
    );
  }

  Widget _buildQuizZoneQuestions() {
    final bookmarkCubit = context.read<BookmarkCubit>();
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        margin: EdgeInsets.only(top: 15),
        child: FutureBuilder(
          future: getLocalData(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              if (numWrongQuestion == 0) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 150.0),
                  child: Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("noData"),
                        onTapRetry: () {
                          setState(() {});
                        },
                        showErrorImage: true),
                  ),
                );
              } else {
                return ListView.builder(
                  padding: const EdgeInsets.all(10.0),
                  itemCount: filteredList.length,
                  itemBuilder: _buildItem,
                  shrinkWrap: true,
                );
              }
            } else {
              return CircularProgressIndicator();
            }
          },
        ),

        /* FutureBuilder(
                future: _fetchListQuestion(),
                builder: (context, AsyncSnapshot snapshot) {
                  if (!snapshot.hasData) {
                    return Center(child: CircularProgressIndicator());
                  } else {
                    Container(
                        child: ListView.builder(
                            itemCount: _faouriteList.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (BuildContext context, int index) {
                              return Text('${_faouriteList[index].title}');
                            }));
                  }
                }), */
        //padding: EdgeInsets.only(top: MediaQuery.of(context).size.height),
      ),
    );
  }

  Widget _buildItem(BuildContext context, int index) {
    //Map prquestion = wrongquestions[index];
    return GestureDetector(
      onTap: () {
        var tets = wrongquestions[index]['count'].toString();
        openBottomSheet(listQuestions: filteredList, firstIndex: index);
      },
      child: CustomListTile(
        opacity: 1.0,
        trailingButtonOnTap: () {},
        subtitle:
            AppLocalization.of(context)!.getTranslatedValues("numWrong")! +
                ": " +
                filteredList[index]['count'].toString(),
        title: Helper().parseHtmlString(filteredList[index]['question'] ?? ""),
        leadingChild: Text(
          "${index + 1}",
          style: TextStyle(
            color: Theme.of(context).colorScheme.surface,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _bookMarked(BuildContext context, int index) {
    String rightAns = '';
    String myAnswers = '';
    String remainingAnswer = '';
    String myAnswerDisplay = '';

    for (var i = 0; i < filteredList[index]['options'].length; i++) {
      if (filteredList[index]['correct']
          .contains(filteredList[index]['options'][i]['oid'])) {
        rightAns += "\n- " +
            Helper()
                .parseHtmlString(filteredList[index]['options'][i]['q_option']);
      }

      filteredList.where((element) {
        String oid = element["oid"];
        if (!filteredList[index]['options'][i]['oid'].contains(oid) &&
            !filteredList[index]['options'][i]['oid'].contains(oid)) {
          remainingAnswer += "\n- " +
              Helper().parseHtmlString(
                  filteredList[index]['options'][i]['q_option']);
        }
        return true;
      });

      if (filteredList[index]['userAns'] != null &&
          filteredList[index]['userAns']
              .contains(filteredList[index]['options'][i]['oid'])) {
        myAnswers += "\n- " +
            Helper()
                .parseHtmlString(filteredList[index]['options'][i]['q_option']);
        myAnswerDisplay += Helper().parseHtmlString(
                filteredList[index]['options'][i]['q_option']) +
            " - ";
      }
    }
    //Map prquestion = wrongquestions[index];
    callbackFunction(test, test2) {
      setState(() {
        if (test == "add")
          arrDel.add(test2);
        else
          arrDel.remove(test2);
      });
      print(arrDel);
    }

    return GestureDetector(
      onTap: () {
        openBottomSheet(listQuestions: filteredList, firstIndex: index);
      },
      child: CustomListTile(
        callbackFunc: callbackFunction,
        qid: filteredList[index]['qid'].toString(),
        delete: showDel,
        checked: arrDel.contains(filteredList[index]['qid'].toString())
            ? true
            : false,
        opacity: 1.0,
        trailingButtonOnTap: () {},
        subtitle:
            AppLocalization.of(context)!.getTranslatedValues("yourAnsLbl")! +
                ": " +
                myAnswerDisplay,
        title: Helper().parseHtmlString(filteredList[index]['question']),
        leadingChild: Text(
          "${index + 1}",
          style: TextStyle(
            color: Theme.of(context).colorScheme.surface,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildAudioQuestions() {
    final bookmarkCubit = context.read<AudioQuestionBookmarkCubit>();
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        child: BlocBuilder<AudioQuestionBookmarkCubit,
                AudioQuestionBookMarkState>(
            bloc: bookmarkCubit,
            builder: (context, state) {
              if (state is AudioQuestionBookmarkFetchSuccess) {
                if (state.questions.isEmpty) {
                  return Center(
                    child: Text(
                      AppLocalization.of(context)!
                          .getTranslatedValues("noBookmarkQueLbl")!,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 20.0,
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  padding: EdgeInsetsDirectional.only(
                      top: 25.0,
                      start: MediaQuery.of(context).size.width * (0.075),
                      end: MediaQuery.of(context).size.width * (0.075),
                      bottom: 100),
                  itemBuilder: (context, index) {
                    Question question = state.questions[index];

                    //providing updateBookmarkCubit to every bookmarekd question
                    return BlocProvider<UpdateBookmarkCubit>(
                      create: (context) =>
                          UpdateBookmarkCubit(BookmarkRepository()),
                      //using builder so we can access the recently provided cubit
                      child: Builder(
                        builder: (context) => BlocConsumer<UpdateBookmarkCubit,
                            UpdateBookmarkState>(
                          bloc: context.read<UpdateBookmarkCubit>(),
                          listener: (context, state) {
                            if (state is UpdateBookmarkSuccess) {
                              bookmarkCubit.removeBookmarkQuestion(question.id,
                                  context.read<UserDetailsCubit>().getUserId());
                            }
                            if (state is UpdateBookmarkFailure) {
                              UiUtils.setSnackbar(
                                  AppLocalization.of(context)!
                                      .getTranslatedValues(
                                          convertErrorCodeToLanguageKey(
                                              updateBookmarkFailureCode))!,
                                  context,
                                  false);
                            }
                          },
                          builder: (context, state) {
                            return GestureDetector(
                              onTap: () {
                                /* openBottomSheetBookMarkCubit(
                                  question: question.question!,
                                  yourAnswer: bookmarkCubit
                                      .getSubmittedAnswerForQuestion(
                                          question.id),
                                  correctAnswer: question
                                      .answerOptions![question.answerOptions!
                                          .indexWhere((element) =>
                                              element.id ==
                                              AnswerEncryption
                                                  .decryptCorrectAnswer(
                                                rawKey: context
                                                    .read<UserDetailsCubit>()
                                                    .getUserFirebaseId(),
                                                correctAnswer:
                                                    question.correctAnswer!,
                                              ))]
                                      .title!,
                                ); */
                              },
                              child: CustomListTile(
                                opacity: state is UpdateBookmarkInProgress
                                    ? 0.5
                                    : 1.0,
                                trailingButtonOnTap: state
                                        is UpdateBookmarkInProgress
                                    ? () {}
                                    : () {
                                        context
                                            .read<UpdateBookmarkCubit>()
                                            .updateBookmark(
                                                context
                                                    .read<UserDetailsCubit>()
                                                    .getUserId(),
                                                question.id!,
                                                "0",
                                                "4"); // type is 4 for audio questions
                                      },
                                subtitle: AppLocalization.of(context)!
                                        .getTranslatedValues("yourAnsLbl")! +
                                    ":" +
                                    " ${bookmarkCubit.getSubmittedAnswerForQuestion(question.id)}",
                                title: question.question,
                                leadingChild: Text(
                                  "${index + 1}",
                                  style: TextStyle(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    );
                  },
                  itemCount: state.questions.length,
                );
              }
              if (state is AudioQuestionBookmarkFetchFailure) {
                return ErrorContainer(
                  errorMessage: AppLocalization.of(context)!
                      .getTranslatedValues(convertErrorCodeToLanguageKey(
                          state.errorMessageCode)),
                  showErrorImage: true,
                  errorMessageColor: Theme.of(context).primaryColor,
                  onTapRetry: () {
                    context.read<AudioQuestionBookmarkCubit>().getBookmark(
                        context.read<UserDetailsCubit>().getUserId());
                  },
                );
              }
              return Center(
                child: CircularProgressIndicator(),
              );
            }),
        /* padding:
            EdgeInsets.only(top: MediaQuery.of(context).size.height * (0.16)), */
      ),
    );
  }

  Widget _buildGuessTheWordQuestions() {
    final bookmarkCubit = context.read<GuessTheWordBookmarkCubit>();
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        margin: EdgeInsets.only(top: 15),
        child: FutureBuilder(
          future: getLocalData(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              if (numBookmarkedQuestion == 0) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 150.0),
                  child: Center(
                    child: ErrorContainer(
                        errorMessageColor: Theme.of(context).primaryColor,
                        errorMessage: AppLocalization.of(context)!
                            .getTranslatedValues("noData"),
                        onTapRetry: () {
                          setState(() {});
                        },
                        showErrorImage: true),
                  ),
                );
              } else {
                return ListView.builder(
                  padding: const EdgeInsets.all(10.0),
                  itemCount: filteredList.length,
                  itemBuilder: _bookMarked,
                  shrinkWrap: true,
                );
              }
            } else {
              return CircularProgressIndicator();
            }
          },
        ),
        /* padding:
            EdgeInsets.only(top: MediaQuery.of(context).size.height * (0.16)), */
      ),
    );
  }

  Widget _buildPlayButton() {
    if (_currentSelectedTab == 1) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: EdgeInsets.only(bottom: 25.0),
          child: BlocBuilder<BookmarkCubit, BookmarkState>(
            builder: (context, state) {
              if (state is BookmarkFetchSuccess && state.questions.isNotEmpty) {
                return CustomRoundedButton(
                  widthPercentage: 0.85,
                  backgroundColor: Theme.of(context).primaryColor,
                  buttonTitle: AppLocalization.of(context)!
                      .getTranslatedValues("playBookmarkBtn")!,
                  radius: 5.0,
                  showBorder: false,
                  fontWeight: FontWeight.w500,
                  height: 50.0,
                  titleColor: Theme.of(context).colorScheme.surface,
                  onTap: () {
                    Navigator.of(context).pushNamed(
                      Routes.bookmarkQuiz,
                      arguments: QuizTypes.quizZone,
                    );
                  },
                  elevation: 6.5,
                  textSize: 17.0,
                );
              }
              return Container();
            },
          ),
        ),
      );
    }
    if (_currentSelectedTab == 2) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: EdgeInsets.only(bottom: 25.0),
          child:
              BlocBuilder<GuessTheWordBookmarkCubit, GuessTheWordBookmarkState>(
            builder: (context, state) {
              if (state is GuessTheWordBookmarkFetchSuccess &&
                  state.questions.isNotEmpty) {
                return CustomRoundedButton(
                  widthPercentage: 0.85,
                  backgroundColor: Theme.of(context).primaryColor,
                  buttonTitle: AppLocalization.of(context)!
                      .getTranslatedValues("playBookmarkBtn")!,
                  radius: 5.0,
                  showBorder: false,
                  fontWeight: FontWeight.w500,
                  height: 50.0,
                  titleColor: Theme.of(context).colorScheme.surface,
                  onTap: () {
                    Navigator.of(context).pushNamed(
                      Routes.bookmarkQuiz,
                      arguments: QuizTypes.guessTheWord,
                    );
                  },
                  elevation: 6.5,
                  textSize: 17.0,
                );
              }
              return Container();
            },
          ),
        ),
      );
    }
    return Align(
      alignment: Alignment.bottomCenter,
      child: Padding(
        padding: EdgeInsets.only(bottom: 25.0),
        child:
            BlocBuilder<AudioQuestionBookmarkCubit, AudioQuestionBookMarkState>(
          builder: (context, state) {
            if (state is AudioQuestionBookmarkFetchSuccess &&
                state.questions.isNotEmpty) {
              return CustomRoundedButton(
                widthPercentage: 0.85,
                backgroundColor: Theme.of(context).primaryColor,
                buttonTitle: AppLocalization.of(context)!
                    .getTranslatedValues("playBookmarkBtn")!,
                radius: 5.0,
                showBorder: false,
                fontWeight: FontWeight.w500,
                height: 50.0,
                titleColor: Theme.of(context).colorScheme.surface,
                onTap: () {
                  Navigator.of(context).pushNamed(
                    Routes.bookmarkQuiz,
                    arguments: QuizTypes.audioQuestions,
                  );
                },
                elevation: 6.5,
                textSize: 17.0,
              );
            }
            return Container();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      child: DefaultTabController(
          length: 2,
          child: Builder(builder: (BuildContext context) {
            return Scaffold(
                /* body: Stack(
                children: [
                  PageBackgroundGradientContainer(),
                  Align(
                    alignment: Alignment.topCenter,
                    child: _currentSelectedTab == 1
                        ? _buildQuizZoneQuestions()
                        : _currentSelectedTab == 2
                            ? _buildGuessTheWordQuestions()
                            : _buildAudioQuestions(),
                  ),
                  _buildPlayButton(),
                  Align(
                    alignment: Alignment.topCenter,
                    child: _buildAppBar(),
                  ),
                ], */
                appBar: AppBar(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    leading: CustomBackButton(
                      iconColor: Theme.of(context).primaryColor,
                    ),
                    title: widget.isVisibleSearchBox
                        ? buildSearchField(context)
                        : Padding(
                            padding: EdgeInsets.only(top: 5),
                            child: Text(
                              AppLocalization.of(context)!
                                  .getTranslatedValues("reviewQuestionLbl")!,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 22.0),
                            ),
                          ),
                    centerTitle: true,
                    actions: [
                      _selectedTabIndex == 1
                          ? GestureDetector(
                              onTap: () {
                                setState(() {
                                  if (showDel)
                                    showDel = false;
                                  else
                                    showDel = true;
                                });
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: Icon(
                                  showDel == false
                                      ? Icons.delete_outline
                                      : Icons.delete,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            )
                          : Container(),
                      if (Common.inPremiumGroup)
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              widget.isVisibleSearchBox =
                                  !widget.isVisibleSearchBox;
                              numWrongQuestion = wrongquestions.length;
                              numBookmarkedQuestion = bookmark.length;
                              filteredList = _selectedTabIndex == 0
                                  ? wrongquestions
                                  : bookmark;
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(right: 10),
                            child: Icon(
                              widget.isVisibleSearchBox
                                  ? Icons.close
                                  : Icons.search,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        )
                    ],
                    bottom: TabBar(
                        labelPadding: EdgeInsetsDirectional.only(
                            top: MediaQuery.of(context).size.height * .0),
                        labelColor: Theme.of(context).primaryColor,
                        unselectedLabelColor: Theme.of(context)
                            .colorScheme
                            .secondary
                            .withOpacity(0.7),
                        labelStyle: Theme.of(context).textTheme.titleMedium,
                        indicatorColor: Theme.of(context).primaryColor,
                        indicatorSize: TabBarIndicatorSize.tab,
                        indicatorWeight: 5,
                        onTap: _handleTabSelection,
                        tabs: [
                          Tab(
                              text:
                                  "${AppLocalization.of(context)!.getTranslatedValues("wrongQuestions")} (${numWrongQuestion})"),
                          Tab(
                              text:
                                  "${AppLocalization.of(context)!.getTranslatedValues("markedQuestions")} (${numBookmarkedQuestion})"),
                        ])),
                body: Stack(children: [
                  PageBackgroundGradientContainer(),
                  Padding(
                    padding: showDel
                        ? const EdgeInsets.only(bottom: 60.0)
                        : EdgeInsets.all(0),
                    child: TabBarView(
                        physics: NeverScrollableScrollPhysics(),
                        children: [
                          _buildQuizZoneQuestions(),
                          _buildGuessTheWordQuestions(),
                        ]),
                  ),
                  showDel
                      ? Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            height: 80,
                            color: Colors.transparent,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 10),
                              child: ElevatedButton(
                                onPressed: () async {
                                  if (arrDel.length > 0) {
                                    await _removeItems();
                                    setState(() {
                                      // Thực hiện các thay đổi liên quan đến state
                                    });
                                  }
                                },
                                style: ButtonStyle(
                                  backgroundColor:
                                      MaterialStateProperty.all<Color>(
                                          arrDel.length > 0
                                              ? Theme.of(context).primaryColor
                                              : Colors.grey),
                                  overlayColor: arrDel.length == 0
                                      ? MaterialStateProperty.all<Color>(
                                          Colors.transparent)
                                      : null,
                                ),
                                child: Text(
                                  AppLocalization.of(context)!
                                      .getTranslatedValues("delete")!,
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 22.0),
                                ),
                              ),
                            ),
                          ),
                        )
                      : Container(),
                ]));
          })),
    );
  }

  Widget buildSearchField(BuildContext context) {
    AppTheme appTheme = context.read<ThemeCubit>().state.appTheme;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: TextField(
        autofocus: true,
        style: TextStyle(
            color: appTheme == AppTheme.Light ? Colors.black : Colors.white),
        decoration: InputDecoration(
          hintText:
              "${AppLocalization.of(context)!.getTranslatedValues("hintSearchInput")}",
          border: InputBorder.none,
        ),
        onChanged: onChange,
      ),
    );
  }
}

class MySeparator extends StatelessWidget {
  const MySeparator({Key? key, this.height = 1, this.color = Colors.black})
      : super(key: key);
  final double height;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        const dashWidth = 5.0;
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Flex(
            children: List.generate(dashCount, (_) {
              return SizedBox(
                width: dashWidth,
                height: dashHeight,
                child: DecoratedBox(
                  decoration: BoxDecoration(color: color),
                ),
              );
            }),
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            direction: Axis.horizontal,
          ),
        );
      },
    );
  }
}
