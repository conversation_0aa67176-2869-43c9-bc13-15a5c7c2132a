import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_countdown_timer/countdown_timer_controller.dart';
import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';

import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/ui/screens/exam/examsScreen.dart';
import 'package:flutterquiz/ui/screens/exam/helper.dart';
import 'package:flutterquiz/ui/screens/home/<USER>';
import 'package:flutterquiz/ui/screens/model/question_api_model.dart';
import 'package:flutterquiz/ui/screens/model/result2_model.dart';
import 'package:flutterquiz/ui/styles/app_colors.dart';
import 'package:flutterquiz/ui/styles/app_styles.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/ui/widgets/error_dialog.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:loading_overlay/loading_overlay.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';

import 'package:http/http.dart' as http;

class ExamSimulator extends StatefulWidget {
  final String idQuiz;
  final String quizName;
  final int passPercent;
  final int duration; // minutes
  final bool resume;
  const ExamSimulator(
      {Key? key,
      required this.idQuiz,
      required this.quizName,
      required this.passPercent,
      required this.duration,
      this.resume = false})
      : super(key: key);

  @override
  _ExamState createState() => _ExamState();
}

class _ExamState extends State<ExamSimulator> with WidgetsBindingObserver {
  int _currentIndex = 0;
  var _answers = Map<int, dynamic>();
  var _answersText = Map<int, dynamic>();
  var _bookmarks = Map<int, dynamic>();
  bool _saving = false;
  String rid = "";
  List<int> bookmarksIndex = [];
  List<Question> question = [];
  late ScrollController _scrollControllerOptions;
  late ScrollController _scrollControllerQuestion;
  var _multiSelect = Map<int, dynamic>();
  late Future<String> futureLocalData;
  var bookmarkList;
  int remainTime = 0; // seconds
  int timeDoQuiz = 0; // seconds
  double valueProgress = 0;
  Timer? _timer;
  bool internet = false;
  Map _answersId = {};
  String endQuizTime = '';
  final List alphabet = [
    'A. ',
    'B. ',
    'C. ',
    'D. ',
    'E. ',
    'F. ',
    'G. ',
    'H. ',
    'I.',
    'K. '
  ];
  List<Question> incorrectQuestion = [];
  List<Question> bookmarkQuestion = [];
  int endTime = 0;
  int startTime = 0;
  late CountdownTimerController controller;

  void _initMultiSelect() {
    var indexQuestion = 0;
    question.forEach((element) {
      if (element.type == "multi") {
        var temp = [];
        for (var index = 0; index < element.options!.length; index++) {
          temp.add(false);
        }
        _answers[indexQuestion] = temp;
        _answersText[indexQuestion] = [];
        _answersId[indexQuestion] = [];
      } else {
        _answers[indexQuestion] = "";
        _answersText[indexQuestion] = "";
        _answersId[indexQuestion] = [];
      }
      indexQuestion++;
    });
  }

  void _saveResult(String name, status, percentage, time, correctList) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Fetch and decode data
    final String resultString = await prefs.getString('result') ?? "";
    Map data = {
      'name': name,
      'status': status,
      'percentage': percentage,
      'quizId': widget.idQuiz,
      'score_individual': correctList
    };
    await _sendResult(data);
    await ApiServices().getUserInforApi();
    //print(user_list);
    //print(list);
    /* if (resultString != "") {
      var a = Result2.decode(resultString);
      a.add(Result2(
          name: name,
          status: status,
          percentage: percentage,
          time: time,
          questions: question,
          answers: _answers.values.toList(),
          answersText: _answersText.values.toList(),
          passPercent: widget.passPercent,
          quizId: widget.idQuiz,
          bookmark: _bookmarks.values.toList(),
          quizDuration: widget.duration,
          timeDoQuiz: timeDoQuiz));
      await prefs.setString('result', Result2.encode(a));
    } else {
      final String encodedData = Result2.encode([
        Result2(
            name: name,
            status: status,
            percentage: percentage,
            time: time,
            questions: question,
            answers: _answers.values.toList(),
            answersText: _answersText.values.toList(),
            passPercent: widget.passPercent,
            quizId: widget.idQuiz,
            bookmark: _bookmarks.values.toList(),
            quizDuration: widget.duration,
            timeDoQuiz: timeDoQuiz)
      ]);
      await prefs.setString('result', encodedData);
    } */
  }

  Future<void> _sendResult(data) async {
    List scoreIndividual = [];
    List questionId = [];
    var _correctList = data['score_individual'].values.toList();
    int correct = 0;
    _correctList.forEach((element) {
      if (element == true) {
        scoreIndividual.add(1);
        correct++;
      } else {
        scoreIndividual.add(2);
      }
    });
    question.forEach((element) {
      questionId.add(element.id);
    });
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var user = preferences.getString('username');
    data['username'] = user;
    data['appid'] = Common.appid;
    data['score_individual'] = scoreIndividual.join(',');
    data['start_time'] = startTime;
    data['end_time'] = (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
    data['r_qids'] = questionId;
    data['score_obtained'] = correct;
    data['total_time'] = timeDoQuiz + 1;
    data['answers_id'] = _answersId.values.toList();
    data['app_username'] = Common.username;
    data['bookmark_index'] = bookmarksIndex.join(",");
    var sendResult = await ApiServices().sendResult(jsonEncode(data));
    if (sendResult[0] == false) {
      _saveDataToSendLater(jsonEncode(data));
    }
    getListResult();
    getUserInforApi();
    rid = sendResult[1];
    setState(() {
      _saving = false;
    });
    Navigator.of(context).pushNamed(Routes.resultSummary, arguments: {
      "rid": rid,
      "quid": widget.idQuiz.toString(),
      "quiz_name": widget.quizName.toString(),
      "pass_percentage": widget.passPercent.toString(),
      "duration": widget.duration.toString(),
      "backButtonStatus": false,
      "examTheme": ExamTheme.Scrum
    });
  }

  Future<void> getListWrongAns() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getWrongAns----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_wrong_answered"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('wrongAnsApi', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListWrongAns, 200);
      }
    }
  }

  Future<void> getListMarkedAns() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getMarkedAns----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await http
            .post(Uri.parse(apiUrl + "get_marked_question"), body: {
          "key": appkey,
          "token": token,
          "info": info,
          "data": jsonEncode(data)
        });
        if (response.statusCode == 200) {
          preferences.setString('markedAnsApi', response.body);
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListMarkedAns, 200);
      }
    }
  }

  Future<void> getListResult() async {
    print('getResult');
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      if (user == null || user == "") {
        context
            .read<UserDetailsCubit>()
            .setUserDetails(context.read<AuthCubit>().getUserFirebaseId());
        user = preferences.getString('username');
      }

      print("getResult----------------------------------3");
      print(user.toString());
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await dio.post(apiUrl + "get_result_list_by_user",
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data)
            }));
        if (response.statusCode == 200) {
          preferences.setString('result', response.data.toString());
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getListResult, 200);
      }
    }
  }

  Future<void> getUserInforApi() async {
    //Common.appid = packageInfo.packageName;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool loadRemoteDatatSucceed = false;
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) {
      var token = preferences.getString('token');
      var user = preferences.getString('username');
      String totalQuiz = '0';
      String average = '0';
      String recent = '0';
      String info = await GetDeviceInfo() + "-Quiz Result Request";
      Map data = {'user': user};
      try {
        final response = await dio.post(apiUrl + "get_user_info",
            data: FormData.fromMap({
              "key": appkey,
              "token": token,
              "info": info,
              "data": jsonEncode(data),
            }));
        if (response.statusCode == 200) {
          preferences.setString('userInfo', response.data.toString());
          if (response.data == "") {
            totalQuiz = "0";
            average = "0";
            recent = "0";
          } else {
            final parsed = jsonDecode(response.data);
            totalQuiz = parsed['num_quiz'].toString();
            average = parsed['average'].toString();
            recent = parsed['recent'].toString();
            Common.totalQuiz = totalQuiz;
            Common.average = average;
            Common.recent = recent;
          }
          loadRemoteDatatSucceed = true;
        }
      } catch (e) {
        // attempPost(info);
        if (loadRemoteDatatSucceed == false) retryFuture(getUserInforApi, 200);
      }
    }
  }

  Future<String> attempPost(String info) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var token = preferences.getString('token');
    token ??= "";
    final response = await dio.post(getUserInfor().url,
        data: FormData.fromMap({"key": appkey, "token": token, "info": info}));

    if (response.statusCode == 200) {
      preferences.setString('token', response.data.toString());
      return response.data;
    } else {
      //status = false;
      return "";
    }
  }

  retryFuture(future, delay) {
    Future.delayed(Duration(milliseconds: delay), () {
      future();
    });
  }

  void _saveDataToSendLater(data) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> sendLater = prefs.getStringList('sendLater') ?? [];
    sendLater.add(data);
    prefs.setStringList('sendLater', sendLater);
    print('save data to send later');
  }

  void _handleNext() {
    if (_bookmarks[_currentIndex] == null) {
      _bookmarks[_currentIndex] = 0;
    }
    if (_answers[_currentIndex] == '') {
      _showAlertDialog();
      return;
    } else if (question[_currentIndex].type == 'multi') {
      if (_answers[_currentIndex].contains(true) == false) {
        _showAlertDialog();
        return;
      }
    }
    if (_currentIndex < (question.length - 1)) {
      setState(() {
        _saveLocal();
        print('next');
        _currentIndex++;
      });
    } else {
      _showConfirmDialog();
    }
    _scrollControllerOptions
        .jumpTo(_scrollControllerOptions.position.minScrollExtent);
    _scrollControllerQuestion
        .jumpTo(_scrollControllerQuestion.position.minScrollExtent);
  }

  void _endQuiz() {
    int correct = 0;
    double percent = 0;
    var correctQuestion;
    late Map correctList = {};
    _answers.forEach((index, value) {
      var idData = jsonEncode(_answersId.values.toList());
      question[index].userAns = idData;
      if (question[index].type == "single") {
        correctQuestion = question[index].correct;
        if (correctQuestion == value) {
          correctList[index] = true;
          correct++;
        } else {
          correctList[index] = false;
          question[index].quizId = widget.idQuiz;
          incorrectQuestion.add(question[index]);
        }
      } else if (question[index].type == "multi") {
        correctList[index] = true;
        var correctIndex = question[index].correctIndex;
        for (int i = 0; i < correctIndex!.length; i++) {
          if (_answers[index][correctIndex[i]] == false) {
            correctList[index] = false;
            question[index].quizId = widget.idQuiz;
            incorrectQuestion.add(question[index]);
            break;
          }
        }
        if (correctList[index] == true) correct++;
      }
      if (_bookmarks[index] == 1) {
        question[index].quizId = widget.idQuiz;
        bookmarkQuestion.add(question[index]);
        bookmarksIndex.add(index);
      }
    });
    saveBookmarkQuestion();
    saveIncorrectQuestion();
    getListWrongAns();
    getListMarkedAns();
    percent = (correct / question.length) * 100;
    String status = percent >= widget.passPercent ? "Pass" : "Fail";
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('dd/MM/yyyy').format(now);
    endQuizTime = formattedDate;
    print(question);
    _saveResult(widget.quizName, status, percent.toString(), formattedDate,
        correctList);
    _clearLocal();
  }

  saveIncorrectQuestion() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final localIncorrectString =
        await prefs.getString('incorrectQuestion') ?? "";
    final localIncorrect =
        localIncorrectString != "" ? jsonDecode(localIncorrectString) : [];
    List newListQuestion = [...localIncorrect, ...incorrectQuestion];
    prefs.setString('incorrectQuestion', jsonEncode(newListQuestion));
  }

  saveBookmarkQuestion() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final localBookmarkString = await prefs.getString('bookmarkQuestion') ?? "";
    final localBookmark =
        localBookmarkString != "" ? jsonDecode(localBookmarkString) : [];
    List newListBookmark = [...localBookmark, ...bookmarkQuestion];
    prefs.setString('bookmarkQuestion', jsonEncode(newListBookmark));
  }

  void _handleBack() {
    setState(() {
      print('back');
      _currentIndex--;
    });
  }

  void _showAlertDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.white,
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                  "${AppLocalization.of(context)!.getTranslatedValues("mustSelectAnswer")}",
                  style: AppStyles.dialogText,
                  textAlign: TextAlign.center,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: Container(
                    child: Row(
                      children: [
                        Expanded(
                            child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            "OK",
                            style: AppStyles.primaryButton,
                          ),
                          style: ElevatedButton.styleFrom(
                            foregroundColor: AppColors.white,
                            backgroundColor: AppColors.greenPrimary,
                            shadowColor: Color.fromARGB(92, 0, 166, 144),
                            elevation: 0,
                            minimumSize: Size(20, 44),
                            side: BorderSide(
                                color: AppColors.greenPrimary,
                                width: 1.0,
                                style: BorderStyle.solid),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.0)),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showConfirmDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.white,
        // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
        insetPadding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
            child: Column(
              children: [
                Container(
                  width: 196,
                  height: 139,
                  decoration: BoxDecoration(
                      image: DecorationImage(
                    image: AssetImage("assets/images/info_dialog.png"),
                    fit: BoxFit.fill,
                  )),
                ),
                Text(
                    "${AppLocalization.of(context)!.getTranslatedValues("submitTestConfirmation")}",
                    style: AppStyles.dialogText,
                    textAlign: TextAlign.center),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: Container(
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(right: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text(
                              "${AppLocalization.of(context)!.getTranslatedValues("noBtn")}",
                              style: AppStyles.secondaryButton,
                              textAlign: TextAlign.center,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.greenPrimary,
                              backgroundColor: Colors
                                  .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                              shadowColor: Colors
                                  .white, //specify the button's elevation color
                              elevation: 0, //buttons Material shadow
                              // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                              minimumSize: Size(20,
                                  44), //specify the button's first: width and second: height
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle
                                      .solid), //set border for the button
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        )),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(left: 6),
                          child: ElevatedButton(
                            onPressed: () {
                              controller.disposeTimer();
                              _timer?.cancel();
                              _timer = null;
                              _endQuiz();
                              Navigator.of(context).pop();
                              setState(() {
                                _saving = true;
                              });
                              /* Navigator.of(context).pushNamed(
                                  Routes.resultSummary,
                                  arguments: {"rid": rid}); */
                              /* Navigator.of(context)
                                  .pushReplacement(MaterialPageRoute(
                                builder: (context) => NewExamResult(
                                  answers: _answers,
                                  questions: question,
                                  answersText: _answersText,
                                  quizId: widget.idQuiz,
                                  quizName: widget.quizName,
                                  bookmark: _bookmarks,
                                  passPercent: widget.passPercent,
                                  time: endQuizTime,
                                  quizDuration: widget.duration,
                                  timeDoQuiz: timeDoQuiz,
                                ),
                              )); */
                              /* Navigator.of(context).pushReplacement(
                                  MaterialPageRoute(
                                      builder: (context) => HomeScreen())) */
                            },
                            child: Text(
                              "${AppLocalization.of(context)!.getTranslatedValues("yesBtn")}",
                              style: AppStyles.primaryButton,
                            ),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: AppColors.white,
                              backgroundColor: AppColors.greenPrimary,
                              shadowColor: Color.fromARGB(92, 0, 166, 144),
                              elevation: 0,
                              minimumSize: Size(20, 44),
                              side: BorderSide(
                                  color: AppColors.greenPrimary,
                                  width: 1.0,
                                  style: BorderStyle.solid),
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0)),
                            ),
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _bookmark() {
    if (_bookmarks[_currentIndex] == 1) {
      _bookmarks[_currentIndex] = 0;
    } else {
      _bookmarks[_currentIndex] = 1;
    }
    //print(_bookmarks);
  }

  Future<String> getContinue() async {
    String question = '';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (prefs.getString('question') != null) {
      question = prefs.getString('question')!;
    }
    return question;
  }

  Future<String> _getLocal() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var answerList;
    var answerTextList;
    var status;
    if (!widget.resume) {
      question = await ApiServices().fetchQuestion(widget.idQuiz);
      // prefs.setString('question', jsonEncode(question));
    } else {
      String questionString = prefs.getString('questionResume') ?? '';
      const JsonDecoder _decoder = JsonDecoder();
      final useListContainer = _decoder.convert(questionString);
      final List questionList = useListContainer;
      question = questionList.map((e) => Question.fromJson(e)).toList();
    }
    _initMultiSelect();
    if (prefs.getString('answered_' + widget.idQuiz) != null) {
      answerList =
          jsonDecode(prefs.getString('answered_' + widget.idQuiz) ?? '');
      answerTextList =
          jsonDecode(prefs.getString('answeredText_' + widget.idQuiz) ?? '');
    }
    if (prefs.getString('bookmarked_' + widget.idQuiz) != null) {
      bookmarkList =
          jsonDecode(prefs.getString('bookmarked_' + widget.idQuiz) ?? '');
    }
    if (prefs.getInt('timeDoQuiz_' + widget.idQuiz) != null) {
      timeDoQuiz = prefs.getInt('timeDoQuiz_' + widget.idQuiz) ?? 0;
      remainTime = widget.duration * 60 - timeDoQuiz;
    } else {
      remainTime = widget.duration * 60;
    }
    valueProgress = (1 / (widget.duration * 60)) * timeDoQuiz;
    if (answerList != null) {
      for (var index = 0; index < answerList.length; index++) {
        _answers[index] = answerList[index];
        _answersText[index] = answerTextList[index];
      }
    }
    if (bookmarkList != null) {
      for (var index = 0; index < bookmarkList.length; index++) {
        _bookmarks[index] = bookmarkList[index];
      }
    }
    if (prefs.getInt('timeStartQuiz_' + widget.idQuiz) != null) {
      startTime = prefs.getInt('timeStartQuiz_' + widget.idQuiz) ?? 0;
      print(startTime);
    } else {
      startTime = (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
      prefs.setInt('timeStartQuiz_' + widget.idQuiz, startTime);
      print('save time start quiz ${startTime}');
    }
    if (prefs.getString('answeredId_' + widget.idQuiz) != null) {
      var listAnswersId =
          jsonDecode(prefs.getString('answeredId_' + widget.idQuiz) ?? '');
      for (int i = 0; i < listAnswersId.length; i++) {
        _answersId[i] = listAnswersId[i];
      }
    }
    print(_answers);
    print(_bookmarks);
    print('getlocal');
    status = 'done';
    return status;
  }

  void _saveLocal() async {
    //print(_answers);
    var data = _answers.values.toList();
    var data2 = _bookmarks.values.toList();
    var textData = _answersText.values.toList();
    var idData = _answersId.values.toList();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('answered_' + widget.idQuiz, jsonEncode(data));
    prefs.setString('bookmarked_' + widget.idQuiz, jsonEncode(data2));
    prefs.setString('answeredText_' + widget.idQuiz, jsonEncode(textData));
    prefs.setString('answeredId_' + widget.idQuiz, jsonEncode(idData));
    print('saved to local');
  }

  void _clearLocal() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove('answered_' + widget.idQuiz);
    prefs.remove('bookmarked_' + widget.idQuiz);
    prefs.remove('answeredText_' + widget.idQuiz);
    prefs.remove('timeDoQuiz_' + widget.idQuiz);
    prefs.remove('timeStartQuiz_' + widget.idQuiz);
    prefs.remove('answeredId_' + widget.idQuiz);
    prefs.remove('resumeQuiz');
    prefs.remove('questionResume');
    print('local clear');
  }

  void _selectCheckbox(index, id) {
    if (_answers[_currentIndex][index] == false) {
      _answers[_currentIndex][index] = true;
    } else {
      _answers[_currentIndex][index] = false;
    }
    if (_answersId[_currentIndex].contains(id)) {
      _answersId[_currentIndex].remove(id);
    } else {
      _answersId[_currentIndex].add(id);
    }
  }

  void _multiText(text) {
    if (_answersText[_currentIndex].contains(text)) {
      _answersText[_currentIndex].remove(text);
    } else {
      _answersText[_currentIndex].add(text);
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (t) {
      setState(() {
        if (timeDoQuiz == widget.duration * 60 && widget.duration != 0) {
          t.cancel();
        } else {
          timeDoQuiz += 1;
          valueProgress += (1 / (widget.duration * 60));
          // print(valueProgress);
          print('time do quiz: ${timeDoQuiz}');
          print('remain time: ${remainTime}');
        }
      });
    });
  }

  void _saveTimeDoQuiz() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setInt('timeDoQuiz_' + widget.idQuiz, timeDoQuiz);
    print('save time do quiz');
  }

  void _addResumeQuiz() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('resumeQuiz', widget.idQuiz);
    prefs.setString('questionResume', jsonEncode(question));
  }

  Future<bool> _timeUpDiaglog() async {
    print('timeup dialog');
    _endQuiz();
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => WillPopScope(
            child: Dialog(
              // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
              insetPadding: EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                  child: Column(
                    children: [
                      Container(
                        width: 196,
                        height: 139,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                          image: AssetImage("assets/images/info_dialog.png"),
                          fit: BoxFit.fill,
                        )),
                      ),
                      Text(
                        "${AppLocalization.of(context)!.getTranslatedValues("timeUpDialog")}",
                        style: AppStyles.dialogText,
                        textAlign: TextAlign.center,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 24),
                        child: Container(
                          child: Row(
                            children: [
                              Expanded(
                                  child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  context.loaderOverlay.show();
                                },
                                child: Text(
                                  "OK",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ))
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
            onWillPop: () async {
              return false;
            },
          ),
        )) ??
        false;
  }

  Future<bool> _onBackPressed() async {
    return (await showDialog(
          context: context,
          builder: (BuildContext context) => Dialog(
            backgroundColor: Colors.white,
            // contentPadding: EdgeInsets.fromLTRB(16, 30, 16, 24),
            insetPadding: EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 30, 16, 24),
                child: Column(
                  children: [
                    Container(
                      width: 196,
                      height: 139,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage("assets/images/info_dialog.png"),
                        fit: BoxFit.fill,
                      )),
                    ),
                    Text(
                      "${AppLocalization.of(context)!.getTranslatedValues("quitQuizDialog")}",
                      style: AppStyles.dialogText.copyWith(color: Colors.black),
                      textAlign: TextAlign.center,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Container(
                        child: Row(
                          children: [
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(right: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  "${AppLocalization.of(context)!.getTranslatedValues("noBtn")}",
                                  style: AppStyles.secondaryButton,
                                  textAlign: TextAlign.center,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.greenPrimary,
                                  backgroundColor: Colors
                                      .white, //specify the color of the button's text and icons as well as the overlay colors used to indicate the hover, focus, and pressed states
                                  shadowColor: Colors
                                      .white, //specify the button's elevation color
                                  elevation: 0, //buttons Material shadow
                                  // padding: const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 8.0, left: 8.0), //specify the button's Padding
                                  minimumSize: Size(20,
                                      44), //specify the button's first: width and second: height
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle
                                          .solid), //set border for the button
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            )),
                            Expanded(
                                child: Padding(
                              padding: const EdgeInsets.only(left: 6),
                              child: ElevatedButton(
                                onPressed: () {
                                  _saveTimeDoQuiz();
                                  _addResumeQuiz();
                                  Navigator.of(context).pop(false);
                                  _goBack(context);
                                },
                                child: Text(
                                  "${AppLocalization.of(context)!.getTranslatedValues("yesBtn")}",
                                  style: AppStyles.primaryButton,
                                ),
                                style: ElevatedButton.styleFrom(
                                  foregroundColor: AppColors.white,
                                  backgroundColor: AppColors.greenPrimary,
                                  shadowColor: Color.fromARGB(92, 0, 166, 144),
                                  elevation: 0,
                                  minimumSize: Size(20, 44),
                                  side: BorderSide(
                                      color: AppColors.greenPrimary,
                                      width: 1.0,
                                      style: BorderStyle.solid),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0)),
                                ),
                              ),
                            ))
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        )) ??
        false;
  }

  _showFullModalQuestionList(context) {
    ScrollController _scrollController = ScrollController();
    int bookmarkCount = 0;
    String mode = 'all';
    for (int i = 0; i < _bookmarks.length; i++) {
      if (_bookmarks[i] != null && _bookmarks[i] == 1) {
        bookmarkCount++;
      }
    }
    showGeneralDialog(
      context: context,
      barrierDismissible:
          false, // should dialog be dismissed when tapped outside
      barrierLabel: "Modal", // label for barrier
      transitionDuration: Duration(
          milliseconds:
              300), // how long it takes to popup dialog after button click
      pageBuilder: (_, __, ___) {
        // your widget implementation
        return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
          return Scaffold(
            appBar: AppBar(
                backgroundColor: Colors.transparent,
                centerTitle: true,
                leading: IconButton(
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    }),
                title: Text(
                  "${AppLocalization.of(context)!.getTranslatedValues("questionList")}",
                  style: AppStyles.body20
                      .copyWith(color: Theme.of(context).colorScheme.secondary),
                ),
                elevation: 0.0),
            body: LoaderOverlay(
              child: Container(
                padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: const Color(0xfff8f8f8),
                      width: 1,
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 5, bottom: 5),
                      child: Row(
                        children: [
                          InkWell(
                            child: Text(
                                AppLocalization.of(context)!
                                    .getTranslatedValues("allQuestion")!,
                                style: mode == 'all'
                                    ? AppStyles.bodyBold.copyWith(
                                        fontSize: 15, color: AppColors.darkGrey)
                                    : AppStyles.bodyBold.copyWith(
                                        fontSize: 15,
                                        color: AppColors.lightGrey2)),
                            onTap: () {
                              setState(() {
                                mode = 'all';
                                _scrollController.animateTo(0,
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.fastOutSlowIn);
                              });
                            },
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 15, right: 15),
                            child: Text('|',
                                style: TextStyle(
                                    color: AppColors.lightGrey, fontSize: 15)),
                          ),
                          InkWell(
                            child: Row(
                              children: [
                                Icon(
                                  Icons.bookmark,
                                  color: mode == 'all'
                                      ? AppColors.lightGrey2
                                      : AppColors.darkGrey,
                                ),
                                Text(
                                    '${AppLocalization.of(context)!.getTranslatedValues("bookmarkLbl")} (${bookmarkCount})',
                                    style: mode == 'all'
                                        ? AppStyles.bodyBold.copyWith(
                                            fontSize: 15,
                                            color: AppColors.lightGrey2)
                                        : AppStyles.bodyBold.copyWith(
                                            fontSize: 15,
                                            color: AppColors.darkGrey)),
                              ],
                            ),
                            onTap: () {
                              setState(() {
                                mode = 'bookmark';
                                print('bookmark');
                                _scrollController.animateTo(0,
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.fastOutSlowIn);
                              });
                            },
                          )
                        ],
                      ),
                    ),
                    if (mode == 'all') ...{
                      Expanded(
                        child: ListView.separated(
                            controller: _scrollController,
                            itemBuilder: (context, index) {
                              return ListTile(
                                minLeadingWidth: 20,
                                title: Row(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(right: 20),
                                      child: Text(
                                          '${AppLocalization.of(context)!.getTranslatedValues("questionLbl")} ${index + 1}',
                                          style: _bookmarks[index] != null &&
                                                  _bookmarks[index] == 1
                                              ? AppStyles.bodyBold.copyWith(
                                                  color: Colors.yellow)
                                              : _answersText[index]
                                                          .isNotEmpty ||
                                                      index == _currentIndex
                                                  ? _answersText[index]
                                                          .isNotEmpty
                                                      ? AppStyles.bodyBold
                                                          .copyWith(
                                                              color:
                                                                  Colors.green)
                                                      : AppStyles.bodyBold
                                                          .copyWith(
                                                              color:
                                                                  Colors.orange)
                                                  : AppStyles.bodyBold.copyWith(
                                                      color: Colors.blue)),
                                    ),
                                    if (_answersText[index].isNotEmpty) ...{
                                      Text(
                                          "${AppLocalization.of(context)!.getTranslatedValues("answered")}",
                                          style: _bookmarks[index] != null &&
                                                  _bookmarks[index] == 1
                                              ? AppStyles.bodyBold.copyWith(
                                                  color: Colors.yellow)
                                              : AppStyles.body.copyWith(
                                                  color: Colors.green))
                                    } else if (_answersText[index].isEmpty &&
                                        index == _currentIndex) ...{
                                      Text(
                                          "${AppLocalization.of(context)!.getTranslatedValues("unAnswered")}",
                                          style: _bookmarks[index] != null &&
                                                  _bookmarks[index] == 1
                                              ? AppStyles.bodyBold.copyWith(
                                                  color: Colors.yellow)
                                              : AppStyles.body.copyWith(
                                                  color: Colors.orange))
                                    }
                                  ],
                                ),
                                leading: _bookmarks[index] != null &&
                                        _bookmarks[index] == 1
                                    ? Icon(Icons.bookmark, color: Colors.yellow)
                                    : Icon(Icons.bookmark,
                                        color: Colors.transparent),
                                onTap: () {
                                  if (_answersText[index].isNotEmpty ||
                                      index == _currentIndex) {
                                    setState(() {
                                      _currentIndex = index;
                                      Navigator.pop(context);
                                    });
                                  }
                                },
                              );
                            },
                            separatorBuilder: (context, index) {
                              return Divider();
                            },
                            itemCount: _answers.length),
                      )
                    } else ...{
                      Expanded(
                        child: ListView.separated(
                            controller: _scrollController,
                            itemBuilder: (context, index) {
                              if (_bookmarks[index] != null &&
                                  _bookmarks[index] == 1) {
                                return ListTile(
                                  minLeadingWidth: 20,
                                  title: Row(
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(right: 20),
                                        child: Text(
                                            '${AppLocalization.of(context)!.getTranslatedValues("questionLbl")} ${index + 1}',
                                            style: AppStyles.bodyBold.copyWith(
                                                color: AppColors.darkGrey)),
                                      ),
                                      if (_answersText[index].isNotEmpty) ...{
                                        Text(
                                            "${AppLocalization.of(context)!.getTranslatedValues("answered")}",
                                            style: AppStyles.body.copyWith(
                                                color: AppColors.darkGrey))
                                      } else if (_answersText[index].isEmpty &&
                                          index == _currentIndex) ...{
                                        Text(
                                            "${AppLocalization.of(context)!.getTranslatedValues("unAnswered")}",
                                            style: AppStyles.body.copyWith(
                                                color: AppColors.darkGrey))
                                      }
                                    ],
                                  ),
                                  leading: Icon(Icons.bookmark,
                                      color: AppColors.black),
                                  onTap: () {
                                    setState(() {
                                      _currentIndex = index;
                                      Navigator.pop(context);
                                    });
                                  },
                                );
                              } else {
                                return Container(
                                  height: 0,
                                  width: 0,
                                );
                              }
                            },
                            separatorBuilder: (context, index) {
                              if (_bookmarks[index] != null &&
                                  _bookmarks[index] == 1) {
                                return Divider();
                              } else {
                                return Container(
                                  height: 0,
                                  width: 0,
                                );
                              }
                            },
                            itemCount: _bookmarks.length),
                      )
                    }
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  void _checkInternet() async {
    bool connect = await InternetConnectionChecker().hasConnection;
    if (connect) internet = true;
  }

  _goBack(BuildContext context) {
    Navigator.of(context)
        .pushNamedAndRemoveUntil(Routes.home, (Route<dynamic> route) => false);
  }

  @override
  void initState() {
    WidgetsBinding.instance?.addObserver(this);
    _checkInternet();
    futureLocalData = _getLocal();
    _scrollControllerOptions = ScrollController();
    _scrollControllerQuestion = ScrollController();
    _getLastQ();
    super.initState();
  }

  Future<void> _getLastQ() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var answerList;
    int all = 0;
    if (prefs.getString('answered_' + widget.idQuiz) != null) {
      answerList =
          jsonDecode(prefs.getString('answered_' + widget.idQuiz) ?? '');
      all = answerList.length;
    }

    int count = 0;
    for (int i = 0; i < all; i++) {
      if (answerList[i] is String && answerList[i].length == 0) {
        break;
      }
      if (answerList[i] is List && answerList[i].contains(true) == false) {
        break;
      }
      count++;
      //print(_answers[i]);
    }
    if (count == question.length && count != 0) {
      _currentIndex = count - 1;
    } else {
      _currentIndex = count;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive) {
      controller.disposeTimer();
      _timer?.cancel();
      _timer = null;
      print('pause time');
    } else if (state == AppLifecycleState.resumed) {
      print('start time');
      _startTimer();
      remainTime = widget.duration * 60 - timeDoQuiz;
      endTime = DateTime.now().millisecondsSinceEpoch +
          Duration(seconds: remainTime).inMilliseconds;
      controller =
          CountdownTimerController(endTime: endTime, onEnd: _timeUpDiaglog);
    }
    print('state = $state');
  }

  @override
  void dispose() {
    WidgetsBinding.instance?.removeObserver(this);
    controller.dispose();
    _timer?.cancel();
    _timer = null;
    super.dispose();
    print('dispose');
    /* if (_currentIndex < (question.length - 1)) {
      _saveLocal();
    } */
  }

  @override
  void setState(fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
        future: futureLocalData,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            if (endTime == 0) {
              _startTimer();
              if (widget.duration == 0) {
                endTime = 9999999999;
              } else {
                endTime = DateTime.now().millisecondsSinceEpoch +
                    Duration(seconds: remainTime).inMilliseconds;
              }
              controller = CountdownTimerController(
                  endTime: endTime, onEnd: _timeUpDiaglog);
            }
            return WillPopScope(
              onWillPop: _onBackPressed,
              child: LoaderOverlay(
                child: LoadingOverlay(
                    child: Scaffold(
                      appBar: AppBar(
                        iconTheme: IconThemeData(
                          color: Colors.white,
                        ),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        title: Text(
                          widget.quizName,
                          style: AppStyles.title,
                        ),
                        actions: [
                          IconButton(
                            icon: Icon(Icons.list, color: AppColors.white),
                            onPressed: () =>
                                {_showFullModalQuestionList(context)},
                          ),
                        ],
                      ),
                      body: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 10, top: 10),
                            child: Row(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(right: 5),
                                  child: Icon(
                                    Icons.access_time,
                                    color:
                                        Theme.of(context).colorScheme.secondary,
                                  ),
                                ),
                                Text(
                                    '${AppLocalization.of(context)!.getTranslatedValues("remainTime")}: ',
                                    style: AppStyles.examBody.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondary)),
                                widget.duration == 0
                                    ? Text(
                                        "${AppLocalization.of(context)!.getTranslatedValues("noLimit")}",
                                        style: AppStyles.examBody.copyWith(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .secondary))
                                    : CountdownTimer(
                                        controller: controller,
                                        endTime: endTime,
                                        onEnd: _timeUpDiaglog,
                                        widgetBuilder:
                                            (_, CurrentRemainingTime? time) {
                                          if (time == null) {
                                            return Text(
                                                AppLocalization.of(context)!
                                                    .getTranslatedValues(
                                                        "timeUp")!);
                                          }
                                          Object? hours = time.hours == null
                                              ? '0'
                                              : time.hours;
                                          Object? min =
                                              time.min == null ? '0' : time.min;
                                          return Text(
                                              '${hours.toString().padLeft(2, '0')}:${min.toString().padLeft(2, '0')}:${time.sec.toString().padLeft(2, '0')}',
                                              style: AppStyles.examBody
                                                  .copyWith(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .secondary));
                                        },
                                      ),
                              ],
                            ),
                          ),
                          if (widget.duration != 0) ...{
                            Container(
                              margin:
                                  EdgeInsets.only(left: 10, right: 10, top: 5),
                              child: LinearProgressIndicator(
                                backgroundColor: Colors.grey,
                                color: AppColors.greenButton,
                                minHeight: 5,
                                value: valueProgress,
                              ),
                            )
                          },
                          Expanded(
                            child: Container(
                              // padding: const EdgeInsets.all(16.0),
                              margin: const EdgeInsets.only(
                                  top: 10, left: 10, right: 10),
                              decoration: BoxDecoration(
                                  border: Border.all(color: Color(0xffdddddd)),
                                  color: Color(0xfff9fafb),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5))),
                              child: Row(
                                children: [
                                  Flexible(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        GestureDetector(
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.all(16.0),
                                              decoration: BoxDecoration(
                                                  color: Color(0xFFE3E3E4),
                                                  border: Border(
                                                      bottom: BorderSide(
                                                          color: Color(
                                                              0xffdddddd)))),
                                              child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      '${AppLocalization.of(context)!.getTranslatedValues("questionLbl")} ${_currentIndex + 1} ${AppLocalization.of(context)!.getTranslatedValues("totalOf")} ${question.length}',
                                                      style: AppStyles
                                                          .examHeading15,
                                                    ),
                                                    InkWell(
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        onTap: () {
                                                          setState(() {
                                                            _bookmark();
                                                            _saveLocal();
                                                          });
                                                        },
                                                        child: SizedBox(
                                                          width: 20,
                                                          height: 20,
                                                          child: _bookmarks[
                                                                      _currentIndex] ==
                                                                  1
                                                              ? Icon(
                                                                  Icons
                                                                      .bookmark,
                                                                  color:
                                                                      AppColors
                                                                          .black)
                                                              : Icon(
                                                                  Icons
                                                                      .bookmark_border,
                                                                  color: AppColors
                                                                      .black),
                                                        )),
                                                  ]),
                                            ),
                                            onTap: () {
                                              setState(() {
                                                _bookmark();
                                              });
                                            }),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 10,
                                              left: 10,
                                              right: 10,
                                              bottom: 10),
                                          child: Container(
                                            constraints: BoxConstraints(
                                                maxHeight:
                                                    MediaQuery.of(context)
                                                            .size
                                                            .height *
                                                        0.3),
                                            child: SingleChildScrollView(
                                              controller:
                                                  _scrollControllerQuestion,
                                              child: HtmlWidget(
                                                question[_currentIndex]
                                                    .question
                                                    .replaceAll('../../',
                                                        Common.apiDomain),
                                                key: ValueKey(_currentIndex),
                                                textStyle: TextStyle(
                                                    fontSize: 18,
                                                    color: AppColors.black,
                                                    height: 1.5),
                                                onTapImage: (p0) {
                                                  print(p0.sources.first.url);
                                                  final imageProvider =
                                                      Image.network(p0.sources
                                                              .first.url)
                                                          .image;
                                                  showImageViewer(
                                                      context, imageProvider,
                                                      backgroundColor: Colors
                                                          .black
                                                          .withOpacity(0.8),
                                                      useSafeArea: true,
                                                      doubleTapZoomable: true);
                                                },
                                              )
                                              /* Text(
                                                      Helper().parseHtmlString(
                                                          question[_currentIndex]
                                                              .question),
                                                      style: AppStyles.body18
                                                          .copyWith(
                                                              color:
                                                                  AppColors.black,
                                                              fontSize:
                                                                  double.parse(
                                                                      CommonFont
                                                                          .size))) */
                                              ,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Container(
                                            decoration: BoxDecoration(
                                                border: Border(
                                                    top: BorderSide(
                                                        color: Color(
                                                            0xffdddddd)))),
                                            child: SingleChildScrollView(
                                              controller:
                                                  _scrollControllerOptions,
                                              child: Theme(
                                                data:
                                                    Theme.of(context).copyWith(
                                                  unselectedWidgetColor:
                                                      Colors.grey,
                                                ),
                                                child: Column(
                                                  children: [
                                                    ...question[_currentIndex]
                                                        .options!
                                                        .mapIndexed(
                                                            (index, option) {
                                                      if (question[
                                                                  _currentIndex]
                                                              .type ==
                                                          "single") {
                                                        return RadioListTile(
                                                            activeColor:
                                                                AppColors.black,
                                                            /* visualDensity:
                                                            VisualDensity(
                                                                horizontal: -4,
                                                                vertical: -2), */
                                                            title: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      top: 5.0,
                                                                      bottom:
                                                                          5.0),
                                                              child: RichText(
                                                                text: TextSpan(
                                                                  text: alphabet[
                                                                      index],
                                                                  style: AppStyles
                                                                      .bodyBold
                                                                      .copyWith(
                                                                          color: AppColors
                                                                              .black,
                                                                          fontSize:
                                                                              double.parse(CommonFont.size) - 5),
                                                                  children: <TextSpan>[
                                                                    TextSpan(
                                                                        text: Helper().parseHtmlString(option.qOption.replaceAll(
                                                                            '\n',
                                                                            ' ')),
                                                                        style: AppStyles.body.copyWith(
                                                                            height:
                                                                                1.5,
                                                                            color:
                                                                                AppColors.black,
                                                                            fontSize: double.parse(CommonFont.size) - 5)),
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                            value: option.oid,
                                                            groupValue: _answers[
                                                                _currentIndex],
                                                            onChanged: (value) {
                                                              setState(() {
                                                                _answers[
                                                                        _currentIndex] =
                                                                    option.oid;
                                                                _answersText[
                                                                        _currentIndex] =
                                                                    option
                                                                        .qOption;
                                                                _answersId[
                                                                    _currentIndex] = [
                                                                  option.oid
                                                                ];
                                                                _saveLocal();
                                                                //print(_answersText);
                                                              });
                                                            });
                                                      } else {
                                                        return CheckboxListTile(
                                                            activeColor:
                                                                AppColors.black,
                                                            controlAffinity:
                                                                ListTileControlAffinity
                                                                    .leading,
                                                            title: RichText(
                                                              text: TextSpan(
                                                                text: alphabet[
                                                                    index],
                                                                style: AppStyles
                                                                    .bodyBold
                                                                    .copyWith(
                                                                        color: AppColors
                                                                            .black,
                                                                        fontSize:
                                                                            double.parse(CommonFont.size) -
                                                                                5),
                                                                children: <TextSpan>[
                                                                  TextSpan(
                                                                      text: Helper().parseHtmlString(option
                                                                          .qOption
                                                                          .replaceAll(
                                                                              '\n', ' ')),
                                                                      style: AppStyles.body.copyWith(
                                                                          height:
                                                                              1.2,
                                                                          color: AppColors
                                                                              .black,
                                                                          fontSize:
                                                                              double.parse(CommonFont.size) - 5)),
                                                                ],
                                                              ),
                                                            ),
                                                            value: _answers[
                                                                    _currentIndex]
                                                                [index],
                                                            onChanged: (value) {
                                                              setState(() {
                                                                _multiText(option
                                                                    .qOption);
                                                                _selectCheckbox(
                                                                    index,
                                                                    option.oid);
                                                                _saveLocal();
                                                              });
                                                            });
                                                      }
                                                    })
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 10),
                            child: Container(
                              child: Row(
                                children: [
                                  Expanded(
                                      child: Padding(
                                    padding: const EdgeInsets.only(
                                        top: 12, right: 6, bottom: 38),
                                    child: ElevatedButton(
                                      onPressed: () {
                                        _currentIndex != 0
                                            ? setState(() {
                                                print('back');
                                                _currentIndex--;
                                                _scrollControllerOptions.jumpTo(
                                                    _scrollControllerOptions
                                                        .position
                                                        .minScrollExtent);
                                                _scrollControllerQuestion.jumpTo(
                                                    _scrollControllerQuestion
                                                        .position
                                                        .minScrollExtent);
                                              })
                                            : null;
                                      },
                                      child: Text(
                                        AppLocalization.of(context)!
                                            .getTranslatedValues("previous")!,
                                        style: AppStyles.secondaryButton
                                            .copyWith(color: Color(0xFF6A6A6A)),
                                        textAlign: TextAlign.center,
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        foregroundColor: Color(0xFF6A6A6A),
                                        backgroundColor: Color(0xFFE8E8E8),
                                        shadowColor: Colors.white,
                                        elevation: 0,
                                        minimumSize: Size(20, 48),
                                        side: BorderSide(
                                            color: Color(0xFFE8E8E8),
                                            width: 1.0,
                                            style: BorderStyle.solid),
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(4.0)),
                                      ),
                                    ),
                                  )),
                                  Expanded(
                                      child: Padding(
                                    padding: const EdgeInsets.only(
                                        top: 12, left: 6, bottom: 38),
                                    child: ElevatedButton(
                                      onPressed: _handleNext,
                                      child: Text(
                                        _currentIndex < (question.length - 1)
                                            ? AppLocalization.of(context)!
                                                .getTranslatedValues("next")!
                                            : AppLocalization.of(context)!
                                                .getTranslatedValues("finish")!,
                                        style: AppStyles.primaryButton,
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        foregroundColor: AppColors.white,
                                        backgroundColor: AppColors.greenPrimary,
                                        shadowColor:
                                            Color.fromARGB(92, 0, 166, 144),
                                        elevation: 4,
                                        minimumSize: Size(20, 48),
                                        side: BorderSide(
                                            color: AppColors.greenPrimary,
                                            width: 1.0,
                                            style: BorderStyle.solid),
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(4.0)),
                                      ),
                                    ),
                                  )),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    isLoading: _saving),
              ),
            );
          } else if (snapshot.hasError) {
            if (internet == false) {
              return Scaffold(
                  appBar: AppBar(
                    leading: IconButton(
                      icon: Icon(Icons.arrow_back),
                      iconSize: 20.0,
                      onPressed: () {
                        _goBack(context);
                      },
                    ),
                    title: Text(
                      "quizList",
                      style: AppStyles.title,
                    ),
                  ),
                  body: Center(
                      child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset('assets/images/no-internet.png'),
                      Padding(
                        padding: const EdgeInsets.only(top: 20),
                        child: Text("noInternet", style: AppStyles.body),
                      ),
                    ],
                  )));
            } else {
              return ErrorDialog();
            }
          } else {
            return Scaffold(body: Center(child: CircularProgressIndicator()));
          }
        });
  }
}
