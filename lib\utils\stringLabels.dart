//Widget Terms and condition
String termAgreement = "termAgreement";
String termOfService = "termOfService";
String andLbl = "andLbl";
String privacyPolicy = "privacyPolicy";

//fill otp screen
String otpVerificationLbl = "otpVerificationLbl";
String otpSendLbl = "otpSendLbl";
String submitBtn = "submitBtn";
String submittingButton = "submittingButton";
String resetLbl = "resetLbl";
String resendBtn = "resendBtn";
String resendSnackBar = "resendSnackBar";
String noInterNetSnackBar = "noInterNetSnackBar";
String enterOtp = "enterOtpMsg";

//otp screen
String enterNumberLbl = "enterNumberLbl";
String receiveOtpLbl = "receiveOtpLbl";
String countryLbl = "countryLbl";
String validMobMsg = "validMobMsg";
String requestOtpLbl = "requestOtpLbl";

//signInscreen
String userLoginLbl = "userLoginLbl";
String emailRequiredMsg = "emailRequiredMsg";
String validEmail = "validEmail";
String emailLbl = "emailLbl";
String pwdLengthMsg = "pwdLengthMsg";
String pwdLbl = "pwdLbl";
String loginLbl = "loginLbl";
String enterEmailLbl = "enterEmailLbl";
String forgotPwdLbl = "forgotPwdLbl";
String resetPwdLbl = "resetPwdLbl";
String resetEnterEmailLbl = "resetEnterEmailLbl";
String pwdResetLinkLbl = 'pwdResetLinkLbl';
String orLbl = "orLbl";
String loginSocialMediaLbl = "loginSocialMediaLbl";
String noAccountLbl = "noAccountLbl";
String signUpLbl = "signUpLbl";

//SignUpSCreen
String cnPwdNotMatchMsg = "cnPwdNotMatchMsg";
String cnPwdLbl = "cnPwdLbl";
String alreadyAccountLbl = "alreadyAccountLbl";
String emailVerify = "emailVerify";

//battle CreateRoomDialog widget
String liveChatLbl = "liveChatLbl";
String entryLbl = "entryLbl";
String creatingLoadingLbl = "creatingLoadingLbl";
String creatingLbl = "creatingLbl"; //use as Uppercase also
String enterCodeLbl = "enterCodeLbl";
String joiningLoadingLbl = "joiningLoadingLbl";
String joinLbl = "joinLbl"; //use as Uppercase also

// battle WaitForOthersContainer Widget
String waitOtherComplete = "waitOtherComplete";

// battle WaitingForPlayesDialog Widget
String waitingLbl = "waitingLbl";
String roomDeletedOwnerLbl = "roomDeletedOwnerLbl";
String okayLbl = "okayLbl";
String entryAmountLbl = "entryAmountLbl";
String roomCodeLbl = "roomCodeLbl";
String shareRoomCodeLbl = "shareRoomCodeLbl";
String vsLbl = "vsLbl";
String startLbl = "startLbl";

//BattleRoomFindOpponentScreen
String findingOpponentLbl = "findingOpponentLbl";
String foundOpponentLbl = "foundOpponentLbl";
String getReadyLbl = "getReadyLbl";
String bestOfLuckLbl = "bestOfLuckLbl";
String opponentNotFoundLbl = "opponentNotFoundLbl";
String retryLbl = "retryLbl";

// battle BattleRoomQuizScreen
String youWonLbl = "youWonLbl";
String opponentLeftLbl = "opponentLeftLbl";
String youLeftLbl = "youLeftLbl";

//MultiUserBattleRoomQuizScreen
String everyOneLeftLbl = "everyOneLeftLbl";

//MultiUserBattleRoomResultScreen
String resultLbl = "resultLbl";
String youLostLbl = "youLostLbl";
String exitLbl = "exitLbl";

//home QuizTypeContainer
String referralCodeLbl = "referralCodeLbl";
String enterReferralCodeLbl = "enterReferralCodeLbl";

//homeScreen
String selfChallengeLbl = "selfChallengeLbl";
String practiceModeLbl = "practiceModeLbl";
String examModeLbl = "examModeLbl";
String reviewQuestionLbl = "reviewQuestionLbl";
String desReviewQuestion = "desReviewQuestion";
String statisticsLbl = "statisticsLbl";
String reportLbl = "reportLbl";
String desReport = "desReport";
String desStatistics = "desStatistics";
String challengeYourselfLbl = "challengeYourselfLbl";
String practiceAnytimeLbl = "practiceAnytimeLbl";

// profile ChooseProfileDialog widget
String photoLibraryLbl = "photoLibraryLbl";
String cameraLbl = "cameraLbl";

// profile EditProfileFieldBottomSheetContainer
String updatingLbl = "updatingLbl";
String updateLbl = "updateLbl";
String enterValidEmailMsg = "enterValidEmailMsg";

//EditProfileFieldDialog
String enterValidNameMsg = "enterValidNameMsg";

//ProfileScreen
String bookmarkLbl = "bookmarkLbl";
String howToPlayLbl = "howToPlayLbl";
String inviteFriendsLbl = "inviteFriendsLbl";
String contactUs = "contactUs";
String aboutUs = "aboutUs";
String termsAndConditions = "termsAndConditions";
String rateUsLbl = "rateUsLbl";
String shareAppLbl = "shareAppLbl";
String logoutLbl = "logoutLbl";
String logoutDialogLbl = "logoutDialogLbl";
String yesBtn = "yesBtn";
String noBtn = "noBtn";
String profileLbl = "profileLbl";
String nameLbl = "nameLbl";
String notEditNumberMsg = "notEditNumberMsg";
String mobileNumberLbl = "mobileNumberLbl";
String notEditMailLbl = "notEditMailLbl";
String groupLbl = "groupLbl";
String certificateLbl = "certificateLbl";

//SelectProfilePictureScreen
String selectProfilePhotoLbl = "selectProfilePhotoLbl";
String selectProfileLbl = "selectProfileLbl";
String uploadProfilePictureLbl = "uploadProfilePictureLbl";
String continueLbl = "continueLbl";
String enterNameLbl = "enterNameLbl";

//MultipleUserDetailsContainer
String completedLbl = "Completed";
String playingLbl = "Playing";

//BookmarkQuizScreen
String completeAllQueLbl = "completeAllQueLbl";
String goBAckLbl = "goBAckLbl";

//'Contest LeaderBoardScreen
String contestLeaderBoardLbl = "contestLeaderBoardLbl";

//"ContestScreen
String pastLbl = "pastLbl";
String liveLbl = "liveLbl";
String upcomingLbl = "upcomingLbl";
String contestLbl = "contestLbl";
String noPastGameLbl = "noPastGameLbl";
String entryFeesLbl = "entryFeesLbl";
String endsOnLbl = "endsOnLbl";
String playersLbl = "playersLbl";
String leaderboardLbl = "leaderboardLbl";
String playLbl = "playLbl";

//FunAndLearnTitleScreen
String questionLbl = "questionLbl";

//resultScreen
String victoryLbl = "victoryLbl";
String congratulationsLbl = "congratulationsLbl";
String defeatLbl = "defeatLbl";
String betterNextLbl = "betterNextLbl";
String winnerLbl = "winnerLbl";
String youLossLbl = "youLossLbl";
String matchDrawLbl = "matchDrawLbl";
String looserLbl = "looserLbl";
String playAgainBtn = "playAgainBtn";
String nextLevelBtn = "nextLevelBtn";
String shareScoreBtn = "shareScoreBtn";
String myScoreLbl = "myScoreLbl";
String reviewAnsBtn = "reviewAnsBtn";
String anotherOpponentBtn = "anotherOpponentBtn";
String homeBtn = "homeBtn";

//reviewAnswerScreen
String notesLbl = "notesLbl";
String yourAnsLbl = "yourAnsLbl";
String correctAndLbl = "correctAndLbl";
String reviewAnswerLbl = "reviewAnswerLbl";
//SelectRoomScreen
String oneToOneLbl = "oneToOneLbl";
String privateRoomLbl = "privateRoomLbl";
String publicRoomLbl = "publicRoomLbl";

//SelfChallengeQuestionsScreen
String attemptedLbl = "attemptedLbl";
String unAttemptedLbl = "unAttemptedLbl";
String selfChallenge = "selfChallenge";

//selfchallangeScreen
String selectNoQusLbl = "selectNoQusLbl";
String selectTimeLbl = "selectTimeLbl";

//SubCategoryAndLevelScreen
String levelLbl = "levelLbl";

//bookmardScreen
String noBookmarkQueLbl = "noBookmarkQueLbl";
String playBookmarkBtn = "playBookmarkBtn";
//CoinStoreScreen
String coinsLbl = "coinsLbl";
String offerLbl = "offerLbl";
String storeLbl = "storeLbl";
//IntroSliderScreen
String title1 = "title1";
String title2 = "title2";
String title3 = "title3";
String description1 = "description1";
String description2 = "description2";
String description3 = "description3";

//LeaderBoardScreen
String dailyLbl = "dailyLbl";
String monthLbl = "monthLbl";
String allTimeLbl = "allTimeLbl";

//ReferAndEarnScreen
String referAndEarn = "referAndEarn";
String referFrdLbl = "referFrdLbl";
String yourRefCOdeLbl = "yourRefCOdeLbl";
String referCodeCopyMsg = "referCodeCopyMsg";
String shareNowLbl = "shareNowLbl";

//RewardsScreen
String quizFanLbl = "quizFanLbl";
String completeSubTitle = "completeSubTitle";
String rewardsLbl = "rewardsLbl";

//SplashScreen
String quizLbl = "quizLbl";
//ExitGameDailog
String quizExitLbl = "quizExitLbl";
//FontSizeDialog
String fontSizeLbl = "fontSizeLbl";
//SettingsDialogContainer
String soundLbl = "soundLbl";
String vibrationLbl = "vibrationLbl";
String settingLbl = "settingLbl";

//quizTypes
String quizZone = "quizZone";
String dailyQuiz = "dailyQuiz";
String groupPlay = "groupPlay";
String battleQuiz = "battleQuiz";
String contest = "contest";
String guessTheWord = "guessTheWord";
String funAndLearn = "funAndLearn";
String trueAndFalse = "trueAndFalse";
String mathManiaKey = "mathMania";
String audioQuestionsKey = 'audioQuestions';
String examKey = "exam";
String tournamentKey = "tournament";

String desQuizZone = "desQuizZone";
String desDailyQuiz = "desDailyQuiz";
String desGroupPlay = "desGroupPlay";
String desBattleQuiz = "desBattleQuiz";
String desContest = "desContest";
String desGuessTheWord = "desGuessTheWord";
String desFunAndLearn = "desFunAndLearn";
String desTrueAndFalse = "desTrueAndFalse";
String desMathManiaKey = "desMathMania";
String desAudioQuestionsKey = "desAudioQuestions";
String desExamKey = "desExam";
String desTournamentKey = "desTournament";

String noCoinsMsg = "noCoinsMsg";

String uploadingBtn = "uploadingBtn";
String rankLbl = "rankLbl";
String scoreLbl = "scoreLbl";
String otpNotMatchMsg = "otpNotMatchMsg";

String unableToCreateRoomKey = "unableToCreateRoom";
String unableToFindRoomKey = "unableToFindRoom";
String unableToJoinRoomKey = "unableToJoinRoom";
String unableToSubmitAnswerKey = "unableToSubmitAnswer";
String iHaveInviteCodeKey = "iHaveInviteCode";
String cancelButtonKey = "cancel";
String reportQuestionKey = "reportQuestion";
String enterReasonKey = "enterReason";
String notesKey = "notesLbl";
String letsStart = "letsStart";

String createRoomKey = "createRoom";
String joinRoomKey = "joinRoom";
String accountDeactivatedKey = "accountDeactivated";
String currentlyNotAvailableKey = "currentlyNotAvailable";
String coinStoreKey = "coinStore";
String purchaseErrorKey = "purchaseError";
String productsFetchedFailureKey = "productsFetchedFailure";
String noProductsKey = "noProducts";
String inAppPurchaseUnavailableKey = "inAppPurchaseUnavailable";
String coinsBoughtSuccessKey = "coinsBoughtSuccess";
String themeKey = "theme";
String examThemeKey = "examTheme";
String lightThemeKey = "lightTheme";
String darkThemeKey = "darkTheme";
String scrumThemeKey = "scrumorg";
String eliteThemeKey = "elite";
String selectCategoryKey = "selectCategory";
String selectSubCategoryKey = "selectSubCategory";
String languageKey = "language";
String accountKey = "account";
String aboutQuizAppKey = "aboutQuizApp";
String chatKey = "chat";
String messagesKey = "messages";
String emojisKey = "emojis";
String badgesKey = "badges";
String showOptionsKey = "showOptions";
String enterRoomCodeHereKey = "enterRoomCodeHere";
String currentCoinsKey = "currentCoins";
String pleaseSelectCategoryKey = "pleaseSelectCategory";
String moreThanZeroCoinsKey = "moreThanZeroCoins";
String updateApplicationKey = "updateApplication";
String updateKey = "update";
String warningKey = "warning";
String failedToGetAppUrlKey = "failedToGetAppUrl";

String needMoreKey = "needMore";
String correctAnswerToUnlockKey = "correctAnswerToUnlock";

String getKey = "get";
String coinsUnlockingByBadgeKey = "coinsUnlockingByBadge";
String totalRewardsEarnedKey = "totalRewardsEarned"; //
String byUnlockingKey = "byUnlocking";
String scratchHereKey = "scratchHere";
String noRewardsKey = "noRewards";
String questionsKey = "questions";
String backKey = "back";
String hintKey = "hint";

String myRankKey = "myRank";

String enterValidExamKey = "enterValidExamKey";
String enterExamKey = "enterExamKey";
String examResultKey = "examResult";
String examDurationKey = "examDuration";
String completedInKey = "completedIn";
String totalQuestionsKey = "totalQuestions";
String obtainedMarksKey = "obtainedMarks";
String markKey = "mark";
String totalKey = "total";
String correctKey = "correct";
String incorrectKey = "incorrect";
String youLeftTheExamKey = "youLeftTheExam";
String iAgreeWithExamRulesKey = "iAgreeWithExamRules";
String pleaseAcceptExamRulesKey = "pleaseAcceptExamRules";
String examRulesKey = "examRules";
String viewAllRulesKey = "viewAllRules";
String statisticsLabelKey = "statisticsLabel";
String helloKey = "hello";
String collectedBadgesKey = "collectedBadges";
String quizDetailsKey = "quizDetails";
String questionDetailsKey = "questionDetails";
String battleStatisticsKey = "battleStatistics";
String viewAllKey = "viewAll";
String playedKey = "played";
String wonKey = "won";
String lostKey = "lost";
String deleteAccountKey = "deleteAccount";
String deleteAccountConfirmationKey = "deleteAccountConfirmation";
String deletingAccountKey = "deletingAccount";
String accountDeletedSuccessfullyKey = "accountDeletedSuccessfully";
String coinHistoryKey = "coinHistory";
String walletKey = "wallet";
String requestKey = "request";
String transactionKey = "transaction";
String redeemableAmountKey = "redeemableAmount";
String totalCoinsKey = "totalCoins";
String redeemNowKey = "redeemNow";
String minimumRedeemableAmountKey = "minimumRedeemableAmount";
String notEnoughCoinsToRedeemAmountKey = "notEnoughCoinsToRedeemAmount";
String totalEarningsKey = "totalEarnings";
String redeemRequestKey = "redeemRequest";
String totalQuizKey = "totalQuiz";
String averageKey = "average";
String recentKey = "recent";

//coins type
String wonQuizZoneKey = "wonQuizZone";
String wonMathQuizKey = "wonMathQuiz";
String wonBattleKey = "wonBattle";
String playedBattleKey = "playedBattle";
String playedContestKey = "playedContest";
String wonContestKey = "wonContest";
String playedGroupBattleKey = "playedGroupBattle";
String wonGroupBattleKey = "wonGroupBattle";
String wonGuessTheWordKey = "wonGuessTheWord";
String wonFunNLearnKey = "wonFunNLearn";
String wonTrueFalseKey = "wonTrueFalse";
String wonDailyQuizKey = "wonDailyQuiz";
String wonAudioQuizKey = "wonAudioQuiz";
String watchedRewardAdKey = "watchedRewardAd";
String boughtCoinsKey = "boughtCoins";
String rewardByScratchingCardKey = "rewardByScratchingCard";
String referredCodeToFriendKey = "referredCodeToFriend";
String used5050lifelineKey = "used5050lifeline";
String usedResetTimerlifelineKey = "usedResetTimerlifeline";
String usedAudiencePolllifelineKey = "usedAudiencePolllifeline";
String usedSkiplifelineKey = "usedSkiplifeline";
String usedHintLifelineKey = "usedHintLifeline";
String redeemedAmountkey = "redeemedAmount";
String pendingKey = "pending";
String coinsWillBeDeductedKey = "coinsWillBeDeducted";
String selectPayoutOptionKey = "selectPayoutOption";
String successfullyRequestedKey = "successfullyRequested";
String trackRequestKey = "trackRequest";
String payoutMethodKey = "payoutMethod";
String makeRequestKey = "makeRequest";
String requestingKey = "requesting";

String completedKey = "completed";
String wrongDetailsKey = "wrongDetails";
String pleaseFillAllDataKey = "pleaseFillAllData";
String changePayoutMethodKey = "changePayoutMethod";
//String coinsWillDeductToReviewAnswerKey = "coinsWillDeductToReviewAnswer";
String appUnderMaintenanceKey = "appUnderMaintenance";
String youWillGetKey = "youWillGet";
String theyWillGetKey = "theyWillGet";
String changeCertificateKey = "changeCertificate";
