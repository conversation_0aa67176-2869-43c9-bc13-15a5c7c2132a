import 'dart:core';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/reportQuestionPopup.dart';
import 'package:flutterquiz/ui/screens/review_question/review_tag.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/ui/screens/review_question/review_question.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart';

class QuizModel {
  final String question;
  final String correctAnswer;
  final String yourAnswer;
  final String remainingAnswer;
  final String numWrong;
  final String description;

  QuizModel({
    this.question = "",
    this.correctAnswer = "",
    this.yourAnswer = "",
    this.remainingAnswer = "",
    this.numWrong = "",
    this.description = "",
  });
}

class ReviewContent extends StatelessWidget {
  Map<String, dynamic> model;
  int numberQuestion = 0;
  ReviewContent({required this.model, required this.numberQuestion});

  @override
  Widget build(BuildContext context) {
    List<dynamic> listOptions = model["options"];
    List<String> userAns = model["userAns"].split(",");
    List<String> correctAns = model["correct"].split(",");
    AppTheme appTheme = context.read<ThemeCubit>().state.appTheme;
    double elementWidth = MediaQuery.of(context).size.width * (0.84);

    Future openDialog() async {
      await showDialog(
          context: context,
          builder: (context) {
            return ReportQuestionPopup(
              question:
                  model["question"].replaceAll('../../', Common.apiDomain),
              questionId: model["qid"],
              quizId: model["quid"] ?? "0",
            );
          });
      Navigator.of(context).pop();
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(
            height: 15.0,
          ),
          if (numberQuestion != 0)
            Padding(
                padding: EdgeInsets.only(bottom: 12.0),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NumberRectangleWidget(
                        number: numberQuestion,
                        backgroundColor: Theme.of(context).primaryColor,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 20),
                        child: InkWell(
                          onTap: () {
                            openDialog();
                          },
                          child: Icon(
                            Icons.chat,
                            size: 20,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      )
                    ])),
          Container(
            width: elementWidth,
            child: HtmlWidget(
              model["question"].replaceAll('../../', Common.apiDomain),
              key: ValueKey(UniqueKey),
              textStyle: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  height: 1.5,
                  color: appTheme == AppTheme.Light
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).colorScheme.secondary),
              onTapImage: (p0) {
                print(p0.sources.first.url);
                final imageProvider = Image.network(p0.sources.first.url).image;
                showImageViewer(context, imageProvider,
                    backgroundColor: Colors.black.withOpacity(0.8),
                    useSafeArea: true,
                    doubleTapZoomable: true);
              },
            ),
          ),
          Divider(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: listOptions.map((option) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15.0),
                      color: userAns.contains(option["oid"]) == true
                          ? correctAns.contains(option["oid"]) == true
                              ? Colors.green
                              : Colors.red
                          : correctAns.contains(option["oid"]) == true
                              ? Colors.green
                              : Theme.of(context).colorScheme.secondary,
                    ),
                    width: MediaQuery.of(context).size.width * (0.8),
                    margin: EdgeInsets.only(top: 15.0, left: 0),
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.0, vertical: 15.0),
                    child: Row(children: [
                      userAns.contains(option["oid"]) == true
                          ? double.parse(option["score"]!) > 0
                              ? model["question_type"].toString() ==
                                      "Câu hỏi một lựa chọn"
                                  ? Icon(
                                      Icons.radio_button_checked_rounded,
                                      color: Colors.white,
                                    )
                                  : Icon(
                                      Icons.check_box,
                                      color: Colors.white,
                                    )
                              : model["question_type"].toString() ==
                                      "Câu hỏi một lựa chọn"
                                  ? Icon(
                                      Icons.radio_button_checked_rounded,
                                      color: Colors.white,
                                    )
                                  : Icon(
                                      Icons.check_box,
                                      color: Colors.white,
                                    )
                          : double.parse(option["score"]!) > 0
                              ? model["question_type"].toString() ==
                                      "Câu hỏi một lựa chọn"
                                  ? Icon(
                                      Icons.radio_button_off_rounded,
                                      color: Colors.white,
                                    )
                                  : Icon(
                                      Icons.check_box_outline_blank_rounded,
                                      color: Colors.white,
                                    )
                              : model["question_type"].toString() ==
                                      "Câu hỏi một lựa chọn"
                                  ? Icon(
                                      Icons.radio_button_off_rounded,
                                      color: Colors.grey,
                                    )
                                  : Icon(
                                      Icons.check_box_outline_blank_rounded,
                                      color: Colors.grey,
                                    ),
                      SizedBox(
                        width: 12,
                      ),
                      Flexible(
                          child: HtmlWidget(
                        option["q_option"]
                                ?.replaceAll('../../', Common.apiDomain) ??
                            "",
                        textStyle: TextStyle(
                            color: userAns.contains(option["oid"]) ||
                                    double.parse(option["score"]!) > 0
                                ? Colors.white
                                : Theme.of(context).colorScheme.surface,
                            height: 1.0,
                            fontSize: 16.0),
                        onTapImage: (p0) {
                          print(p0.sources.first.url);
                          final imageProvider =
                              Image.network(p0.sources.first.url).image;
                          showImageViewer(context, imageProvider,
                              backgroundColor: Colors.black.withOpacity(0.8),
                              useSafeArea: true,
                              doubleTapZoomable: true);
                        },
                      ))
                    ]),
                  ),
                ],
              );
            }).toList(),
          ),
          SizedBox(
            height: 10.0,
          ),
          SizedBox(
            height: 10.0,
          ),
          model["description"] != ""
              ? Column(
                  children: [
                    const MySeparator(color: Colors.grey),
                    Padding(
                      padding: const EdgeInsets.only(top: 0.0),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 10.0,
                          ),
                          Container(
                            width: elementWidth,
                            child: /* Text(
                                      AppLocalization.of(context)!
                                              .getTranslatedValues(
                                                  "description")! +
                                          ": \n" +
                                          "$description",
                                      textAlign: TextAlign.start,
                                      style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14.0,
                                          fontFamily: 'Lato',
                                          letterSpacing: 1.2,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .secondary),
                                    ), */
                                Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                RichText(
                                  text: TextSpan(
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 15.0,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondary),
                                    children: <TextSpan>[
                                      TextSpan(
                                          text: AppLocalization.of(context)!
                                                  .getTranslatedValues(
                                                      "description")! +
                                              ":",
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold)),
                                      /* TextSpan(
                                                  text: '$description',
                                                ), */
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(top: 5),
                                  padding: EdgeInsets.zero,
                                  child: HtmlWidget(
                                    /* "<b style='margin-right:3px'>" +
                                        AppLocalization.of(context)!
                                            .getTranslatedValues(
                                                "description")! +
                                        ":  </b>" + */
                                    model["description"]
                                        .replaceAll('../../', Common.apiDomain)
                                        .replaceAll('<p>', '')
                                        .replaceAll('</p>', ''),
                                    textStyle: TextStyle(
                                        height: 1.5,
                                        color: appTheme == AppTheme.Light
                                            ? Theme.of(context)
                                                .colorScheme
                                                .secondary
                                            : Theme.of(context)
                                                .colorScheme
                                                .secondary),
                                    onTapImage: (p0) {
                                      print(p0.sources.first.url);
                                      final imageProvider =
                                          Image.network(p0.sources.first.url)
                                              .image;
                                      showImageViewer(context, imageProvider,
                                          backgroundColor:
                                              Colors.black.withOpacity(0.8),
                                          useSafeArea: true,
                                          doubleTapZoomable: true);
                                    },
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Container(),
          model["count"] != 0 && model["count"] != null
              ? Padding(
                  padding: const EdgeInsets.only(top: 0.0),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 12,
                      ),
                      const MySeparator(color: Colors.grey),
                      SizedBox(
                        height: 12,
                      ),
                      Container(
                        width: elementWidth,
                        child: RichText(
                          text: TextSpan(
                            style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: 15.0,
                                color: Theme.of(context).colorScheme.secondary),
                            children: <TextSpan>[
                              TextSpan(
                                  text: AppLocalization.of(context)!
                                          .getTranslatedValues("numWrong")! +
                                      ": ",
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold)),
                              TextSpan(
                                text: model["count"].toString() == "null"
                                    ? '0'
                                    : model["count"].toString(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : Container(),
          SizedBox(
            height: 25.0,
          ),
        ],
      ),
    );
  }
}
