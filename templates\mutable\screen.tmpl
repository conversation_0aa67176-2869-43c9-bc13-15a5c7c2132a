import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:${appName}${relative}/index.dart';

class ${upperName}Screen extends StatefulWidget {
  const ${upperName}Screen({
    Key key,
    @required ${upperName}Bloc ${privateName}Bloc,
  })  : _${privateName}Bloc = ${privateName}Bloc,
        super(key: key);

  final ${upperName}Bloc _${privateName}Bloc;

  @override
  ${upperName}ScreenState createState() {
    return ${upperName}ScreenState();
  }
}

class ${upperName}ScreenState extends State<${upperName}Screen> {
  ${upperName}ScreenState();

  @override
  void initState() {
    super.initState();
    _load();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<${upperName}Bloc, ${upperName}State>(
        cubit: widget._${privateName}Bloc,
        builder: (
          BuildContext context,
          ${upperName}State currentState,
        ) {
          if (currentState is Un${upperName}State) {
            return Center(
              child: CircularProgressIndicator(),
            );
          }
          if (currentState is Error${upperName}State) {
            return Center(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(currentState.errorMessage ?? 'Error'),
                Padding(
                  padding: const EdgeInsets.only(top: 32.0),
                  child: RaisedButton(
                    color: Colors.blue,
                    child: Text('reload'),
                    onPressed: _load,
                  ),
                ),
              ],
            ));
          }
           if (currentState is In${upperName}State) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(currentState.hello),
                  Text('Flutter files: done'),
                  Padding(
                    padding: const EdgeInsets.only(top: 32.0),
                    child: RaisedButton(
                      color: Colors.red,
                      child: Text('throw error'),
                      onPressed: () => _load(true),
                    ),
                  ),
                ],
              ),
            );
          }
          return Center(
              child: CircularProgressIndicator(),
          );
          
        });
  }

  void _load([bool isError = false]) {
    widget._${privateName}Bloc.add(Load${upperName}Event(isError));
  }
}
