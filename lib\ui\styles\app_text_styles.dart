import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

class AppTextStyles {
  static final TextStyle title = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w400,
  );

  static final TextStyle titleBold = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle headingPass = GoogleFonts.lato(
    color: Colors.green,
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle headingFail = GoogleFonts.lato(
    color: Colors.red,
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading40 = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 40,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle heading30 = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading15 = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle heading20Pass = GoogleFonts.lato(
    color: Colors.green,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );
  static final TextStyle heading20Fail = GoogleFonts.lato(
    color: Colors.red,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle body = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body40 = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 18,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyBold = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.bold,
  );

  static final TextStyle bodylightGrey = GoogleFonts.lato(
    color: AppColors.lightGreen,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyDarkGreen = GoogleFonts.lato(
    color: AppColors.darkGreen,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle bodyDarkRed = GoogleFonts.lato(
    color: AppColors.darkRed,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle body20 = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyLightGrey20 = GoogleFonts.lato(
    color: AppColors.lightGrey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyWhite20 = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body11 = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 11,
    fontWeight: FontWeight.normal,
  );
}
