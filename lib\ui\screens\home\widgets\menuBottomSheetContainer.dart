import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';

import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/guessTheWordBookmarkCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/languageBottomSheetContainer.dart';
import 'package:flutterquiz/ui/screens/inAppPurchase.dart';
import 'package:flutterquiz/ui/screens/profile/widgets/changeCertDialog.dart';
import 'package:flutterquiz/ui/screens/profile/widgets/examThemeDialog.dart';
import 'package:flutterquiz/ui/screens/profile/widgets/themeDialog.dart';
import 'package:flutterquiz/ui/widgets/menuTile.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:launch_review/launch_review.dart';
import 'package:share_plus/share_plus.dart';

class MenuBottomSheetContainer extends StatelessWidget {
  const MenuBottomSheetContainer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.0),
            topRight: Radius.circular(20.0),
          ),
          gradient: UiUtils.buildLinerGradient([
            Theme.of(context).scaffoldBackgroundColor,
            Theme.of(context).canvasColor
          ], Alignment.topCenter, Alignment.bottomCenter)),
      child: Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            //
            SizedBox(
              height: MediaQuery.of(context).size.height * (0.01),
            ),
            // if (!Common.premium &&
            //     Common.registeredFromApp &&
            //     !Common.inPremiumGroup)
            //   MenuTile(
            //     isSvgIcon: false,
            //     onTap: () {
            //       Navigator.of(context).pop();
            //       Navigator.push(context,
            //           MaterialPageRoute(builder: (context) => PaymentPage()));
            //     },
            //     title: "unlockAllExams",
            //     leadingIconData: Icons.workspace_premium,
            //   ),
            context.read<SystemConfigCubit>().isPaymentRequestEnable()
                ? MenuTile(
                    isSvgIcon: true,
                    onTap: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pushNamed(Routes.wallet);
                    },
                    title: walletKey,
                    leadingIcon: "wallet.svg",
                  )
                : SizedBox(),

            // MenuTile(
            //   isSvgIcon: true,
            //   onTap: () {
            //     Navigator.of(context).pop();
            //     Navigator.of(context).pushNamed(Routes.coinHistory);
            //   },
            //   title: coinHistoryKey,
            //   leadingIcon: "coinhistory.svg",
            // ),

            context.read<SystemConfigCubit>().isInAppPurchaseEnable()
                ? MenuTile(
                    isSvgIcon: true,
                    onTap: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pushNamed(Routes.coinStore);
                    },
                    title: coinStoreKey,
                    leadingIcon: "coin_store.svg",
                  )
                : SizedBox(),
            // MenuTile(
            //   isSvgIcon: true,
            //   onTap: () {
            //     Navigator.of(context).pop();
            //     Navigator.of(context).pushNamed(Routes.badges);
            //   },
            //   title: badgesKey,
            //   leadingIcon: "badges.svg",
            // ),

            // MenuTile(
            //   isSvgIcon: true,
            //   onTap: () {
            //     Navigator.of(context).pop();
            //     Navigator.of(context).pushNamed(Routes.rewards);
            //   },
            //   title: rewardsLbl,
            //   leadingIcon: "rewards.svg",
            // ),

            context.read<SystemConfigCubit>().getLanguageMode() == "1"
                ? MenuTile(
                    isSvgIcon: true,
                    onTap: () {
                      Navigator.of(context).pop();
                      showDialog(
                          context: context,
                          builder: (_) => LanguageDailogContainer());
                    },
                    title: languageKey,
                    leadingIcon: "language_icon.svg",
                  )
                : SizedBox(),

            MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();
                showDialog(context: context, builder: (_) => ThemeDialog());
              },
              title: themeKey,
              leadingIcon: "theme.svg", //theme icon
            ),

            // MenuTile(
            //   isSvgIcon: true,
            //   onTap: () {
            //     Navigator.of(context).pop();
            //     showDialog(context: context, builder: (_) => ExamThemeDialog());
            //   },
            //   title: examThemeKey,
            //   leadingIcon: "theme.svg", //theme icon
            // ),

            MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();

                Navigator.of(context).pushNamed(Routes.statistics);
                //
              },
              title: statisticsLabelKey,
              leadingIcon: "statistics.svg", //theme icon
            ),

            /* MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();

                Navigator.of(context).pushNamed(Routes.notification);
                //
              },
              title: "notificationLbl",
              leadingIcon: "notification.svg", //theme icon
            ), */

            MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();

                Navigator.of(context).pushNamed(Routes.profile);
              },
              title: accountKey,
              leadingIcon: "account.svg", //theme icon
            ),

            /* MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context)
                    .pushNamed(Routes.appSettings, arguments: howToPlayLbl);
              },
              title: howToPlayLbl,
              leadingIcon: "howtoplay_icon.svg", //theme icon
            ), */

            MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();
                showDialog(
                    context: context, builder: (_) => ChangeCertDialog());
              },
              title: changeCertificateKey,
              leadingIcon: "termscond_icon.svg", //theme icon
            ),

            MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();

                Navigator.of(context).pushNamed(Routes.aboutApp);
              },
              title: aboutUs,
              leadingIcon: "about_us.svg", //theme icon
            ),

            /*MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();
                LaunchReview.launch(
                  androidAppId: packageName,
                  iOSAppId: "585027354",
                );
              },
              title: "rateUsLbl",
              leadingIcon: "rateus_icon.svg", //theme icon
            ),

            MenuTile(
              isSvgIcon: true,
              onTap: () {
                Navigator.of(context).pop();
                try {
                  if (Platform.isAndroid) {
                    Share.share(context.read<SystemConfigCubit>().getAppUrl() +
                        "\n" +
                        context
                            .read<SystemConfigCubit>()
                            .getSystemDetails()
                            .shareappText);
                  } else {
                    Share.share(context.read<SystemConfigCubit>().getAppUrl() +
                        "\n" +
                        context
                            .read<SystemConfigCubit>()
                            .getSystemDetails()
                            .shareappText);
                  }
                } catch (e) {
                  UiUtils.setSnackbar(e.toString(), context, false);
                }
              },
              title: "shareAppLbl",
              leadingIcon: "share_app.svg", //theme icon
            ), */
            MenuTile(
              isSvgIcon: true,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (_) => AlertDialog(
                          content: Text(
                            AppLocalization.of(context)!
                                .getTranslatedValues("logoutDialogLbl")!,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                          ),
                          actions: [
                            TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  Navigator.of(context).pop();

                                  context
                                      .read<BadgesCubit>()
                                      .updateState(BadgesInitial());
                                  context
                                      .read<BookmarkCubit>()
                                      .updateState(BookmarkInitial());
                                  context
                                      .read<GuessTheWordBookmarkCubit>()
                                      .updateState(
                                          GuessTheWordBookmarkInitial());

                                  context
                                      .read<AudioQuestionBookmarkCubit>()
                                      .updateState(
                                          AudioQuestionBookmarkInitial());

                                  context.read<AuthCubit>().signOut();
                                  Navigator.of(context)
                                      .pushReplacementNamed(Routes.login);
                                },
                                child: Text(
                                  AppLocalization.of(context)!
                                      .getTranslatedValues("yesBtn")!,
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor),
                                )),
                            TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: Text(
                                  AppLocalization.of(context)!
                                      .getTranslatedValues("noBtn")!,
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor),
                                )),
                          ],
                        ));
              },
              title: "logoutLbl",
              leadingIcon: "logout_icon.svg", //theme icon
            ),
            SizedBox(
              height: MediaQuery.of(context).size.height * (0.01),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: Text(
                  examUrl == "https://exam.scrumpass.com"
                      ? AppLocalization.of(context)!
                              .getTranslatedValues("versionLbl")! +
                          Common.version
                      : AppLocalization.of(context)!
                              .getTranslatedValues("versionLbl")! +
                          Common.version +
                          "-beta",
                  style: TextStyle(color: Theme.of(context).primaryColor)),
            ),
            /* Center(
              child: Container(
                width: 250,
                child: MenuTile(
                  isSvgIcon: false,
                  onTap: () {},
                  title: "versionLbl",
                  version: examUrl == "https://exam.scrumpass.com"
                      ? Common.version
                      : Common.version + "-beta",
                  leadingIcon: "guesstheword_icon.svg", //theme icon
                ),
              ),
            ), */
            SizedBox(
              height: MediaQuery.of(context).size.height * (0.025),
            ),
          ],
        ),
      ),
    );
  }
}
