import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/result_detail.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class QuestionContainer extends StatelessWidget {
  final ResultDetail? question;
  final Color? questionColor;
  final int? questionNumber;
  final bool isMathQuestion;

  const QuestionContainer({
    Key? key,
    this.question,
    required this.isMathQuestion,
    this.questionColor,
    this.questionNumber,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    print(isMathQuestion);
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: Container(
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * (0.1)),
                  child: isMathQuestion
                      ? TeXView(
                          child: TeXViewDocument(
                            question!.question!,
                          ),
                          style: TeXViewStyle(
                              contentColor: questionColor ??
                                  Theme.of(context).primaryColor,
                              backgroundColor: Colors.transparent,
                              sizeUnit: TeXViewSizeUnit.pixels,
                              textAlign: TeXViewTextAlign.center,
                              fontStyle: TeXViewFontStyle(fontSize: 23)),
                        )
                      : HtmlWidget(
                          questionNumber == null
                              ? question!.question!
                                  .replaceAll('../../', Common.apiDomain)
                              : "$questionNumber. " +
                                  question!.question!
                                      .replaceAll('../../', Common.apiDomain),
                          textStyle: TextStyle(
                              fontSize: 18,
                              color: Theme.of(context).primaryColor,
                              height: 1.5),
                          onTapImage: (p0) {
                            print(p0.sources.first.url);
                            final imageProvider =
                                Image.network(p0.sources.first.url).image;
                            showImageViewer(context, imageProvider,
                                backgroundColor: Colors.black.withOpacity(0.8),
                                useSafeArea: true,
                                doubleTapZoomable: true);
                          },
                        )),
            ),
          ],
        ),
        SizedBox(
          height: 15.0,
        ),
      ],
    );
  }
}
