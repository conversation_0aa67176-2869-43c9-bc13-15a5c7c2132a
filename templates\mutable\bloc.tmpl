import 'dart:async';
import 'dart:developer' as developer;

import 'package:bloc/bloc.dart';
import 'package:${appName}${relative}/index.dart';

class ${upperName}Bloc extends Bloc<${upperName}Event, ${upperName}State> {
  // todo: check singleton for logic in project
  // use GetIt for DI in projct
  static final ${upperName}Bloc _${privateName}BlocSingleton = ${upperName}Bloc._internal();
  factory ${upperName}Bloc() {
    return _${privateName}BlocSingleton;
  }
  ${upperName}Bloc._internal(): super(Un${upperName}State(0));
  
  @override
  Future<void> close() async{
    // dispose objects
    await super.close();
  }

  @override
  ${upperName}State get initialState => Un${upperName}State(0);

  @override
  Stream<${upperName}State> mapEventToState(
    ${upperName}Event event,
  ) async* {
    try {
      yield* event.applyAsync(currentState: state, bloc: this);
    } catch (_, stackTrace) {
      developer.log('$_', name: '${upperName}Bloc', error: _, stackTrace: stackTrace);
      yield state;
    }
  }
}
