import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/settings/settingsLocalDataSource.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class ExamThemeState {
  final ExamTheme appTheme;
  ExamThemeState(this.appTheme);
}

class ExamThemeCubit extends Cubit<ExamThemeState> {
  SettingsLocalDataSource settingsLocalDataSource;
  ExamThemeCubit(this.settingsLocalDataSource)
      : super(ExamThemeState(UiUtils.getExamThemeFromLabel(
            settingsLocalDataSource.examTheme())));

  void changeTheme(ExamTheme appTheme) {
    settingsLocalDataSource
        .setExamTheme(UiUtils.getExamThemeLabelFromExamTheme(appTheme));
    emit(ExamThemeState(appTheme));
  }
}
