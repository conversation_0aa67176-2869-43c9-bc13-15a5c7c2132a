import 'dart:async';
import 'dart:developer' as developer;

import 'package:bloc/bloc.dart';
import 'package:${appName}${relative}/index.dart';

class ${upperName}Bloc extends Bloc<${upperName}Event, ${upperName}State> {

  ${upperName}Bloc(${upperName}State initialState) : super(initialState);

  @override
  Stream<${upperName}State> mapEventToState(
    ${upperName}Event event,
  ) async* {
    try {
      yield* event.applyAsync(currentState: state, bloc: this);
    } catch (_, stackTrace) {
      developer.log('$_', name: '${upperName}Bloc', error: _, stackTrace: stackTrace);
      yield state;
    }
  }
}
