import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

String noto = 'notoSans';

class AppStyles {
  static final TextStyle title = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w400,
  );

  static final TextStyle titleBold = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading40 = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 40,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading40White = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 40,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle heading15 = GoogleFonts.lato(
    color: AppColors.black,
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle body = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyWhite = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyBold = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.bold,
  );

  static final TextStyle bodylightGrey = GoogleFonts.lato(
    color: AppColors.lightGreen,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyDarkGreen = GoogleFonts.lato(
    color: AppColors.darkGreen,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle bodyDarkRed = GoogleFonts.lato(
    color: AppColors.darkRed,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle body20 = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body18 = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 18,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle bodyLightGrey20 = GoogleFonts.lato(
    color: AppColors.lightGrey,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );

  static final TextStyle bodyWhite20 = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );
  static final TextStyle body11 = GoogleFonts.lato(
    color: AppColors.grey,
    fontSize: 11,
    fontWeight: FontWeight.normal,
  );

  //font test

  static final TextStyle examHeading15 = GoogleFonts.notoSans(
    color: AppColors.black,
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle examBodyBold = GoogleFonts.notoSans(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.bold,
  );
  static final TextStyle examBody = GoogleFonts.notoSans(
    color: AppColors.grey,
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  //Home page
  static final TextStyle homeHeading = GoogleFonts.lato(
    color: AppColors.homeHeading,
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle homeAppOverviewTitle = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.5,
  );

  static final TextStyle homeAppOverviewContent = GoogleFonts.lato(
    color: AppColors.lightGreyText,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle homeCardTitle = GoogleFonts.lato(
    color: AppColors.darkBlueText,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle homeScoreCardTitle = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle homeScoreCardText = GoogleFonts.lato(
    color: AppColors.scoreCardText,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle appBarTitle = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle listQuizTitle = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle listQuizInfo = GoogleFonts.lato(
    color: AppColors.lightGreyText,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle listPass = GoogleFonts.lato(
    color: AppColors.greenText,
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle listFail = GoogleFonts.lato(
    color: AppColors.redText,
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle secondaryButton = GoogleFonts.lato(
    color: AppColors.greenPrimary,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle primaryButton = GoogleFonts.lato(
    color: AppColors.white,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle questionText = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 14,
    fontWeight: FontWeight.w700,
    height: 1.57,
  );

  static final TextStyle correctAnswerText = GoogleFonts.lato(
    color: AppColors.greenText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.57,
  );

  static final TextStyle incorrectAnswerText = GoogleFonts.lato(
    color: AppColors.redAnswerText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.57,
  );

  static final TextStyle titleBody16 = GoogleFonts.lato(
    color: AppColors.lightGreyText,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle titleScoreCardResult = GoogleFonts.lato(
    color: AppColors.homeHeading,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle textScoreCardResult = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle reportDialogTitle = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  static final TextStyle reportDialogText = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle dialogText = GoogleFonts.lato(
    color: AppColors.blackText,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle textWeight500 = GoogleFonts.lato(
    color: AppColors.blackText,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle textWeight700 = GoogleFonts.lato(
    color: AppColors.blackText,
    fontWeight: FontWeight.w700,
  );
}
