import 'package:flutter/material.dart';

class AppColors {
  static final Color greenButton = Color(0xFF189ab6);
  static final Color purple = Color(0xFF8257E5);
  static final Color white = Color(0xFFFFFFFF);
  static final Color black = Color(0xFF514766);
  static final Color grey = Color(0xFF6E6680);
  static final Color lightGrey = Color(0xFFA6A1B2);
  static final Color border = Color(0xFFE1E1E6);
  static final Color lightScaffold = Color(0xFFDCDCE5);
  static final Color chartSecondary = Color(0xFFE1E6E3);
  static final Color chartPrimary = darkGreen;
  static final Color darkGrey = Color(0xFF545454);
  static final Color lightGrey2 = Color(0xFFC4C4C4);

  //Greens
  static final Color lightGreen = Color(0xFFE1F5EC);
  static final Color green = Color(0xFFB8DBCB);
  static final Color darkGreen = Color(0xFF04D361);

  //Reds
  static final Color lightRed = Color(0xFFF5E9EC);
  static final Color red = Color(0xFFE5C5CF);
  static final Color darkRed = Color(0xFFCC3750);

  //LevelButton
  static final Color levelButtonFacil = Color(0xFFEBEBFC);
  static final Color levelButtonMedio = lightGreen;
  static final Color levelButtonDificil = Color(0xFFF5EFE9);
  static final Color levelButtonPerito = lightRed;

  static final Color levelButtonBorderFacil = Color(0xFFCECEF5);
  static final Color levelButtonBorderMedio = green;
  static final Color levelButtonBorderDificil = Color(0xFFE5D5C5);
  static final Color levelButtonBorderPerito = red;

  static final Color levelButtonTextFacil = Color(0xFF6363DB);
  static final Color levelButtonTextMedio = darkGreen;
  static final Color levelButtonTextDificil = Color(0xFFE8891C);
  static final Color levelButtonTextPerito = darkRed;

  // New UI
  static final Color blackText = Color(0xFF1F1F1F);
  static final Color lightGreyText = Color(0xFF7A8694);
  static final Color darkBlueText = Color(0xFF00397C);
  static final Color scoreCardText = Color(0xFFE1EFFF);
  static final Color homeChartBackground = Color(0xFFCBFFF8);
  static final Color homeChartPrimary = Color(0xFF13A5C6);
  static final Color listChartBackground = Color(0xFFA4CEFF);
  static final Color listChartPrimary = Color(0xFF076CE1);
  static final Color redText = Color(0xFFAF1A10);
  static final Color redAnswerText = Color(0xFFC22930);
  static final Color greenText = Color(0xFF219653);
  static final Color resultFailChartBackground = Color(0xFFFFEDED);
  static final Color resultPassChartBackground = Color(0xFFE2FAEC);
  static final Color lightGreyBorder = Color(0xFFCDD0D3);
  static final Color lightBlueText = Color(0xFF0A6DE1);
  //MULTIPLE APP COLORS
  static final Color homeHeading = Color(0xFF261944);
  static final Color greenPrimary = Color(0xFF4F3195);
  static final Color chartRight = Color(0xff00B9B9);
  static final Color chartWrong = Color(0xffF9705D);
  static final Color chartUnans = Color(0xffB8CCD2);
  static final Color gradientBannerTop = Color(0xff4F3195);
  static final Color gradientBannerBottom = Color(0xff4F3195);
  static final Color quizListBgChart = Color(0xff9DF1FF);
  static final Color quizListProgessChart = Color(0xff00B3D1);
  static final Color resultListBgChartWin = Color(0xffE2FAEC);
  static final Color resultListProgessChartWin = Color(0xff219653);
  static final Color resultListBgChartFail = Color(0xffFFEDED);
  static final Color resultListProgessChartFail = Color(0xffAF3610);
  static final Color resultDetailHeader = Color(0xFF30387D);
  static final Color heading = Color(0xFF30387D);
  static final Color popUpBannerBG = Color(0xff00326C);
}
