class ResultDetail {
  String? qid;
  String? questionType;
  String? question;
  String? description;
  String? status;
  int? correct;
  int? marked;
  List<Options>? options;
  List<String>? selectedOptions;

  ResultDetail(
      {this.qid,
      this.questionType,
      this.question,
      this.description,
      this.status,
      this.options,
      this.selectedOptions,
      this.correct,
      this.marked});

  ResultDetail.fromJson(Map<String, dynamic> json) {
    this.qid = json["qid"];
    this.questionType = json["question_type"];
    this.question = json["question"];
    this.description = json["description"];
    this.status = json["status"];
    this.correct = json["correct"];
    this.marked = json["marked"];
    this.options = json["options"] == null
        ? null
        : (json["options"] as List).map((e) => Options.fromJson(e)).toList();
    this.selectedOptions = json["selected_options"] == null
        ? null
        : List<String>.from(json["selected_options"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["qid"] = this.qid;
    data["question_type"] = this.questionType;
    data["question"] = this.question;
    data["description"] = this.description;
    data["status"] = this.status;
    data["correct"] = this.correct;
    data["marked"] = this.marked;
    data["selected_options"] = this.selectedOptions;
    if (this.options != null)
      data["options"] = this.options?.map((e) => e.toJson()).toList();
    return data;
  }
}

class Options {
  String? oid;
  String? qOption;
  String? score;

  Options({this.oid, this.qOption, this.score});

  Options.fromJson(Map<String, dynamic> json) {
    this.oid = json["oid"];
    this.qOption = json["q_option"];
    this.score = json["score"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["oid"] = this.oid;
    data["q_option"] = this.qOption;
    data["score"] = this.score;
    return data;
  }
}
