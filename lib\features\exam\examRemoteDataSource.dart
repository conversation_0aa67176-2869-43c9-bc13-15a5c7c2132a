import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutterquiz/features/exam/examException.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/exam/api_servies.dart';
import 'package:flutterquiz/utils/apiBodyParameterLabels.dart';
import 'package:flutterquiz/utils/apiUtils.dart';
import 'package:flutterquiz/utils/constants.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';

import 'package:http/http.dart' as http;
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ExamRemoteDataSource {
  Future<dynamic> getExams(
      {required String userId,
      required String languageId,
      required String type,
      required String limit,
      required String offset}) async {
    try {
      //body of post request
      final body = {
        accessValueKey: accessValue,
        userIdKey: userId,
        languageIdKey: languageId,
        typeKey: type, // 1 for today , 2 for completed
        limitKey: limit,
        offsetKey: offset,
      };

      if (languageId.isEmpty) {
        body.remove(languageIdKey);
      }
      if (limit.isEmpty) {
        body.remove(limitKey);
      }

      if (offset.isEmpty) {
        body.remove(offsetKey);
      }

      final response = await http.post(Uri.parse(getExamModuleUrl),
          body: body, headers: await ApiUtils.getHeaders());

      final responseJson = jsonDecode(response.body);

      if (responseJson['error']) {
        throw ExamException(
          errorMessageCode: responseJson['message'].toString() == "102"
              ? type == "1"
                  ? noExamForTodayCode
                  : haveNotCompletedExamCode
              : responseJson['message'],
        );
      }

      return responseJson;
    } on SocketException catch (_) {
      throw ExamException(errorMessageCode: noInternetCode);
    } on ExamException catch (e) {
      throw ExamException(errorMessageCode: e.toString());
    } catch (e) {
      throw ExamException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  Future<List<dynamic>> getQuestionForExam({required String examId}) async {
    try {
      //body of post request
      final body = {
        accessValueKey: accessValue,
        'id': examId,
      };

      final response = await dio.post(getExamModuleQuestionsUrl,
          data: FormData.fromMap(body),
          options: Options(headers: await ApiUtils.getHeaders()));
      final responseJson = response.data;

      if (responseJson['error']) {
        throw ExamException(errorMessageCode: responseJson['message']);
      }
      return responseJson['data'];
    } on SocketException catch (_) {
      throw ExamException(errorMessageCode: noInternetCode);
    } on ExamException catch (e) {
      throw ExamException(errorMessageCode: e.toString());
    } catch (e) {
      throw ExamException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  Future<dynamic> updateExamStatusToInExam({
    required String examModuleId,
    required String userId,
  }) async {
    try {
      //body of post request
      final body = {
        accessValueKey: accessValue,
        examModuleIdKey: examModuleId,
        userIdKey: userId,
      };

      final response = await http.post(Uri.parse(setExamModuleResultUrl),
          body: body, headers: await ApiUtils.getHeaders());
      final responseJson = jsonDecode(response.body);

      if (responseJson['error']) {
        print(responseJson);
        throw ExamException(
            errorMessageCode: responseJson['message'].toString() == "103"
                ? alreadyInExamCode
                : responseJson['message']);
      }
      return responseJson['data'];
    } on SocketException catch (_) {
      throw ExamException(errorMessageCode: noInternetCode);
    } on ExamException catch (e) {
      throw ExamException(errorMessageCode: e.toString());
    } catch (e) {
      throw ExamException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  Future<dynamic> submitExamResult(
      {required String examModuleId,
      required String userId,
      required String totalDuration,
      required List<Map<String, dynamic>> statistics,
      required String obtainedMarks,
      required bool rulesViolated,
      required List<String> capturedQuestionIds,
      required Map data}) async {
    try {
      //body of post request
      final body = {
        accessValueKey: accessValue,
        examModuleIdKey: examModuleId,
        userIdKey: userId,
        statisticsKey: json.encode(statistics),
        totalDurationKey: totalDuration,
        obtainedMarksKey: obtainedMarks,
        rulesViolatedKey: rulesViolated ? "1" : "0",
        capturedQuestionIdsKey: json.encode(capturedQuestionIds),
        'data': jsonEncode(data),
        'platform': Common.platform
      };

      print(body);

      final response = await dio.post(setExamModuleResultUrl,
          data: FormData.fromMap(body),
          options: Options(headers: await ApiUtils.getHeaders()));
      final responseJson = response.data;

      if (responseJson['error']) {
        throw ExamException(errorMessageCode: responseJson['message']);
      }
      return responseJson['data'];
    } on SocketException catch (_) {
      throw ExamException(errorMessageCode: noInternetCode);
    } on ExamException catch (e) {
      throw ExamException(errorMessageCode: e.toString());
    } catch (e) {
      print(e.toString());
      throw ExamException(errorMessageCode: defaultErrorMessageCode);
    }
  }

  // Future<String> checkHasDoingQuiz() async {
  //   print('checkHasDoingQuiz');
  //   SharedPreferences preferences = await SharedPreferences.getInstance();
  //   bool connect = await InternetConnectionChecker().hasConnection;
  //   if (connect) {
  //     var token = preferences.getString('token');
  //     var user = preferences.getString('username');
  //     String info = await GetDeviceInfo() + "- Check Doing Request";
  //     Map data = {'user': user};
  //     try {
  //       final response = await http
  //           .post(Uri.parse(examBaseUrl + "check_doing_quiz"), body: {
  //         "key": appkey,
  //         "token": token,
  //         "info": info,
  //         "data": jsonEncode(data)
  //       });
  //       if (response.statusCode == 200) {
  //         return response.body;
  //       } else {
  //         return "error";
  //       }
  //     } on SocketException catch (_) {
  //       throw ExamException(errorMessageCode: noInternetCode);
  //     } on ExamException catch (e) {
  //       throw ExamException(errorMessageCode: e.toString());
  //     } catch (e) {
  //       print(e.toString());
  //       throw ExamException(errorMessageCode: defaultErrorMessageCode);
  //     }
  //   }
  //   return "";
  // }
}
