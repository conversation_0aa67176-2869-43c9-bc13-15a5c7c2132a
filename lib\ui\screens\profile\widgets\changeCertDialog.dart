import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/profileManagement/cubits/certificateCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainner.dart';
import 'package:flutterquiz/utils/uiUtils.dart';

class ChangeCertDialog extends StatefulWidget {
  const ChangeCertDialog({Key? key}) : super(key: key);

  @override
  State<ChangeCertDialog> createState() => _ChangeCertDialogState();
}

class _ChangeCertDialogState extends State<ChangeCertDialog> {
  @override
  void initState() {
    context
        .read<CertificateCubit>()
        .getAllCertificates(context.read<UserDetailsCubit>().getUserId());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CertificateCubit, CertificateState>(
      bloc: context.read<CertificateCubit>(),
      listener: (context, state) {
        if (state is CertificateFailure) {
          Navigator.of(context).pop();
          UiUtils.setSnackbar(
              AppLocalization.of(context)!.getTranslatedValues("errorDialog")!,
              context,
              false);
        }

        if (state is CertificateChangeSuccess) {
          Navigator.of(context).pushNamedAndRemoveUntil(
              Routes.home, (Route<dynamic> route) => false);
          UiUtils.setSnackbar(
              AppLocalization.of(context)!
                  .getTranslatedValues("changeCertificateSuccess")!,
              context,
              false);
        }
      },
      builder: (context, state) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
          content: state is CertificateLoading
              ? SizedBox(
                  child: CircularProgressContainer(
                    useWhiteLoader: false,
                  ),
                )
              : state is CertificateSuccess
                  ? SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: state.certificates.map((certificate) {
                          return Container(
                            margin: EdgeInsets.symmetric(vertical: 10.0),
                            decoration: BoxDecoration(
                              color: certificate.isCurrent
                                  ? Theme.of(context).primaryColor
                                  : Theme.of(context).colorScheme.secondary,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: ListTile(
                              trailing: certificate.isCurrent
                                  ? Icon(
                                      Icons.check,
                                      color:
                                          Theme.of(context).colorScheme.surface,
                                    )
                                  : SizedBox(),
                              onTap: () {
                                if (certificate.isCurrent == false) {
                                  context
                                      .read<CertificateCubit>()
                                      .changeCertificate(
                                          context
                                              .read<UserDetailsCubit>()
                                              .getUserId(),
                                          certificate.id);
                                } else {
                                  Navigator.of(context).pop();
                                }
                              },
                              title: Text(
                                certificate.name,
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.surface,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    )
                  : const SizedBox(),
        );
      },
    );
  }
}
