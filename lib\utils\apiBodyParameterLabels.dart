final String accessValueKey = 'access_key';
final String userIdKey = 'user_id';
final String firebaseIdKey = 'firebase_id';
final String typeKey = 'type';
final String nameKey = 'name';
final String emailKey = 'email';
final String profileKey = 'profile';
final String mobileKey = 'mobile';
final String fcmIdKey = 'fcm_id';
final String imageKey = 'image';
final String idKey = 'id';
final String languageIdKey = 'language_id';
final String categoryKey = 'category';
final String subCategoryKey = 'subcategory';
final String limitKey = 'limit';
final String offsetKey = 'offset';
final String scoreKey = 'score';
final String coinsKey = 'coins';
final String levelKey = 'level';
final String contestIdKey = 'contest_id';
final String questionAttendedKey = 'questions_attended';
final String correctAnswersKey = 'correct_answers';
final String roomIdKey = 'room_id';
final String roomTypeKey = 'room_type';
final String numberOfQuestionsKey = 'no_of_que';
final String funAndLearnKey = 'fun_n_learn_id';
final String matchIdKey = 'match_id';
final String statusKey = 'status';
final String questionIdKey = 'question_id';
final String messageKey = 'message';
final String friendCodeKey = 'friends_code';
final String typeIdKey = 'type_id';
final String userId1Key = 'user_id1';
final String userId2Key = 'user_id2';
final String winnerIdKey = 'winner_id';
final String isDrawnKey = 'is_drawn';
final String destroyRoomKey = "destroy_match";
final String examModuleIdKey = "exam_module_id";
final String totalDurationKey = "total_duration";
final String statisticsKey = "statistics";
final String obtainedMarksKey = "obtained_marks";
final String rulesViolatedKey = "rules_violated";
final String capturedQuestionIdsKey = "captured_question_ids";
final String titleKey = "title";
final String paymentTypeKey = "payment_type";
final String paymentAddressKey = "payment_address";
final String paymentAmountKey = "payment_amount";
final String coinUsedKey = "coin_used";
final String detailsKey = "details";
final String passwordKey = "password";
final String certificateIdKey = "certificate";
