import 'package:flutter/material.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/ui/styles/colors.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FontSizeDialog extends StatefulWidget {
  final SettingsCubit bloc;
  FontSizeDialog({required this.bloc});
  @override
  _FontSizeDialog createState() => _FontSizeDialog();
}

class _FontSizeDialog extends State<FontSizeDialog> {
  static double textSize = 14;
  Future<void> getFontSize() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var fontsize = preferences.getString('fontsize');
    if (fontsize != null) {
      setState(() {
        textSize = double.parse(fontsize);
      });
    }
  }

  Future<void> setFontSize(double size) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setString('fontsize', size.toString());
  }

  @override
  void initState() {
    super.initState();
    getFontSize();
  }

  @override
  Widget build(BuildContext context) {
    return FittedBox(
        child: AlertDialog(
      backgroundColor: pageBackgroundColor,
      insetPadding: EdgeInsets.symmetric(horizontal: 70, vertical: 300),
      title: Center(
        child: Text(
          AppLocalization.of(context)!.getTranslatedValues("fontSizeLbl")!,
          style: TextStyle(
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
      content: StatefulBuilder(
          builder: (context, state) => FittedBox(
                child: Slider(
                  label: (textSize).toStringAsFixed(0),
                  value: textSize,
                  activeColor: primaryColor,
                  inactiveColor: primaryColor,
                  min: 14,
                  max: 25,
                  divisions: 10,
                  onChanged: (value) {
                    state(() {
                      textSize = value;
                      setFontSize(value);
                      widget.bloc.changeFontSize(textSize);
                      print(textSize);
                    });
                  },
                ),
              )),
      actions: <Widget>[
        TextButton(
          style: TextButton.styleFrom(
            foregroundColor: primaryColor,
            backgroundColor: Theme.of(context).primaryColor,
            elevation: 20,
            shadowColor: backgroundColor.withOpacity(0.8),
            side: BorderSide(width: 1.0, color: primaryColor),
            minimumSize: Size(100, 20),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
          ),
          onPressed: () {
            widget.bloc.changeFontSize(textSize);
            Navigator.of(context).pop();
          },
          child: Text(
            AppLocalization.of(context)!.getTranslatedValues("okayLbl")!,
            style: TextStyle(
              color: Theme.of(context).colorScheme.surface,
            ),
          ),
        )
      ],
    ));
  }
}
