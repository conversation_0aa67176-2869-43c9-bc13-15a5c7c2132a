import 'dart:convert';

class Result {
  final String? quiz_name, result_status;
  final String? total_time;
  final String? percentage_obtained;
  final String? rid;
  final String? quid;
  final String? noq;
  final String? duration;
  final String? pass_percentage;
  final String? endTime;
  Result({
    this.quiz_name,
    this.result_status,
    this.total_time,
    this.percentage_obtained,
    this.rid,
    this.quid,
    this.noq,
    this.duration,
    this.pass_percentage,
    this.endTime
  });

  factory Result.fromMap(Map<String, dynamic> json) => Result(
        quiz_name: json["quiz_name"],
        result_status: json["result_status"],
        total_time: json["total_time"],
        percentage_obtained: json["percentage_obtained"],
        rid: json["rid"],
        quid: json["quid"],
        noq: json["noq"],
        duration: json["duration"],
        pass_percentage: json["pass_percentage"],
        endTime: json["end_time"],
      );
  Map<String, dynamic> toMap() {
    return {
      'quiz_name': quiz_name,
      'result_status': result_status,
      'total_time': total_time,
      'percentage_obtained': percentage_obtained,
      'quid': quid,
      'duration': duration,
      'noq': noq,
      'pass_percentage': pass_percentage,
      'end_time': endTime,
    };
  }

  String toJson() => json.encode(toMap());

  factory Result.fromJson(String source) => Result.fromMap(json.decode(source));
}
