<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="248.69" height="280.227" viewBox="0 0 248.69 280.227">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b38746"/>
      <stop offset="0.389" stop-color="#d9973b"/>
      <stop offset="0.75" stop-color="#ad6c10"/>
      <stop offset="0.858" stop-color="#d3953d"/>
      <stop offset="1" stop-color="#b07f35"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffee76"/>
      <stop offset="1" stop-color="#1d1d1b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#e6a729"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#663d1f"/>
      <stop offset="1" stop-color="#e0c0a8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-15" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f0cc62"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-20" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-27" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-29" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-40" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe878"/>
      <stop offset="1" stop-color="#ed9e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-41" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e0c0a8"/>
      <stop offset="1" stop-color="#663d1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-90" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-91" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-108" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-154" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-164" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-4"/>
  </defs>
  <g id="_06_coins" data-name="06_coins" transform="translate(-163.29 -159.507)">
    <g id="Group_3363" data-name="Group 3363" transform="translate(270.99 159.507)">
      <path id="Path_12040" data-name="Path 12040" d="M204.452,225.1c0,17.164,27.916,31.076,62.351,31.076s62.351-13.912,62.351-31.076l0-13.582h-124.7V225.1Z" transform="translate(-195.145 -47.815)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12041" data-name="Path 12041" d="M238.43,224.289q3.99.26,8.127.264a121.96,121.96,0,0,0,16.371-1.086v13.554a93.056,93.056,0,0,1-16.371,1.08c-8.823,0-23.176-1.684-32.678-4.6V219.93a107.544,107.544,0,0,0,24.551,4.359Z" transform="translate(-174.899 -29.744)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12042" data-name="Path 12042" d="M237.76,234.137c12.1-5.64,19.71-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.069-19.861,22.744V234.137Z" transform="translate(-123.613 -47.693)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12043" data-name="Path 12043" d="M217.256,230.369v13.594c-8.033-5.231-12.8-11.772-12.8-18.867V211.515c.038,7.092,4.8,13.623,12.8,18.854Z" transform="translate(-195.145 -47.815)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12044" data-name="Path 12044" d="M234.137,223.13q3.99.26,8.127.264v13.547a117.033,117.033,0,0,1-26.386-2.911V220.47a112.231,112.231,0,0,0,18.259,2.66Z" transform="translate(-170.606 -28.584)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12045" data-name="Path 12045" d="M274.343,241.992a28.314,28.314,0,0,1-.362,4.655.313.313,0,0,1-.617,0,29.876,29.876,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.356,28.356,0,0,1,.362,4.659Zm8.209-.831a29.114,29.114,0,0,1-.349,4.655.3.3,0,0,1-.589,0,31.217,31.217,0,0,1,0-9.314.3.3,0,0,1,.589,0,29.155,29.155,0,0,1,.349,4.658Zm8.127-1.426a30.857,30.857,0,0,1-.334,4.659.283.283,0,0,1-.563,0,32.663,32.663,0,0,1,0-9.314.283.283,0,0,1,.563,0,30.781,30.781,0,0,1,.334,4.655Zm7.982-2.081a32.232,32.232,0,0,1-.318,4.655.269.269,0,0,1-.538,0,34.264,34.264,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.245,32.245,0,0,1,.318,4.658Zm7.746-2.836a33.85,33.85,0,0,1-.3,4.658.271.271,0,0,1-.255.255.276.276,0,0,1-.255-.255,36.058,36.058,0,0,1,0-9.317.279.279,0,0,1,.255-.258.274.274,0,0,1,.255.258,33.849,33.849,0,0,1,.3,4.658Zm7.362-3.72a35.536,35.536,0,0,1-.286,4.655.243.243,0,1,1-.485,0,38,38,0,0,1,0-9.314.243.243,0,1,1,.485,0,35.516,35.516,0,0,1,.286,4.658Zm6.657-4.863a38.076,38.076,0,0,1-.271,4.655.231.231,0,1,1-.46,0,40.193,40.193,0,0,1,0-9.314.231.231,0,1,1,.46,0,38.091,38.091,0,0,1,.271,4.658Zm-63.975,15.757a28.314,28.314,0,0,0,.362,4.655.313.313,0,0,0,.617,0,29.881,29.881,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.356,28.356,0,0,0-.362,4.659Zm-8.209-.831a29.107,29.107,0,0,0,.349,4.655.3.3,0,0,0,.293.258.3.3,0,0,0,.3-.258,31.206,31.206,0,0,0,0-9.314.3.3,0,0,0-.3-.258.3.3,0,0,0-.293.258,29.148,29.148,0,0,0-.349,4.658Zm-8.127-1.426a30.857,30.857,0,0,0,.334,4.659.283.283,0,0,0,.563,0,32.669,32.669,0,0,0,0-9.314.283.283,0,0,0-.563,0,30.781,30.781,0,0,0-.334,4.655Zm-7.982-2.081a32.232,32.232,0,0,0,.318,4.655.269.269,0,0,0,.538,0,34.271,34.271,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.245,32.245,0,0,0-.318,4.658Zm-7.746-2.836a34.39,34.39,0,0,0,.3,4.658.257.257,0,0,0,.513,0,36.065,36.065,0,0,0,0-9.317.257.257,0,1,0-.513,0,34.389,34.389,0,0,0-.3,4.658Zm-7.362-3.72a35.537,35.537,0,0,0,.286,4.655.243.243,0,1,0,.485,0,38,38,0,0,0,0-9.314.243.243,0,1,0-.485,0,35.516,35.516,0,0,0-.286,4.658Zm-6.657-4.863a38.076,38.076,0,0,0,.271,4.655.231.231,0,1,0,.46,0,40.193,40.193,0,0,0,0-9.314.231.231,0,1,0-.46,0,38.091,38.091,0,0,0-.271,4.658ZM264.7,242.272a26.943,26.943,0,0,0,.381,4.658.33.33,0,0,0,.642,0,28.685,28.685,0,0,0,0-9.317.329.329,0,0,0-.642,0,26.943,26.943,0,0,0-.381,4.658Zm-59.59-22.36a39.564,39.564,0,0,0,.255,4.655c.016.151.107.258.214.258s.2-.107.217-.258a42.659,42.659,0,0,0,0-9.314c-.019-.151-.107-.258-.217-.258s-.2.107-.214.258a39.621,39.621,0,0,0-.255,4.658Zm120.584,0a39.578,39.578,0,0,1-.255,4.655c-.016.151-.107.258-.214.258s-.2-.107-.217-.258a42.669,42.669,0,0,1,0-9.314c.016-.151.107-.258.217-.258s.2.107.214.258a39.635,39.635,0,0,1,.255,4.658Z" transform="translate(-193.74 -40.342)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12046" data-name="Path 12046" d="M204.616,233.255c0,17.419,27.469,31.542,61.35,31.542s61.35-14.123,61.35-31.542-27.466-31.539-61.35-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-194.792 -68.86)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12047" data-name="Path 12047" d="M204.452,232.773c0,17.227,27.916,31.2,62.351,31.2s62.351-13.969,62.351-31.2-27.916-31.2-62.351-31.2-62.351,13.966-62.351,31.2Z" transform="translate(-195.145 -69.158)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12048" data-name="Path 12048" d="M206.325,229.034c-.4,15.379,24.551,28.5,55.734,29.323s56.792-10.988,57.195-26.364S294.7,203.485,263.52,202.669s-56.792,10.988-57.195,26.364Z" transform="translate(-191.132 -66.898)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12049" data-name="Path 12049" d="M206.448,230.629c0,15.492,25.1,28.051,56.068,28.051s56.068-12.559,56.068-28.051-25.1-28.054-56.068-28.054-56.068,12.562-56.068,28.054Z" transform="translate(-190.858 -67.015)" fill="#9e9e9e" fill-rule="evenodd"/>
      <path id="Path_12050" data-name="Path 12050" d="M208.132,230.348c0,15.319,24.822,27.737,55.438,27.737s55.435-12.417,55.435-27.737-24.819-27.736-55.435-27.736-55.438,12.417-55.438,27.736Z" transform="translate(-187.241 -66.935)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12051" data-name="Path 12051" d="M318.48,232.361c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.392,14.392,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.068,12.562,56.068,28.054a14.392,14.392,0,0,1-.1,1.731Z" transform="translate(-190.858 -67.015)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12052" data-name="Path 12052" d="M202.973,220.423c0,17.161,27.916,31.076,62.35,31.076s62.351-13.915,62.351-31.076l0-13.582h-124.7v13.582Z" transform="translate(-198.321 -57.853)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12053" data-name="Path 12053" d="M236.951,219.615q3.99.26,8.127.261a121.961,121.961,0,0,0,16.371-1.086v13.553a92.985,92.985,0,0,1-16.371,1.083c-8.823,0-23.176-1.684-32.678-4.6V215.256a107.749,107.749,0,0,0,24.551,4.359Z" transform="translate(-178.076 -39.781)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12054" data-name="Path 12054" d="M236.281,229.465c12.1-5.641,19.71-13.66,19.858-22.568l0,13.406c0,8.977-7.642,17.069-19.861,22.741V229.465Z" transform="translate(-126.789 -57.733)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12055" data-name="Path 12055" d="M215.777,225.7v13.594c-8.033-5.231-12.8-11.772-12.8-18.867V206.841c.041,7.088,4.806,13.623,12.8,18.854Z" transform="translate(-198.321 -57.853)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12056" data-name="Path 12056" d="M232.658,218.458q3.99.26,8.127.261v13.55a116.867,116.867,0,0,1-26.386-2.915V215.8a112.574,112.574,0,0,0,18.259,2.663Z" transform="translate(-173.783 -38.624)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12057" data-name="Path 12057" d="M272.868,237.315a28.362,28.362,0,0,1-.365,4.658.313.313,0,0,1-.617,0,29.9,29.9,0,0,1,0-9.317.314.314,0,0,1,.617,0,28.361,28.361,0,0,1,.365,4.658Zm8.206-.831a29.575,29.575,0,0,1-.346,4.658.3.3,0,0,1-.592,0,31.206,31.206,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.5,29.5,0,0,1,.346,4.655Zm8.127-1.423a30.782,30.782,0,0,1-.334,4.655.283.283,0,0,1-.563,0,32.669,32.669,0,0,1,0-9.314.283.283,0,0,1,.563,0,30.857,30.857,0,0,1,.334,4.658Zm7.982-2.084a32.276,32.276,0,0,1-.318,4.658.283.283,0,0,1-.268.258.286.286,0,0,1-.271-.258,34.63,34.63,0,0,1,0-9.317.283.283,0,0,1,.271-.255.28.28,0,0,1,.268.255,32.278,32.278,0,0,1,.318,4.658Zm7.746-2.833a34.341,34.341,0,0,1-.3,4.655.257.257,0,1,1-.513,0,36.04,36.04,0,0,1,0-9.314.257.257,0,1,1,.513,0,34.355,34.355,0,0,1,.3,4.658Zm7.362-3.724a35.6,35.6,0,0,1-.286,4.658.243.243,0,1,1-.485,0,38.024,38.024,0,0,1,0-9.317.243.243,0,1,1,.485,0,35.6,35.6,0,0,1,.286,4.658Zm6.657-4.863a37.518,37.518,0,0,1-.271,4.658.231.231,0,1,1-.46,0,40.22,40.22,0,0,1,0-9.317.231.231,0,1,1,.46,0,37.518,37.518,0,0,1,.271,4.658Zm-63.975,15.757a28.383,28.383,0,0,0,.362,4.658.313.313,0,0,0,.617,0,29.9,29.9,0,0,0,0-9.317.314.314,0,0,0-.617,0,28.383,28.383,0,0,0-.362,4.658Zm-8.209-.831a29.184,29.184,0,0,0,.349,4.658.3.3,0,0,0,.589,0,31.211,31.211,0,0,0,0-9.314.3.3,0,0,0-.589,0,29.113,29.113,0,0,0-.349,4.655Zm-8.127-1.423a30.782,30.782,0,0,0,.334,4.655.283.283,0,0,0,.563,0,32.663,32.663,0,0,0,0-9.314.283.283,0,0,0-.563,0,30.857,30.857,0,0,0-.334,4.658Zm-7.982-2.084a32.276,32.276,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.287,34.287,0,0,0,0-9.317.269.269,0,0,0-.538,0,32.278,32.278,0,0,0-.318,4.658Zm-7.746-2.833a33.8,33.8,0,0,0,.3,4.655.272.272,0,0,0,.255.258.276.276,0,0,0,.255-.258,36.033,36.033,0,0,0,0-9.314.276.276,0,0,0-.255-.258.272.272,0,0,0-.255.258,33.815,33.815,0,0,0-.3,4.658Zm-7.362-3.724a35.587,35.587,0,0,0,.286,4.658.27.27,0,0,0,.242.255.265.265,0,0,0,.242-.255,38.024,38.024,0,0,0,0-9.317.268.268,0,0,0-.242-.258.273.273,0,0,0-.242.258,35.586,35.586,0,0,0-.286,4.658Zm-6.657-4.863a38.131,38.131,0,0,0,.271,4.658.231.231,0,1,0,.46,0,40.22,40.22,0,0,0,0-9.317.231.231,0,1,0-.46,0,38.131,38.131,0,0,0-.271,4.658Zm54.327,16.04a26.911,26.911,0,0,0,.381,4.655.329.329,0,0,0,.642,0,28.661,28.661,0,0,0,0-9.314.329.329,0,0,0-.642,0,26.923,26.923,0,0,0-.381,4.658Zm-59.59-22.364a39.674,39.674,0,0,0,.255,4.658c.019.151.107.258.217.258s.2-.107.214-.258a42.659,42.659,0,0,0,0-9.314c-.016-.151-.107-.258-.214-.258s-.2.107-.217.258a39.578,39.578,0,0,0-.255,4.655Zm120.584,0a39.674,39.674,0,0,1-.255,4.658c-.016.151-.107.258-.214.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.151.107-.258.217-.258s.2.107.214.258a39.578,39.578,0,0,1,.255,4.655Z" transform="translate(-196.916 -50.38)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12058" data-name="Path 12058" d="M203.137,228.583c0,17.419,27.469,31.539,61.35,31.539s61.35-14.12,61.35-31.539-27.466-31.542-61.35-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-197.969 -78.9)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12059" data-name="Path 12059" d="M202.973,228.1c0,17.23,27.916,31.2,62.35,31.2s62.351-13.966,62.351-31.2S299.758,196.9,265.324,196.9s-62.35,13.966-62.35,31.193Z" transform="translate(-198.321 -79.196)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12060" data-name="Path 12060" d="M204.846,224.362c-.4,15.376,24.551,28.5,55.737,29.32s56.789-10.985,57.192-26.364-24.551-28.5-55.734-29.323-56.792,10.988-57.195,26.367Z" transform="translate(-194.308 -76.938)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12061" data-name="Path 12061" d="M204.969,225.952c0,15.5,25.1,28.054,56.068,28.054s56.071-12.559,56.071-28.054S292,197.9,261.037,197.9s-56.068,12.559-56.068,28.051Z" transform="translate(-194.034 -77.053)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12062" data-name="Path 12062" d="M317,227.687c-1.788-14.687-26.16-26.323-55.964-26.323S206.861,213,205.073,227.687a14.445,14.445,0,0,1-.1-1.734c0-15.492,25.1-28.051,56.068-28.051s56.071,12.559,56.071,28.051a14.459,14.459,0,0,1-.107,1.734Z" transform="translate(-194.034 -77.053)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12063" data-name="Path 12063" d="M206.671,215.748c0,17.164,27.913,31.076,62.347,31.076s62.35-13.912,62.35-31.076l0-13.582H206.671v13.582Z" transform="translate(-190.379 -67.893)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12064" data-name="Path 12064" d="M240.648,214.94q3.99.26,8.127.264a121.935,121.935,0,0,0,16.371-1.086v13.553a93,93,0,0,1-16.371,1.08c-8.823,0-23.176-1.684-32.678-4.6V210.581a107.576,107.576,0,0,0,24.551,4.359Z" transform="translate(-170.136 -49.821)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-18)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12065" data-name="Path 12065" d="M239.978,224.788c12.1-5.637,19.713-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.073-19.861,22.744V224.788Z" transform="translate(-118.85 -67.771)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12066" data-name="Path 12066" d="M219.472,221.02v13.6c-8.03-5.231-12.8-11.775-12.8-18.87V202.166c.038,7.092,4.8,13.626,12.8,18.854Z" transform="translate(-190.379 -67.893)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12067" data-name="Path 12067" d="M236.355,213.781q3.99.26,8.127.264v13.547a117.076,117.076,0,0,1-26.386-2.911v-13.56a112.248,112.248,0,0,0,18.259,2.66Z" transform="translate(-165.843 -48.662)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12068" data-name="Path 12068" d="M276.565,232.643a28.279,28.279,0,0,1-.365,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.348,28.348,0,0,1,.365,4.659Zm8.206-.831a29.567,29.567,0,0,1-.346,4.658.3.3,0,0,1-.592,0,31.232,31.232,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.568,29.568,0,0,1,.346,4.658Zm8.127-1.426a30.817,30.817,0,0,1-.334,4.658.288.288,0,0,1-.28.258.291.291,0,0,1-.283-.258,32.669,32.669,0,0,1,0-9.314.291.291,0,0,1,.283-.258.288.288,0,0,1,.28.258,30.774,30.774,0,0,1,.334,4.655Zm7.982-2.081a32.209,32.209,0,0,1-.318,4.655.268.268,0,0,1-.535,0,34.264,34.264,0,0,1,0-9.314.268.268,0,0,1,.535,0,32.254,32.254,0,0,1,.318,4.658Zm7.749-2.836a33.848,33.848,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.04,36.04,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.767,33.767,0,0,1,.3,4.655Zm7.359-3.72a35.536,35.536,0,0,1-.286,4.655.243.243,0,1,1-.485,0,38,38,0,0,1,0-9.314.243.243,0,1,1,.485,0,35.55,35.55,0,0,1,.286,4.658Zm6.657-4.863a37.427,37.427,0,0,1-.271,4.655c-.019.151-.113.258-.23.258a.263.263,0,0,1-.23-.258,40.193,40.193,0,0,1,0-9.314.263.263,0,0,1,.23-.258c.116,0,.211.107.23.258a37.48,37.48,0,0,1,.271,4.658Zm-63.975,15.757a28.307,28.307,0,0,0,.362,4.655.314.314,0,0,0,.312.258.311.311,0,0,0,.308-.258,30.14,30.14,0,0,0,0-9.314.311.311,0,0,0-.308-.258.314.314,0,0,0-.312.258,28.377,28.377,0,0,0-.362,4.659Zm-8.209-.831a29.551,29.551,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.51,31.51,0,0,0,0-9.317.3.3,0,0,0-.592,0,29.551,29.551,0,0,0-.349,4.658Zm-8.127-1.426a30.825,30.825,0,0,0,.334,4.658.293.293,0,0,0,.283.258.287.287,0,0,0,.28-.258,32.663,32.663,0,0,0,0-9.314.287.287,0,0,0-.28-.258.293.293,0,0,0-.283.258,30.783,30.783,0,0,0-.334,4.655Zm-7.982-2.081a32.2,32.2,0,0,0,.318,4.655.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.245,32.245,0,0,0-.318,4.658Zm-7.746-2.836a33.848,33.848,0,0,0,.3,4.658.255.255,0,1,0,.51,0,36.033,36.033,0,0,0,0-9.314.255.255,0,1,0-.51,0,33.767,33.767,0,0,0-.3,4.655Zm-7.362-3.72a36.1,36.1,0,0,0,.286,4.655.271.271,0,0,0,.242.258.266.266,0,0,0,.242-.258,38,38,0,0,0,0-9.314.266.266,0,0,0-.242-.258.271.271,0,0,0-.242.258,36.11,36.11,0,0,0-.286,4.658Zm-6.657-4.863a38.037,38.037,0,0,0,.271,4.655.263.263,0,0,0,.23.258c.116,0,.211-.107.23-.258a40.193,40.193,0,0,0,0-9.314c-.019-.151-.113-.258-.23-.258a.263.263,0,0,0-.23.258,38.091,38.091,0,0,0-.271,4.658Zm54.327,16.037a27.262,27.262,0,0,0,.381,4.658.32.32,0,0,0,.321.258.326.326,0,0,0,.324-.258,28.918,28.918,0,0,0,0-9.317.323.323,0,0,0-.324-.255.317.317,0,0,0-.321.255,27.261,27.261,0,0,0-.381,4.658Zm-59.59-22.36a40.361,40.361,0,0,0,.255,4.658c.019.148.107.255.217.255s.2-.107.214-.255a42.688,42.688,0,0,0,0-9.317c-.016-.148-.107-.258-.214-.258s-.2.11-.217.258a40.359,40.359,0,0,0-.255,4.658Zm120.587,0a39.628,39.628,0,0,1-.258,4.658c-.016.148-.107.255-.214.255s-.2-.107-.217-.255a42.688,42.688,0,0,1,0-9.317c.019-.148.11-.258.217-.258s.2.11.214.258a39.627,39.627,0,0,1,.258,4.658Z" transform="translate(-188.977 -60.42)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12069" data-name="Path 12069" d="M206.835,223.906c0,17.422,27.466,31.542,61.346,31.542s61.35-14.12,61.35-31.542-27.466-31.539-61.35-31.539-61.346,14.12-61.346,31.539Z" transform="translate(-190.027 -88.937)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12070" data-name="Path 12070" d="M206.671,223.424c0,17.227,27.913,31.2,62.347,31.2s62.35-13.969,62.35-31.2-27.916-31.2-62.35-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-190.379 -89.236)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12071" data-name="Path 12071" d="M210.291,221.441c0,15.316,24.819,27.737,55.435,27.737s55.435-12.42,55.435-27.737S296.343,193.7,265.726,193.7s-55.435,12.417-55.435,27.737Z" transform="translate(-182.605 -86.066)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12072" data-name="Path 12072" d="M208.544,219.684c-.4,15.379,24.548,28.5,55.734,29.323s56.789-10.988,57.192-26.364-24.551-28.508-55.734-29.323-56.789,10.988-57.192,26.364Z" transform="translate(-186.366 -86.976)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12073" data-name="Path 12073" d="M208.666,221.28c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051-25.105-28.054-56.071-28.054-56.068,12.562-56.068,28.054Z" transform="translate(-186.095 -87.093)" fill="#9e9e9e" fill-rule="evenodd"/>
      <path id="Path_12074" data-name="Path 12074" d="M320.7,223.012c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.392,14.392,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a13.781,13.781,0,0,1-.107,1.731Z" transform="translate(-186.095 -87.093)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12075" data-name="Path 12075" d="M205.192,211.074c0,17.164,27.913,31.076,62.347,31.076s62.351-13.912,62.351-31.076l0-13.582H205.192v13.582Z" transform="translate(-193.555 -77.931)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12076" data-name="Path 12076" d="M239.167,210.266q3.99.26,8.127.261a121.935,121.935,0,0,0,16.371-1.086V223a92.935,92.935,0,0,1-16.371,1.083c-8.823,0-23.176-1.684-32.675-4.6V205.907a107.692,107.692,0,0,0,24.548,4.359Z" transform="translate(-173.31 -59.859)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12077" data-name="Path 12077" d="M238.5,220.116c12.1-5.641,19.713-13.661,19.858-22.568l0,13.406c0,8.98-7.642,17.069-19.861,22.741V220.116Z" transform="translate(-122.026 -77.811)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12078" data-name="Path 12078" d="M217.993,216.346V229.94c-8.03-5.231-12.8-11.772-12.8-18.867V197.492c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-193.555 -77.931)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12079" data-name="Path 12079" d="M234.876,209.107q3.99.26,8.127.261v13.55A116.887,116.887,0,0,1,216.617,220V206.447a112.989,112.989,0,0,0,18.259,2.66Z" transform="translate(-169.019 -58.699)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12080" data-name="Path 12080" d="M275.086,227.966a27.989,27.989,0,0,1-.365,4.658.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,27.949,27.949,0,0,1,.365,4.655Zm8.209-.831a29.522,29.522,0,0,1-.349,4.658.3.3,0,0,1-.592,0,31.212,31.212,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.48,29.48,0,0,1,.349,4.655Zm8.124-1.423a30.374,30.374,0,0,1-.334,4.655.288.288,0,0,1-.28.258.293.293,0,0,1-.283-.258,32.668,32.668,0,0,1,0-9.314.3.3,0,0,1,.283-.258.29.29,0,0,1,.28.258,30.447,30.447,0,0,1,.334,4.658Zm7.982-2.084a32.306,32.306,0,0,1-.315,4.658.283.283,0,0,1-.271.258.281.281,0,0,1-.268-.258,34.264,34.264,0,0,1,0-9.314.281.281,0,0,1,.268-.258.283.283,0,0,1,.271.258,32.23,32.23,0,0,1,.315,4.655Zm7.749-2.833a33.775,33.775,0,0,1-.3,4.655.257.257,0,1,1-.513,0,36.033,36.033,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.859,33.859,0,0,1,.3,4.658Zm7.359-3.724a35.577,35.577,0,0,1-.286,4.658.268.268,0,0,1-.242.258.273.273,0,0,1-.242-.258,38,38,0,0,1,0-9.314.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.491,35.491,0,0,1,.286,4.655Zm6.657-4.863a37.48,37.48,0,0,1-.271,4.658.263.263,0,0,1-.23.258c-.113,0-.208-.11-.227-.258a39.73,39.73,0,0,1,0-9.314c.019-.151.113-.258.227-.258s.211.107.23.258a37.427,37.427,0,0,1,.271,4.655Zm-63.975,15.757a27.99,27.99,0,0,0,.365,4.658.313.313,0,0,0,.617,0,30.135,30.135,0,0,0,0-9.314.313.313,0,0,0-.617,0,27.95,27.95,0,0,0-.365,4.655Zm-8.209-.831a29.523,29.523,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.489,31.489,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.481,29.481,0,0,0-.349,4.655Zm-8.127-1.423a30.773,30.773,0,0,0,.334,4.655.284.284,0,0,0,.567,0,32.979,32.979,0,0,0,0-9.314.284.284,0,0,0-.567,0,30.847,30.847,0,0,0-.334,4.658Zm-7.982-2.084a32.277,32.277,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.2,32.2,0,0,0-.318,4.655Zm-7.746-2.833a33.766,33.766,0,0,0,.3,4.655.276.276,0,0,0,.255.258.272.272,0,0,0,.255-.258,36.04,36.04,0,0,0,0-9.314.272.272,0,0,0-.255-.258.277.277,0,0,0-.255.258,33.849,33.849,0,0,0-.3,4.658Zm-7.362-3.724a36.148,36.148,0,0,0,.286,4.658.244.244,0,1,0,.488,0,38.006,38.006,0,0,0,0-9.314.244.244,0,1,0-.488,0,36.06,36.06,0,0,0-.286,4.655Zm-6.654-4.863a37.48,37.48,0,0,0,.271,4.658c.019.148.113.258.227.258a.263.263,0,0,0,.23-.258,40.184,40.184,0,0,0,0-9.314c-.019-.151-.113-.258-.23-.258s-.208.107-.227.258a37.427,37.427,0,0,0-.271,4.655Zm54.324,16.04a27.223,27.223,0,0,0,.381,4.655.32.32,0,0,0,.321.258.323.323,0,0,0,.324-.258,28.894,28.894,0,0,0,0-9.314.323.323,0,0,0-.324-.258.32.32,0,0,0-.321.258,27.234,27.234,0,0,0-.381,4.658Zm-59.59-22.364a40.334,40.334,0,0,0,.255,4.658c.019.151.11.258.217.258s.2-.107.214-.258a42.137,42.137,0,0,0,0-9.314c-.016-.151-.107-.258-.214-.258s-.2.107-.217.258a40.275,40.275,0,0,0-.255,4.655Zm120.587,0a40.3,40.3,0,0,1-.255,4.658c-.019.151-.11.258-.217.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.151.11-.258.217-.258s.2.107.217.258a40.246,40.246,0,0,1,.255,4.655Z" transform="translate(-192.153 -70.457)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12081" data-name="Path 12081" d="M205.356,219.234c0,17.419,27.466,31.539,61.35,31.539s61.346-14.12,61.346-31.539-27.466-31.542-61.346-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-193.203 -98.977)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12082" data-name="Path 12082" d="M205.192,218.75c0,17.227,27.913,31.193,62.347,31.193s62.351-13.966,62.351-31.193-27.913-31.2-62.351-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-193.555 -99.274)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12083" data-name="Path 12083" d="M207.065,215.011c-.4,15.376,24.551,28.5,55.734,29.32s56.789-10.985,57.192-26.364-24.551-28.5-55.734-29.32-56.789,10.985-57.192,26.364Z" transform="translate(-189.543 -97.014)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12084" data-name="Path 12084" d="M207.187,216.606c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051-25.1-28.054-56.071-28.054-56.068,12.559-56.068,28.054Z" transform="translate(-189.271 -97.13)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12085" data-name="Path 12085" d="M319.219,218.338c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.445,14.445,0,0,1-.1-1.731c0-15.5,25.1-28.054,56.068-28.054s56.071,12.559,56.071,28.054a13.819,13.819,0,0,1-.107,1.731Z" transform="translate(-189.271 -97.13)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12086" data-name="Path 12086" d="M208.132,216.8c0,15.319,24.822,27.737,55.438,27.737s55.435-12.417,55.435-27.737-24.819-27.737-55.435-27.737-55.438,12.417-55.438,27.737Z" transform="translate(-187.241 -96.027)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12087" data-name="Path 12087" d="M202.973,206.4c0,17.164,27.916,31.079,62.35,31.079s62.351-13.915,62.351-31.079l0-13.582h-124.7V206.4Z" transform="translate(-198.321 -87.971)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12088" data-name="Path 12088" d="M236.951,205.591q3.99.264,8.127.264a121.971,121.971,0,0,0,16.371-1.086v13.553a92.986,92.986,0,0,1-16.371,1.083c-8.823,0-23.176-1.687-32.678-4.608V201.232a107.41,107.41,0,0,0,24.551,4.359Z" transform="translate(-178.076 -69.899)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12089" data-name="Path 12089" d="M236.281,215.439c12.1-5.637,19.71-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.073-19.861,22.744V215.439Z" transform="translate(-126.789 -87.849)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12090" data-name="Path 12090" d="M215.777,211.671v13.6c-8.033-5.231-12.8-11.772-12.8-18.87V192.817c.041,7.092,4.806,13.626,12.8,18.854Z" transform="translate(-198.321 -87.971)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12091" data-name="Path 12091" d="M232.658,204.432q3.99.264,8.127.264v13.55a116.87,116.87,0,0,1-26.386-2.915v-13.56a112.246,112.246,0,0,0,18.259,2.66Z" transform="translate(-173.783 -68.739)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12092" data-name="Path 12092" d="M272.868,223.294a28.293,28.293,0,0,1-.365,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.362,28.362,0,0,1,.365,4.658Zm8.206-.831a29.575,29.575,0,0,1-.346,4.658.3.3,0,0,1-.592,0,31.227,31.227,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.575,29.575,0,0,1,.346,4.658Zm8.127-1.426a30.825,30.825,0,0,1-.334,4.658.283.283,0,0,1-.563,0,32.669,32.669,0,0,1,0-9.314.283.283,0,0,1,.563,0,30.813,30.813,0,0,1,.334,4.655Zm7.982-2.081a32.277,32.277,0,0,1-.318,4.658.28.28,0,0,1-.268.255.283.283,0,0,1-.271-.255,34.63,34.63,0,0,1,0-9.317.283.283,0,0,1,.271-.258.281.281,0,0,1,.268.258,32.245,32.245,0,0,1,.318,4.658Zm7.746-2.836a34.355,34.355,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.04,36.04,0,0,1,0-9.314.257.257,0,1,1,.513,0,34.306,34.306,0,0,1,.3,4.655Zm7.362-3.72a35.512,35.512,0,0,1-.286,4.655.243.243,0,1,1-.485,0,38,38,0,0,1,0-9.314.243.243,0,1,1,.485,0,35.563,35.563,0,0,1,.286,4.658Zm6.657-4.863a37.427,37.427,0,0,1-.271,4.655c-.019.151-.113.258-.23.258a.263.263,0,0,1-.23-.258,40.193,40.193,0,0,1,0-9.314.263.263,0,0,1,.23-.258c.116,0,.211.107.23.258a37.48,37.48,0,0,1,.271,4.658Zm-63.975,15.757a28.315,28.315,0,0,0,.362,4.655.313.313,0,0,0,.617,0,29.881,29.881,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.384,28.384,0,0,0-.362,4.658Zm-8.209-.831a29.184,29.184,0,0,0,.349,4.658.3.3,0,0,0,.589,0,31.232,31.232,0,0,0,0-9.317.3.3,0,0,0-.589,0,29.184,29.184,0,0,0-.349,4.658Zm-8.127-1.426a30.825,30.825,0,0,0,.334,4.658.283.283,0,0,0,.563,0,32.663,32.663,0,0,0,0-9.314.283.283,0,0,0-.563,0,30.813,30.813,0,0,0-.334,4.655Zm-7.982-2.081a32.277,32.277,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.287,34.287,0,0,0,0-9.317.269.269,0,0,0-.538,0,32.245,32.245,0,0,0-.318,4.658Zm-7.746-2.836a33.815,33.815,0,0,0,.3,4.658.274.274,0,0,0,.255.258.279.279,0,0,0,.255-.258,36.033,36.033,0,0,0,0-9.314.276.276,0,0,0-.255-.258.272.272,0,0,0-.255.258,33.767,33.767,0,0,0-.3,4.655Zm-7.362-3.72a35.5,35.5,0,0,0,.286,4.655.271.271,0,0,0,.242.258.266.266,0,0,0,.242-.258,38,38,0,0,0,0-9.314.266.266,0,0,0-.242-.258.27.27,0,0,0-.242.258,35.552,35.552,0,0,0-.286,4.658Zm-6.657-4.863a38.037,38.037,0,0,0,.271,4.655.231.231,0,1,0,.46,0,40.193,40.193,0,0,0,0-9.314.231.231,0,1,0-.46,0,38.091,38.091,0,0,0-.271,4.658Zm54.327,16.037a26.949,26.949,0,0,0,.381,4.658.329.329,0,0,0,.642,0,28.661,28.661,0,0,0,0-9.314.329.329,0,0,0-.642,0,26.886,26.886,0,0,0-.381,4.655Zm-59.59-22.36a39.675,39.675,0,0,0,.255,4.658c.019.148.107.255.217.255s.2-.107.214-.255a42.688,42.688,0,0,0,0-9.317c-.016-.148-.107-.258-.214-.258s-.2.11-.217.258a39.673,39.673,0,0,0-.255,4.658Zm120.584,0a39.675,39.675,0,0,1-.255,4.658c-.016.148-.107.255-.214.255s-.2-.107-.217-.255a42.688,42.688,0,0,1,0-9.317c.019-.148.107-.258.217-.258s.2.11.214.258a39.673,39.673,0,0,1,.255,4.658Z" transform="translate(-196.916 -80.497)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12093" data-name="Path 12093" d="M203.137,214.557c0,17.422,27.469,31.542,61.35,31.542s61.35-14.12,61.35-31.542-27.466-31.539-61.35-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-197.969 -109.015)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12094" data-name="Path 12094" d="M202.973,214.075c0,17.23,27.916,31.2,62.35,31.2s62.351-13.966,62.351-31.2-27.916-31.2-62.351-31.2-62.35,13.966-62.35,31.2Z" transform="translate(-198.321 -109.314)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12095" data-name="Path 12095" d="M204.846,210.335c-.4,15.379,24.551,28.508,55.737,29.323s56.789-10.988,57.192-26.364-24.551-28.5-55.734-29.323-56.792,10.988-57.195,26.364Z" transform="translate(-194.308 -107.054)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12096" data-name="Path 12096" d="M204.969,211.931c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051S292,183.877,261.037,183.877s-56.068,12.562-56.068,28.054Z" transform="translate(-194.034 -107.17)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_12097" data-name="Path 12097" d="M204.459,212.349c0,15.319,24.819,27.737,55.435,27.737s55.435-12.417,55.435-27.737-24.819-27.737-55.435-27.737-55.435,12.417-55.435,27.737Z" transform="translate(-195.13 -105.592)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12098" data-name="Path 12098" d="M317,213.663c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.393,14.393,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a14.406,14.406,0,0,1-.107,1.731Z" transform="translate(-194.034 -107.17)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12099" data-name="Path 12099" d="M205.192,201.725c0,17.164,27.913,31.076,62.347,31.076s62.351-13.912,62.351-31.076l0-13.582H205.192v13.582Z" transform="translate(-193.555 -98.009)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12100" data-name="Path 12100" d="M239.167,200.917q3.99.26,8.127.261a121.935,121.935,0,0,0,16.371-1.086v13.553a92.935,92.935,0,0,1-16.371,1.083c-8.823,0-23.176-1.684-32.675-4.6V196.558a107.692,107.692,0,0,0,24.548,4.359Z" transform="translate(-173.31 -79.937)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12101" data-name="Path 12101" d="M238.5,210.767c12.1-5.641,19.713-13.657,19.858-22.568l0,13.406c0,8.98-7.642,17.069-19.861,22.745V210.767Z" transform="translate(-122.026 -97.889)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12102" data-name="Path 12102" d="M217.993,207v13.594c-8.03-5.231-12.8-11.772-12.8-18.867V188.143c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-193.555 -98.009)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12103" data-name="Path 12103" d="M234.876,199.758q3.99.26,8.127.261v13.55a116.891,116.891,0,0,1-26.386-2.915V197.1a112.29,112.29,0,0,0,18.259,2.66Z" transform="translate(-169.019 -78.777)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12104" data-name="Path 12104" d="M275.086,218.617a27.989,27.989,0,0,1-.365,4.658.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,27.949,27.949,0,0,1,.365,4.655Zm8.209-.831a29.521,29.521,0,0,1-.349,4.658.3.3,0,0,1-.592,0,31.211,31.211,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.48,29.48,0,0,1,.349,4.655Zm8.124-1.423a30.447,30.447,0,0,1-.334,4.658.287.287,0,0,1-.28.255.293.293,0,0,1-.283-.255,32.69,32.69,0,0,1,0-9.317.3.3,0,0,1,.283-.258.29.29,0,0,1,.28.258,30.447,30.447,0,0,1,.334,4.658Zm7.982-2.084a32.274,32.274,0,0,1-.315,4.658.283.283,0,0,1-.271.258.281.281,0,0,1-.268-.258,34.264,34.264,0,0,1,0-9.314.281.281,0,0,1,.268-.258.283.283,0,0,1,.271.258,32.23,32.23,0,0,1,.315,4.655Zm7.749-2.833a33.776,33.776,0,0,1-.3,4.655.257.257,0,1,1-.513,0,36.033,36.033,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.859,33.859,0,0,1,.3,4.658Zm7.359-3.724a35.577,35.577,0,0,1-.286,4.658.266.266,0,0,1-.242.258.271.271,0,0,1-.242-.258,38,38,0,0,1,0-9.314.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.491,35.491,0,0,1,.286,4.655Zm6.657-4.863a37.48,37.48,0,0,1-.271,4.658c-.019.151-.113.258-.23.258s-.208-.107-.227-.258a39.73,39.73,0,0,1,0-9.314c.019-.151.113-.258.227-.258s.211.107.23.258a37.463,37.463,0,0,1,.271,4.655Zm-63.975,15.757a27.99,27.99,0,0,0,.365,4.658.313.313,0,0,0,.617,0,30.135,30.135,0,0,0,0-9.314.313.313,0,0,0-.617,0,27.95,27.95,0,0,0-.365,4.655Zm-8.209-.831a29.521,29.521,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.489,31.489,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.481,29.481,0,0,0-.349,4.655Zm-8.127-1.423a30.847,30.847,0,0,0,.334,4.658.285.285,0,0,0,.567,0,33,33,0,0,0,0-9.317.284.284,0,0,0-.567,0,30.847,30.847,0,0,0-.334,4.658Zm-7.982-2.084a32.245,32.245,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.2,32.2,0,0,0-.318,4.655Zm-7.746-2.833a33.766,33.766,0,0,0,.3,4.655.276.276,0,0,0,.255.258.272.272,0,0,0,.255-.258,36.04,36.04,0,0,0,0-9.314.274.274,0,0,0-.255-.258.279.279,0,0,0-.255.258,33.849,33.849,0,0,0-.3,4.658Zm-7.362-3.724a36.148,36.148,0,0,0,.286,4.658.244.244,0,1,0,.488,0,38.006,38.006,0,0,0,0-9.314.244.244,0,1,0-.488,0,36.06,36.06,0,0,0-.286,4.655Zm-6.654-4.863a37.48,37.48,0,0,0,.271,4.658c.019.151.113.258.227.258s.211-.107.23-.258a40.183,40.183,0,0,0,0-9.314c-.019-.151-.113-.258-.23-.258s-.208.107-.227.258a37.463,37.463,0,0,0-.271,4.655Zm54.324,16.04a27.2,27.2,0,0,0,.381,4.655.32.32,0,0,0,.321.258.323.323,0,0,0,.324-.258,28.894,28.894,0,0,0,0-9.314.323.323,0,0,0-.324-.258.32.32,0,0,0-.321.258,27.261,27.261,0,0,0-.381,4.658Zm-59.59-22.36a40.318,40.318,0,0,0,.255,4.655c.019.151.11.258.217.258s.2-.107.214-.258a42.138,42.138,0,0,0,0-9.314c-.016-.151-.107-.258-.214-.258s-.2.107-.217.258a40.335,40.335,0,0,0-.255,4.658Zm120.587,0a40.289,40.289,0,0,1-.255,4.655c-.019.151-.11.258-.217.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.151.11-.258.217-.258s.2.107.217.258a40.306,40.306,0,0,1,.255,4.658Z" transform="translate(-192.153 -90.535)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12105" data-name="Path 12105" d="M205.356,209.885c0,17.419,27.466,31.542,61.35,31.542s61.346-14.123,61.346-31.542-27.466-31.542-61.346-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-193.203 -119.055)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12106" data-name="Path 12106" d="M205.192,209.4c0,17.227,27.913,31.193,62.347,31.193S329.89,226.628,329.89,209.4s-27.913-31.2-62.351-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-193.555 -119.351)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12107" data-name="Path 12107" d="M207.065,205.661c-.4,15.376,24.551,28.5,55.734,29.323S319.588,224,319.991,208.617s-24.551-28.5-55.734-29.32-56.789,10.985-57.192,26.364Z" transform="translate(-189.543 -117.092)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12108" data-name="Path 12108" d="M207.187,207.257c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051-25.1-28.054-56.071-28.054-56.068,12.559-56.068,28.054Z" transform="translate(-189.271 -117.208)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12109" data-name="Path 12109" d="M206.8,207.3c0,15.316,24.819,27.737,55.435,27.737s55.435-12.42,55.435-27.737-24.819-27.737-55.435-27.737S206.8,191.978,206.8,207.3Z" transform="translate(-190.1 -116.439)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12110" data-name="Path 12110" d="M319.219,208.989c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.445,14.445,0,0,1-.1-1.731c0-15.5,25.1-28.054,56.068-28.054s56.071,12.559,56.071,28.054a13.819,13.819,0,0,1-.107,1.731Z" transform="translate(-189.271 -117.208)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12111" data-name="Path 12111" d="M201.5,197.05c0,17.164,27.913,31.079,62.347,31.079s62.35-13.915,62.35-31.079l0-13.582H201.5V197.05Z" transform="translate(-201.495 -108.049)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12112" data-name="Path 12112" d="M235.472,196.246q3.99.26,8.127.261a121.944,121.944,0,0,0,16.371-1.086v13.553a92.959,92.959,0,0,1-16.371,1.083c-8.823,0-23.176-1.687-32.678-4.6V191.883a107.574,107.574,0,0,0,24.551,4.363Z" transform="translate(-181.252 -89.977)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12113" data-name="Path 12113" d="M234.8,206.09c12.1-5.637,19.713-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.073-19.861,22.744V206.09Z" transform="translate(-129.965 -107.926)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12114" data-name="Path 12114" d="M214.3,202.322v13.6c-8.033-5.231-12.8-11.772-12.8-18.87V183.468c.038,7.091,4.8,13.626,12.8,18.854Z" transform="translate(-201.495 -108.049)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12115" data-name="Path 12115" d="M231.179,195.086q3.99.26,8.127.261V208.9a116.888,116.888,0,0,1-26.386-2.915v-13.56a112.582,112.582,0,0,0,18.259,2.663Z" transform="translate(-176.959 -88.817)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12116" data-name="Path 12116" d="M271.389,213.943a28.355,28.355,0,0,1-.365,4.658.314.314,0,0,1-.617,0,29.9,29.9,0,0,1,0-9.317.313.313,0,0,1,.617,0,28.355,28.355,0,0,1,.365,4.658Zm8.206-.831a29.575,29.575,0,0,1-.346,4.658.3.3,0,0,1-.592,0,31.227,31.227,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.574,29.574,0,0,1,.346,4.658Zm8.127-1.423a30.811,30.811,0,0,1-.334,4.655.283.283,0,0,1-.563,0,32.662,32.662,0,0,1,0-9.314.283.283,0,0,1,.563,0,30.825,30.825,0,0,1,.334,4.658Zm7.982-2.084a32.287,32.287,0,0,1-.318,4.658.28.28,0,0,1-.268.255.285.285,0,0,1-.271-.255,34.623,34.623,0,0,1,0-9.317.286.286,0,0,1,.271-.258.281.281,0,0,1,.268.258,32.286,32.286,0,0,1,.318,4.658Zm7.749-2.836a33.815,33.815,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.033,36.033,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.8,33.8,0,0,1,.3,4.655Zm7.359-3.72a35.5,35.5,0,0,1-.286,4.655.243.243,0,1,1-.485,0,38.006,38.006,0,0,1,0-9.314.243.243,0,1,1,.485,0,35.552,35.552,0,0,1,.286,4.658Zm6.657-4.863a37.506,37.506,0,0,1-.271,4.658.259.259,0,0,1-.23.255.262.262,0,0,1-.23-.255,40.22,40.22,0,0,1,0-9.317.265.265,0,0,1,.23-.258.263.263,0,0,1,.23.258,37.505,37.505,0,0,1,.271,4.658Zm-63.975,15.757a28.376,28.376,0,0,0,.362,4.658.311.311,0,0,0,.308.255.315.315,0,0,0,.312-.255,30.155,30.155,0,0,0,0-9.317.317.317,0,0,0-.312-.258.314.314,0,0,0-.308.258,28.376,28.376,0,0,0-.362,4.658Zm-8.209-.831a29.544,29.544,0,0,0,.349,4.658.3.3,0,0,0,.3.258.3.3,0,0,0,.293-.258,31.232,31.232,0,0,0,0-9.317.3.3,0,0,0-.293-.258.3.3,0,0,0-.3.258,29.543,29.543,0,0,0-.349,4.658Zm-8.127-1.423a30.811,30.811,0,0,0,.334,4.655.291.291,0,0,0,.283.258.288.288,0,0,0,.28-.258,32.668,32.668,0,0,0,0-9.314.288.288,0,0,0-.28-.258.291.291,0,0,0-.283.258,30.825,30.825,0,0,0-.334,4.658Zm-7.982-2.084a32.287,32.287,0,0,0,.318,4.658.27.27,0,0,0,.538,0,34.287,34.287,0,0,0,0-9.317.269.269,0,0,0-.538,0,32.286,32.286,0,0,0-.318,4.658Zm-7.746-2.836a33.815,33.815,0,0,0,.3,4.658.255.255,0,1,0,.51,0,36.033,36.033,0,0,0,0-9.314.255.255,0,1,0-.51,0,33.8,33.8,0,0,0-.3,4.655Zm-7.362-3.72a36.049,36.049,0,0,0,.286,4.655.271.271,0,0,0,.242.258.266.266,0,0,0,.242-.258,38,38,0,0,0,0-9.314.268.268,0,0,0-.242-.258.273.273,0,0,0-.242.258,36.1,36.1,0,0,0-.286,4.658Zm-6.657-4.863a38.131,38.131,0,0,0,.271,4.658.231.231,0,1,0,.46,0,40.229,40.229,0,0,0,0-9.317.231.231,0,1,0-.46,0,38.13,38.13,0,0,0-.271,4.658Zm54.327,16.037a26.91,26.91,0,0,0,.381,4.658.329.329,0,0,0,.642,0,28.661,28.661,0,0,0,0-9.314.329.329,0,0,0-.642,0,26.873,26.873,0,0,0-.381,4.655Zm-59.59-22.36a40.361,40.361,0,0,0,.255,4.658c.019.148.107.258.217.258s.2-.11.214-.258a42.678,42.678,0,0,0,0-9.317c-.016-.148-.107-.255-.214-.255s-.2.107-.217.255a40.361,40.361,0,0,0-.255,4.658Zm120.587,0a40.361,40.361,0,0,1-.255,4.658c-.019.148-.11.258-.217.258s-.2-.11-.217-.258a42.678,42.678,0,0,1,0-9.317c.019-.148.107-.255.217-.255s.2.107.217.255a40.361,40.361,0,0,1,.255,4.658Z" transform="translate(-200.093 -100.573)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12117" data-name="Path 12117" d="M201.659,205.211c0,17.419,27.466,31.539,61.346,31.539s61.35-14.12,61.35-31.539-27.466-31.542-61.35-31.542-61.346,14.12-61.346,31.542Z" transform="translate(-201.143 -129.093)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12118" data-name="Path 12118" d="M201.5,204.726c0,17.23,27.913,31.2,62.347,31.2s62.35-13.966,62.35-31.2-27.916-31.2-62.35-31.2S201.5,187.5,201.5,204.726Z" transform="translate(-201.495 -129.391)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12119" data-name="Path 12119" d="M203.368,200.986c-.4,15.379,24.548,28.508,55.734,29.323s56.789-10.988,57.192-26.364-24.551-28.5-55.734-29.323-56.789,10.988-57.192,26.364Z" transform="translate(-197.482 -127.131)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12120" data-name="Path 12120" d="M203.49,202.58c0,15.492,25.1,28.054,56.068,28.054s56.071-12.562,56.071-28.054-25.105-28.051-56.071-28.051S203.49,187.088,203.49,202.58Z" transform="translate(-197.211 -127.246)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12121" data-name="Path 12121" d="M204.321,202.507c0,15.319,24.819,27.737,55.435,27.737s55.438-12.417,55.438-27.737-24.822-27.737-55.438-27.737-55.435,12.417-55.435,27.737Z" transform="translate(-195.426 -126.728)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12122" data-name="Path 12122" d="M315.522,204.311c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.406,14.406,0,0,1-.1-1.731c0-15.492,25.1-28.051,56.068-28.051s56.071,12.559,56.071,28.051a14.407,14.407,0,0,1-.107,1.731Z" transform="translate(-197.211 -127.246)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12123" data-name="Path 12123" d="M205.192,192.376c0,17.164,27.913,31.076,62.347,31.076s62.351-13.912,62.351-31.076l0-13.582H205.192v13.582Z" transform="translate(-193.555 -118.087)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12124" data-name="Path 12124" d="M239.167,191.568q3.99.26,8.127.261a121.935,121.935,0,0,0,16.371-1.086V204.3a93,93,0,0,1-16.371,1.08c-8.823,0-23.176-1.684-32.675-4.6V187.209a107.515,107.515,0,0,0,24.548,4.359Z" transform="translate(-173.31 -100.015)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12125" data-name="Path 12125" d="M238.5,201.418c12.1-5.641,19.713-13.657,19.858-22.568l0,13.406c0,8.98-7.642,17.069-19.861,22.745V201.418Z" transform="translate(-122.026 -117.966)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12126" data-name="Path 12126" d="M217.993,197.648v13.594c-8.03-5.231-12.8-11.772-12.8-18.867V178.794c.038,7.091,4.8,13.623,12.8,18.854Z" transform="translate(-193.555 -118.087)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12127" data-name="Path 12127" d="M234.876,190.409q3.99.26,8.127.261v13.55a116.887,116.887,0,0,1-26.386-2.915V187.749a112.284,112.284,0,0,0,18.259,2.66Z" transform="translate(-169.019 -98.855)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12128" data-name="Path 12128" d="M275.086,209.271a27.978,27.978,0,0,1-.365,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,27.989,27.989,0,0,1,.365,4.658Zm8.209-.831a29.479,29.479,0,0,1-.349,4.655.3.3,0,0,1-.592,0,31.211,31.211,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.521,29.521,0,0,1,.349,4.658Zm8.124-1.426a30.447,30.447,0,0,1-.334,4.658.29.29,0,0,1-.28.258.3.3,0,0,1-.283-.258,32.69,32.69,0,0,1,0-9.317.293.293,0,0,1,.283-.255.287.287,0,0,1,.28.255,30.447,30.447,0,0,1,.334,4.658Zm7.982-2.084a32.275,32.275,0,0,1-.315,4.658.283.283,0,0,1-.271.258.281.281,0,0,1-.268-.258,34.264,34.264,0,0,1,0-9.314.281.281,0,0,1,.268-.258.283.283,0,0,1,.271.258,32.229,32.229,0,0,1,.315,4.655Zm7.749-2.833a33.858,33.858,0,0,1-.3,4.658.257.257,0,0,1-.513,0,36.058,36.058,0,0,1,0-9.317.257.257,0,1,1,.513,0,33.858,33.858,0,0,1,.3,4.658Zm7.359-3.724a35.541,35.541,0,0,1-.286,4.658.266.266,0,0,1-.242.258.271.271,0,0,1-.242-.258,38,38,0,0,1,0-9.314.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.491,35.491,0,0,1,.286,4.655Zm6.657-4.86a37.464,37.464,0,0,1-.271,4.655c-.019.151-.113.258-.23.258s-.208-.107-.227-.258a39.73,39.73,0,0,1,0-9.314c.019-.151.113-.258.227-.258s.211.107.23.258a37.479,37.479,0,0,1,.271,4.658Zm-63.975,15.757a27.978,27.978,0,0,0,.365,4.655.313.313,0,0,0,.617,0,30.135,30.135,0,0,0,0-9.314.313.313,0,0,0-.617,0,27.989,27.989,0,0,0-.365,4.658Zm-8.209-.831a29.479,29.479,0,0,0,.349,4.655.3.3,0,0,0,.592,0,31.489,31.489,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.522,29.522,0,0,0-.349,4.658Zm-8.127-1.426a30.847,30.847,0,0,0,.334,4.658.284.284,0,0,0,.567,0,33,33,0,0,0,0-9.317.285.285,0,0,0-.567,0,30.847,30.847,0,0,0-.334,4.658Zm-7.982-2.084a32.245,32.245,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.2,32.2,0,0,0-.318,4.655Zm-7.746-2.833a33.849,33.849,0,0,0,.3,4.658.276.276,0,0,0,.255.255.271.271,0,0,0,.255-.255,36.065,36.065,0,0,0,0-9.317.274.274,0,0,0-.255-.258.279.279,0,0,0-.255.258,33.848,33.848,0,0,0-.3,4.658Zm-7.362-3.724a36.111,36.111,0,0,0,.286,4.658.244.244,0,1,0,.488,0,38.006,38.006,0,0,0,0-9.314.244.244,0,1,0-.488,0,36.06,36.06,0,0,0-.286,4.655Zm-6.654-4.86a37.464,37.464,0,0,0,.271,4.655c.019.151.113.258.227.258s.211-.107.23-.258a40.183,40.183,0,0,0,0-9.314c-.019-.151-.113-.258-.23-.258s-.208.107-.227.258a37.479,37.479,0,0,0-.271,4.658Zm54.324,16.037a27.261,27.261,0,0,0,.381,4.658.32.32,0,0,0,.321.255.323.323,0,0,0,.324-.255,28.913,28.913,0,0,0,0-9.317.326.326,0,0,0-.324-.258.323.323,0,0,0-.321.258,27.261,27.261,0,0,0-.381,4.658Zm-59.59-22.36a40.278,40.278,0,0,0,.255,4.655c.019.151.11.258.217.258s.2-.107.214-.258a42.138,42.138,0,0,0,0-9.314c-.016-.151-.107-.258-.214-.258s-.2.107-.217.258a40.335,40.335,0,0,0-.255,4.658Zm120.587,0a40.249,40.249,0,0,1-.255,4.655c-.019.151-.11.258-.217.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.151.11-.258.217-.258s.2.107.217.258a40.306,40.306,0,0,1,.255,4.658Z" transform="translate(-192.153 -110.613)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12129" data-name="Path 12129" d="M205.356,200.536c0,17.419,27.466,31.542,61.35,31.542s61.346-14.123,61.346-31.542-27.466-31.542-61.346-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-193.203 -139.133)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12130" data-name="Path 12130" d="M205.192,200.052c0,17.227,27.913,31.193,62.347,31.193s62.351-13.966,62.351-31.193-27.913-31.2-62.351-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-193.555 -139.429)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12131" data-name="Path 12131" d="M207.065,196.313c-.4,15.376,24.551,28.5,55.734,29.323s56.789-10.988,57.192-26.367-24.551-28.5-55.734-29.32-56.789,10.988-57.192,26.364Z" transform="translate(-189.543 -137.169)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12132" data-name="Path 12132" d="M207.187,197.908c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051-25.1-28.054-56.071-28.054-56.068,12.559-56.068,28.054Z" transform="translate(-189.271 -137.286)" fill="#9e9e9e" fill-rule="evenodd"/>
      <path id="Path_12133" data-name="Path 12133" d="M319.219,199.64c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.407,14.407,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a13.782,13.782,0,0,1-.107,1.731Z" transform="translate(-189.271 -137.286)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12134" data-name="Path 12134" d="M205.913,197.973c0,15.319,24.819,27.737,55.435,27.737s55.435-12.417,55.435-27.737-24.819-27.733-55.435-27.733-55.435,12.417-55.435,27.733Z" transform="translate(-192.007 -136.457)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12135" data-name="Path 12135" d="M206.671,187.7c0,17.164,27.913,31.079,62.347,31.079s62.35-13.915,62.35-31.079l0-13.579H206.671V187.7Z" transform="translate(-190.379 -128.124)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12136" data-name="Path 12136" d="M240.648,186.9q3.99.26,8.127.261a121.945,121.945,0,0,0,16.371-1.086v13.553a92.935,92.935,0,0,1-16.371,1.083c-8.823,0-23.176-1.684-32.678-4.6V182.534a107.572,107.572,0,0,0,24.551,4.363Z" transform="translate(-170.136 -110.055)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12137" data-name="Path 12137" d="M239.978,196.744c12.1-5.64,19.713-13.661,19.858-22.568l0,13.4c0,8.98-7.642,17.072-19.861,22.744V196.744Z" transform="translate(-118.85 -128.004)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12138" data-name="Path 12138" d="M219.472,192.971v13.6c-8.03-5.231-12.8-11.772-12.8-18.87V174.12c.038,7.088,4.8,13.623,12.8,18.851Z" transform="translate(-190.379 -128.124)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12139" data-name="Path 12139" d="M236.355,185.737q3.99.26,8.127.261v13.55a116.889,116.889,0,0,1-26.386-2.915v-13.56a112.583,112.583,0,0,0,18.259,2.663Z" transform="translate(-165.843 -108.895)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12140" data-name="Path 12140" d="M276.565,204.594a28.348,28.348,0,0,1-.365,4.659.313.313,0,0,1-.617,0,29.9,29.9,0,0,1,0-9.317.314.314,0,0,1,.617,0,28.348,28.348,0,0,1,.365,4.658Zm8.206-.831a29.568,29.568,0,0,1-.346,4.658.3.3,0,0,1-.592,0,31.232,31.232,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.567,29.567,0,0,1,.346,4.658Zm8.127-1.423a30.8,30.8,0,0,1-.334,4.655.288.288,0,0,1-.28.258A.291.291,0,0,1,292,207a32.668,32.668,0,0,1,0-9.314.291.291,0,0,1,.283-.258.288.288,0,0,1,.28.258,30.816,30.816,0,0,1,.334,4.658Zm7.982-2.084a32.287,32.287,0,0,1-.318,4.658.268.268,0,0,1-.535,0,34.288,34.288,0,0,1,0-9.317.268.268,0,0,1,.535,0,32.287,32.287,0,0,1,.318,4.658Zm7.749-2.833a33.8,33.8,0,0,1-.3,4.655.257.257,0,1,1-.513,0,36.041,36.041,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.816,33.816,0,0,1,.3,4.658Zm7.359-3.724a35.588,35.588,0,0,1-.286,4.658.243.243,0,1,1-.485,0,38.024,38.024,0,0,1,0-9.317.243.243,0,1,1,.485,0,35.588,35.588,0,0,1,.286,4.658Zm6.657-4.863a37.516,37.516,0,0,1-.271,4.658.231.231,0,1,1-.46,0,40.219,40.219,0,0,1,0-9.317.231.231,0,1,1,.46,0,37.517,37.517,0,0,1,.271,4.658Zm-63.975,15.757a28.377,28.377,0,0,0,.362,4.659.317.317,0,0,0,.312.258.314.314,0,0,0,.308-.258,30.16,30.16,0,0,0,0-9.317.311.311,0,0,0-.308-.255.315.315,0,0,0-.312.255,28.376,28.376,0,0,0-.362,4.658Zm-8.209-.831a29.551,29.551,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.51,31.51,0,0,0,0-9.317.3.3,0,0,0-.592,0,29.551,29.551,0,0,0-.349,4.658Zm-8.127-1.423a30.812,30.812,0,0,0,.334,4.655.293.293,0,0,0,.283.258.287.287,0,0,0,.28-.258,32.662,32.662,0,0,0,0-9.314.288.288,0,0,0-.28-.258.293.293,0,0,0-.283.258,30.825,30.825,0,0,0-.334,4.658Zm-7.982-2.084a32.278,32.278,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.288,34.288,0,0,0,0-9.317.269.269,0,0,0-.538,0,32.278,32.278,0,0,0-.318,4.658Zm-7.746-2.833a33.8,33.8,0,0,0,.3,4.655.255.255,0,1,0,.51,0,36.034,36.034,0,0,0,0-9.314.255.255,0,1,0-.51,0,33.816,33.816,0,0,0-.3,4.658Zm-7.362-3.724a36.149,36.149,0,0,0,.286,4.658.27.27,0,0,0,.242.255.265.265,0,0,0,.242-.255,38.024,38.024,0,0,0,0-9.317.268.268,0,0,0-.242-.258.273.273,0,0,0-.242.258,36.149,36.149,0,0,0-.286,4.658Zm-6.657-4.863a38.129,38.129,0,0,0,.271,4.658.231.231,0,1,0,.46,0,40.219,40.219,0,0,0,0-9.317.231.231,0,1,0-.46,0,38.13,38.13,0,0,0-.271,4.658Zm54.327,16.037a27.234,27.234,0,0,0,.381,4.658.317.317,0,0,0,.321.258.323.323,0,0,0,.324-.258,28.9,28.9,0,0,0,0-9.314.323.323,0,0,0-.324-.258.317.317,0,0,0-.321.258,27.223,27.223,0,0,0-.381,4.655Zm-59.59-22.36a40.361,40.361,0,0,0,.255,4.658c.019.148.107.258.217.258s.2-.11.214-.258a42.688,42.688,0,0,0,0-9.317c-.016-.148-.107-.255-.214-.255s-.2.107-.217.255a40.361,40.361,0,0,0-.255,4.658Zm120.587,0a39.629,39.629,0,0,1-.258,4.658c-.016.148-.107.258-.214.258s-.2-.11-.217-.258a42.688,42.688,0,0,1,0-9.317c.019-.148.11-.255.217-.255s.2.107.214.255a39.629,39.629,0,0,1,.258,4.658Z" transform="translate(-188.977 -120.651)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12141" data-name="Path 12141" d="M206.835,195.862c0,17.419,27.466,31.539,61.346,31.539s61.35-14.12,61.35-31.539-27.466-31.542-61.35-31.542-61.346,14.12-61.346,31.542Z" transform="translate(-190.027 -149.171)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12142" data-name="Path 12142" d="M206.671,195.377c0,17.23,27.913,31.2,62.347,31.2s62.35-13.966,62.35-31.2-27.916-31.2-62.35-31.2-62.347,13.969-62.347,31.2Z" transform="translate(-190.379 -149.469)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12143" data-name="Path 12143" d="M208.544,191.64c-.4,15.376,24.548,28.5,55.734,29.32s56.789-10.985,57.192-26.364-24.551-28.5-55.734-29.323-56.789,10.988-57.192,26.367Z" transform="translate(-186.366 -147.209)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12144" data-name="Path 12144" d="M208.666,193.231c0,15.5,25.1,28.054,56.068,28.054s56.071-12.559,56.071-28.054S295.7,165.18,264.734,165.18s-56.068,12.559-56.068,28.051Z" transform="translate(-186.095 -147.324)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12145" data-name="Path 12145" d="M208.316,193.293c0,15.316,24.819,27.737,55.435,27.737s55.438-12.42,55.438-27.737-24.822-27.737-55.438-27.737-55.435,12.417-55.435,27.737Z" transform="translate(-186.846 -146.516)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12146" data-name="Path 12146" d="M320.7,194.962c-1.788-14.683-26.16-26.32-55.964-26.32s-54.176,11.637-55.964,26.32a14.355,14.355,0,0,1-.1-1.731c0-15.492,25.1-28.051,56.068-28.051s56.071,12.559,56.071,28.051a13.746,13.746,0,0,1-.107,1.731Z" transform="translate(-186.095 -147.324)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12147" data-name="Path 12147" d="M202.973,183.027c0,17.164,27.916,31.076,62.35,31.076s62.351-13.912,62.351-31.076l0-13.582h-124.7v13.582Z" transform="translate(-198.321 -138.164)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12148" data-name="Path 12148" d="M236.951,182.219q3.99.26,8.127.264a122.5,122.5,0,0,0,16.371-1.086v13.553a93.043,93.043,0,0,1-16.371,1.08c-8.823,0-23.176-1.684-32.678-4.6V177.86a107.576,107.576,0,0,0,24.551,4.359Z" transform="translate(-178.076 -120.092)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12149" data-name="Path 12149" d="M236.281,192.067c12.1-5.637,19.71-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.069-19.861,22.744V192.067Z" transform="translate(-126.789 -138.042)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12150" data-name="Path 12150" d="M215.777,188.3v13.594c-8.033-5.231-12.8-11.772-12.8-18.867V169.445c.041,7.091,4.806,13.623,12.8,18.854Z" transform="translate(-198.321 -138.164)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12151" data-name="Path 12151" d="M232.658,181.06q3.99.26,8.127.264v13.547A117.05,117.05,0,0,1,214.4,191.96V178.4a112.232,112.232,0,0,0,18.259,2.66Z" transform="translate(-173.783 -118.933)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12152" data-name="Path 12152" d="M272.868,199.922a28.294,28.294,0,0,1-.365,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.333,28.333,0,0,1,.365,4.658Zm8.206-.831a29.5,29.5,0,0,1-.346,4.655.3.3,0,0,1-.592,0,31.206,31.206,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.546,29.546,0,0,1,.346,4.658Zm8.127-1.426a30.856,30.856,0,0,1-.334,4.658.283.283,0,0,1-.563,0,32.668,32.668,0,0,1,0-9.314.283.283,0,0,1,.563,0,30.781,30.781,0,0,1,.334,4.655Zm7.982-2.084a32.245,32.245,0,0,1-.318,4.658.281.281,0,0,1-.268.258.283.283,0,0,1-.271-.258,34.607,34.607,0,0,1,0-9.314.283.283,0,0,1,.271-.258.281.281,0,0,1,.268.258,32.231,32.231,0,0,1,.318,4.655Zm7.746-2.833a34.39,34.39,0,0,1-.3,4.658.257.257,0,0,1-.513,0,36.065,36.065,0,0,1,0-9.317.257.257,0,0,1,.513,0,34.391,34.391,0,0,1,.3,4.659Zm7.362-3.72a35.549,35.549,0,0,1-.286,4.655.243.243,0,1,1-.485,0,38,38,0,0,1,0-9.314.243.243,0,1,1,.485,0,35.563,35.563,0,0,1,.286,4.658Zm6.657-4.863a37.464,37.464,0,0,1-.271,4.655c-.019.151-.113.258-.23.258a.263.263,0,0,1-.23-.258,40.192,40.192,0,0,1,0-9.314.263.263,0,0,1,.23-.258c.116,0,.211.107.23.258a37.479,37.479,0,0,1,.271,4.658Zm-63.975,15.757a28.315,28.315,0,0,0,.362,4.655.313.313,0,0,0,.617,0,29.881,29.881,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.355,28.355,0,0,0-.362,4.658Zm-8.209-.831a29.113,29.113,0,0,0,.349,4.655.3.3,0,0,0,.589,0,31.211,31.211,0,0,0,0-9.314.3.3,0,0,0-.589,0,29.155,29.155,0,0,0-.349,4.658Zm-8.127-1.426a30.856,30.856,0,0,0,.334,4.658.283.283,0,0,0,.563,0,32.662,32.662,0,0,0,0-9.314.283.283,0,0,0-.563,0,30.781,30.781,0,0,0-.334,4.655Zm-7.982-2.084a32.245,32.245,0,0,0,.318,4.658.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.231,32.231,0,0,0-.318,4.655Zm-7.746-2.833a33.85,33.85,0,0,0,.3,4.658.271.271,0,0,0,.255.255.276.276,0,0,0,.255-.255,36.058,36.058,0,0,0,0-9.317.276.276,0,0,0-.255-.255.271.271,0,0,0-.255.255,33.85,33.85,0,0,0-.3,4.659Zm-7.362-3.72a35.537,35.537,0,0,0,.286,4.655.271.271,0,0,0,.242.258.266.266,0,0,0,.242-.258,38,38,0,0,0,0-9.314.266.266,0,0,0-.242-.258.271.271,0,0,0-.242.258,35.552,35.552,0,0,0-.286,4.658Zm-6.657-4.863a38.075,38.075,0,0,0,.271,4.655.231.231,0,1,0,.46,0,40.192,40.192,0,0,0,0-9.314.231.231,0,1,0-.46,0,38.09,38.09,0,0,0-.271,4.658ZM263.217,200.2a26.95,26.95,0,0,0,.381,4.658.329.329,0,0,0,.642,0,28.68,28.68,0,0,0,0-9.317.329.329,0,0,0-.642,0,26.95,26.95,0,0,0-.381,4.658Zm-59.59-22.36a39.579,39.579,0,0,0,.255,4.655c.019.151.107.258.217.258s.2-.107.214-.258a42.659,42.659,0,0,0,0-9.314c-.016-.151-.107-.258-.214-.258s-.2.107-.217.258a39.635,39.635,0,0,0-.255,4.658Zm120.584,0a39.579,39.579,0,0,1-.255,4.655c-.016.151-.107.258-.214.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.151.107-.258.217-.258s.2.107.214.258a39.635,39.635,0,0,1,.255,4.658Z" transform="translate(-196.916 -130.691)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12153" data-name="Path 12153" d="M203.137,191.185c0,17.419,27.469,31.542,61.35,31.542s61.35-14.123,61.35-31.542-27.466-31.539-61.35-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-197.969 -159.209)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12154" data-name="Path 12154" d="M202.973,190.7c0,17.227,27.916,31.2,62.35,31.2s62.351-13.969,62.351-31.2-27.916-31.2-62.351-31.2-62.35,13.966-62.35,31.2Z" transform="translate(-198.321 -159.507)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12155" data-name="Path 12155" d="M204.846,186.963c-.4,15.376,24.551,28.5,55.737,29.323s56.789-10.988,57.192-26.364-24.551-28.508-55.734-29.323-56.792,10.988-57.195,26.364Z" transform="translate(-194.308 -157.247)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12156" data-name="Path 12156" d="M204.969,188.559c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051S292,160.505,261.037,160.505s-56.068,12.562-56.068,28.054Z" transform="translate(-194.034 -157.364)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_12157" data-name="Path 12157" d="M317,190.291c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.394,14.394,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a14.408,14.408,0,0,1-.107,1.731Z" transform="translate(-194.034 -157.364)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12158" data-name="Path 12158" d="M260.966,161.606c-28.681,0-52.332,10.774-55.668,24.674a15.428,15.428,0,0,0-.3,1.646,15.119,15.119,0,0,0,.532,2.512,18.49,18.49,0,0,0,3.245,6.034l.494-.072,69.549-10.119,35.561-5.175c-7.23-11.309-28.41-19.5-53.418-19.5Z" transform="translate(-193.963 -154.999)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12159" data-name="Path 12159" d="M208.859,189.223c.359-13.689,20.689-24.545,47.151-26.188,3.028-.255,6.141-.387,9.314-.387,28.083,0,51.343,10.33,55.432,23.808.148.378.277.755.393,1.133l6.021-.875c-3.916-15.344-30.11-27.208-61.847-27.208-34.435,0-62.35,13.966-62.35,31.2v.085c.028,4.7,2.128,9.153,5.87,13.144l6.116-.888a25.563,25.563,0,0,1-1.822-2.065,18.49,18.49,0,0,1-3.245-6.034,15.04,15.04,0,0,1-.985-4.158,14.384,14.384,0,0,1-.047-1.564Z" transform="translate(-198.321 -159.507)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
    <g id="Group_3364" data-name="Group 3364" transform="translate(163.29 238.044)">
      <path id="Path_12160" data-name="Path 12160" d="M165.508,234.579c0,17.161,27.916,31.076,62.351,31.076s62.347-13.915,62.347-31.076V221h-124.7v13.582Z" transform="translate(-158.527 -114.59)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12161" data-name="Path 12161" d="M199.483,233.771q3.994.26,8.13.261a121.933,121.933,0,0,0,16.367-1.086V246.5a92.919,92.919,0,0,1-16.367,1.083c-8.826,0-23.176-1.684-32.678-4.6V229.412a107.72,107.72,0,0,0,24.548,4.359Z" transform="translate(-138.281 -96.518)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12162" data-name="Path 12162" d="M198.816,243.621c12.1-5.641,19.71-13.657,19.858-22.568v13.406c0,8.98-7.639,17.069-19.858,22.744V243.621Z" transform="translate(-86.995 -114.47)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-90)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12163" data-name="Path 12163" d="M178.309,239.851v13.594c-8.029-5.231-12.8-11.772-12.8-18.867V221c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-158.527 -114.59)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12164" data-name="Path 12164" d="M195.192,232.614q3.994.26,8.13.261v13.55a116.9,116.9,0,0,1-26.389-2.915v-13.56a112.683,112.683,0,0,0,18.259,2.663Z" transform="translate(-133.991 -95.36)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12165" data-name="Path 12165" d="M235.4,251.471a28.018,28.018,0,0,1-.365,4.659.313.313,0,0,1-.617,0,30.135,30.135,0,0,1,0-9.314.313.313,0,0,1,.617,0,27.949,27.949,0,0,1,.365,4.655Zm8.209-.831a29.529,29.529,0,0,1-.349,4.658.3.3,0,0,1-.592,0,31.494,31.494,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.487,29.487,0,0,1,.349,4.655Zm8.124-1.423a30.882,30.882,0,0,1-.331,4.658.285.285,0,0,1-.567,0,32.989,32.989,0,0,1,0-9.317.284.284,0,0,1,.567,0,30.882,30.882,0,0,1,.331,4.658Zm7.985-2.084a32.245,32.245,0,0,1-.318,4.658.269.269,0,0,1-.538,0,34.264,34.264,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.2,32.2,0,0,1,.318,4.655Zm7.746-2.833a33.767,33.767,0,0,1-.3,4.655.276.276,0,0,1-.255.258.272.272,0,0,1-.255-.258,36.034,36.034,0,0,1,0-9.314.272.272,0,0,1,.255-.258.276.276,0,0,1,.255.258,33.85,33.85,0,0,1,.3,4.658Zm7.359-3.724a36.175,36.175,0,0,1-.283,4.658.244.244,0,1,1-.488,0,38.024,38.024,0,0,1,0-9.317.244.244,0,1,1,.488,0,36.175,36.175,0,0,1,.283,4.658Zm6.66-4.863a38.091,38.091,0,0,1-.271,4.658.263.263,0,0,1-.23.258c-.116,0-.211-.107-.23-.258a40.192,40.192,0,0,1,0-9.314c.019-.151.113-.258.23-.258a.263.263,0,0,1,.23.258,38.037,38.037,0,0,1,.271,4.655Zm-63.978,15.757a28.024,28.024,0,0,0,.365,4.659.313.313,0,0,0,.617,0,29.881,29.881,0,0,0,0-9.314.313.313,0,0,0-.617,0,27.956,27.956,0,0,0-.365,4.655Zm-8.209-.831a29.521,29.521,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.206,31.206,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.48,29.48,0,0,0-.349,4.655Zm-8.124-1.423a30.448,30.448,0,0,0,.334,4.658.287.287,0,0,0,.28.255.29.29,0,0,0,.283-.255,32.69,32.69,0,0,0,0-9.317.291.291,0,0,0-.283-.258.288.288,0,0,0-.28.258,30.447,30.447,0,0,0-.334,4.658Zm-7.982-2.084a32.283,32.283,0,0,0,.315,4.658.286.286,0,0,0,.271.258.283.283,0,0,0,.268-.258,34.271,34.271,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.238,32.238,0,0,0-.315,4.655Zm-7.749-2.833a33.777,33.777,0,0,0,.3,4.655.257.257,0,1,0,.513,0,36.041,36.041,0,0,0,0-9.314.257.257,0,1,0-.513,0,33.86,33.86,0,0,0-.3,4.658Zm-7.359-3.724a35.588,35.588,0,0,0,.286,4.658.268.268,0,0,0,.242.258.273.273,0,0,0,.242-.258,38.024,38.024,0,0,0,0-9.317.27.27,0,0,0-.242-.255.265.265,0,0,0-.242.255,35.588,35.588,0,0,0-.286,4.658Zm-6.657-4.863a37.479,37.479,0,0,0,.271,4.658c.019.151.113.258.23.258s.208-.107.227-.258a39.738,39.738,0,0,0,0-9.314c-.019-.151-.113-.258-.227-.258s-.211.107-.23.258a37.427,37.427,0,0,0-.271,4.655Zm54.327,16.04a27.208,27.208,0,0,0,.378,4.655.323.323,0,0,0,.324.258.32.32,0,0,0,.321-.258,28.665,28.665,0,0,0,0-9.314.32.32,0,0,0-.321-.258.323.323,0,0,0-.324.258,27.247,27.247,0,0,0-.378,4.658ZM166.161,229.39a40.306,40.306,0,0,0,.255,4.658c.019.151.11.258.217.258s.2-.107.217-.258a42.659,42.659,0,0,0,0-9.314c-.019-.151-.11-.258-.217-.258s-.2.107-.217.258a40.248,40.248,0,0,0-.255,4.655Zm120.587,0a40.321,40.321,0,0,1-.255,4.658c-.019.151-.107.258-.217.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.151.11-.258.217-.258s.2.107.217.258a40.262,40.262,0,0,1,.255,4.655Z" transform="translate(-157.124 -107.116)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12166" data-name="Path 12166" d="M165.672,242.739c0,17.419,27.466,31.539,61.35,31.539s61.35-14.12,61.35-31.539S260.9,211.2,227.022,211.2s-61.35,14.123-61.35,31.542Z" transform="translate(-158.174 -135.636)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12167" data-name="Path 12167" d="M165.508,242.252c0,17.23,27.916,31.2,62.351,31.2s62.347-13.966,62.347-31.2-27.913-31.193-62.347-31.193-62.351,13.966-62.351,31.193Z" transform="translate(-158.527 -135.932)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12168" data-name="Path 12168" d="M167.381,238.516c-.4,15.376,24.551,28.5,55.734,29.32s56.789-10.985,57.192-26.364-24.548-28.5-55.734-29.32-56.789,10.985-57.192,26.364Z" transform="translate(-154.514 -133.673)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12169" data-name="Path 12169" d="M167.5,240.108c0,15.5,25.105,28.054,56.071,28.054s56.068-12.559,56.068-28.054-25.1-28.051-56.068-28.051S167.5,224.616,167.5,240.108Z" transform="translate(-154.242 -133.789)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12170" data-name="Path 12170" d="M167.3,240.145c0,15.316,24.819,27.737,55.435,27.737s55.435-12.42,55.435-27.737-24.819-27.737-55.435-27.737S167.3,224.825,167.3,240.145Z" transform="translate(-154.687 -133.035)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12171" data-name="Path 12171" d="M279.538,241.843c-1.788-14.687-26.163-26.323-55.964-26.323S169.4,227.156,167.61,241.843a14.448,14.448,0,0,1-.107-1.734c0-15.492,25.105-28.051,56.071-28.051s56.068,12.559,56.068,28.051a14.447,14.447,0,0,1-.1,1.734Z" transform="translate(-154.242 -133.789)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12172" data-name="Path 12172" d="M168.466,229.9c0,17.164,27.913,31.079,62.347,31.079s62.35-13.916,62.35-31.079l0-13.579H168.466V229.9Z" transform="translate(-152.174 -124.628)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12173" data-name="Path 12173" d="M202.441,229.1q3.99.26,8.127.264a121.936,121.936,0,0,0,16.371-1.086v13.554a92.937,92.937,0,0,1-16.371,1.083c-8.823,0-23.176-1.687-32.675-4.6V224.737a107.516,107.516,0,0,0,24.548,4.359Z" transform="translate(-131.929 -106.558)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12174" data-name="Path 12174" d="M201.773,238.944c12.1-5.637,19.713-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.073-19.861,22.745V238.944Z" transform="translate(-80.645 -124.507)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12175" data-name="Path 12175" d="M181.267,235.174v13.6c-8.03-5.231-12.8-11.772-12.8-18.87V216.323c.038,7.088,4.8,13.623,12.8,18.851Z" transform="translate(-152.174 -124.628)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12176" data-name="Path 12176" d="M198.15,227.937q3.99.26,8.127.264v13.55a116.891,116.891,0,0,1-26.386-2.915v-13.56a112.283,112.283,0,0,0,18.259,2.66Z" transform="translate(-127.638 -105.398)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12177" data-name="Path 12177" d="M238.36,246.8a28.017,28.017,0,0,1-.365,4.658.314.314,0,0,1-.617,0,29.9,29.9,0,0,1,0-9.317.313.313,0,0,1,.617,0,28.017,28.017,0,0,1,.365,4.658Zm8.209-.831a29.544,29.544,0,0,1-.349,4.658.3.3,0,0,1-.592,0,31.233,31.233,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.544,29.544,0,0,1,.349,4.658Zm8.124-1.426a30.409,30.409,0,0,1-.334,4.658.288.288,0,0,1-.28.258.293.293,0,0,1-.283-.258,32.668,32.668,0,0,1,0-9.314.293.293,0,0,1,.283-.258.288.288,0,0,1,.28.258,30.366,30.366,0,0,1,.334,4.655Zm7.982-2.081a32.238,32.238,0,0,1-.315,4.655.283.283,0,0,1-.271.258.281.281,0,0,1-.268-.258,34.271,34.271,0,0,1,0-9.314.283.283,0,0,1,.268-.258.286.286,0,0,1,.271.258,32.316,32.316,0,0,1,.315,4.658Zm7.749-2.836a33.85,33.85,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.041,36.041,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.768,33.768,0,0,1,.3,4.655Zm7.359-3.721a35.5,35.5,0,0,1-.286,4.655.266.266,0,0,1-.242.258.271.271,0,0,1-.242-.258,38,38,0,0,1,0-9.314.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.551,35.551,0,0,1,.286,4.658Zm6.657-4.863a37.517,37.517,0,0,1-.271,4.658.23.23,0,1,1-.456,0,40.22,40.22,0,0,1,0-9.317c.019-.151.113-.258.227-.258s.211.107.23.258a37.518,37.518,0,0,1,.271,4.658ZM220.466,246.8a28.01,28.01,0,0,0,.365,4.658.314.314,0,0,0,.617,0,30.16,30.16,0,0,0,0-9.317.313.313,0,0,0-.617,0,28.011,28.011,0,0,0-.365,4.658Zm-8.209-.831a29.559,29.559,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.51,31.51,0,0,0,0-9.317.3.3,0,0,0-.592,0,29.559,29.559,0,0,0-.349,4.658Zm-8.124-1.426a30.851,30.851,0,0,0,.331,4.658.284.284,0,0,0,.567,0,32.973,32.973,0,0,0,0-9.314.284.284,0,0,0-.567,0,30.807,30.807,0,0,0-.331,4.655Zm-7.985-2.081a32.19,32.19,0,0,0,.318,4.655.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.268,32.268,0,0,0-.318,4.658Zm-7.746-2.836a33.85,33.85,0,0,0,.3,4.658.276.276,0,0,0,.255.258.272.272,0,0,0,.255-.258,36.041,36.041,0,0,0,0-9.314.272.272,0,0,0-.255-.258.276.276,0,0,0-.255.258,33.768,33.768,0,0,0-.3,4.655Zm-7.362-3.721a36.062,36.062,0,0,0,.286,4.655.244.244,0,1,0,.488,0,38,38,0,0,0,0-9.314.244.244,0,1,0-.488,0,36.112,36.112,0,0,0-.286,4.658Zm-6.657-4.863a37.464,37.464,0,0,0,.274,4.658.23.23,0,1,0,.456,0,40.22,40.22,0,0,0,0-9.317c-.019-.151-.113-.258-.23-.258a.258.258,0,0,0-.227.258,37.465,37.465,0,0,0-.274,4.658Zm54.33,16.037a27.281,27.281,0,0,0,.378,4.658.32.32,0,0,0,.321.258.323.323,0,0,0,.324-.258,28.918,28.918,0,0,0,0-9.317.323.323,0,0,0-.324-.255.32.32,0,0,0-.321.255,27.28,27.28,0,0,0-.378,4.658Zm-59.593-22.36a40.36,40.36,0,0,0,.255,4.658c.019.148.107.255.217.255s.2-.107.214-.255a42.166,42.166,0,0,0,0-9.317c-.016-.148-.107-.258-.214-.258s-.2.11-.217.258a40.361,40.361,0,0,0-.255,4.658Zm120.587,0a40.36,40.36,0,0,1-.255,4.658c-.019.148-.11.255-.217.255s-.2-.107-.217-.255a42.688,42.688,0,0,1,0-9.317c.019-.148.11-.258.217-.258s.2.11.217.258a40.361,40.361,0,0,1,.255,4.658Z" transform="translate(-150.772 -117.156)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12178" data-name="Path 12178" d="M168.63,238.062c0,17.422,27.466,31.542,61.35,31.542s61.346-14.12,61.346-31.542-27.466-31.539-61.346-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-151.822 -145.674)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12179" data-name="Path 12179" d="M168.466,237.58c0,17.23,27.913,31.2,62.347,31.2s62.35-13.966,62.35-31.2-27.913-31.2-62.35-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-152.174 -145.972)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12180" data-name="Path 12180" d="M170.339,233.84c-.4,15.379,24.551,28.5,55.734,29.323s56.789-10.988,57.192-26.364-24.551-28.5-55.734-29.323-56.789,10.988-57.192,26.364Z" transform="translate(-148.161 -143.712)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12181" data-name="Path 12181" d="M170.461,235.436c0,15.492,25.1,28.054,56.068,28.054S282.6,250.929,282.6,235.436s-25.1-28.054-56.071-28.054-56.068,12.562-56.068,28.054Z" transform="translate(-147.89 -143.829)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_12182" data-name="Path 12182" d="M282.493,237.168c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.4,14.4,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054S282.6,219.944,282.6,235.436a13.783,13.783,0,0,1-.107,1.731Z" transform="translate(-147.89 -143.829)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12183" data-name="Path 12183" d="M169.561,235.737c0,15.316,24.819,27.733,55.435,27.733s55.435-12.417,55.435-27.733S255.613,208,225,208s-55.435,12.417-55.435,27.737Z" transform="translate(-149.822 -142.502)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12184" data-name="Path 12184" d="M164.029,225.23c0,17.164,27.916,31.076,62.35,31.076s62.351-13.912,62.351-31.076l0-13.582h-124.7V225.23Z" transform="translate(-161.703 -134.668)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12185" data-name="Path 12185" d="M198.007,224.422q3.99.26,8.127.261A121.923,121.923,0,0,0,222.5,223.6v13.554a92.616,92.616,0,0,1-16.367,1.083c-8.823,0-23.176-1.684-32.678-4.6V220.063a107.541,107.541,0,0,0,24.551,4.359Z" transform="translate(-141.458 -116.596)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12186" data-name="Path 12186" d="M197.337,234.272c12.1-5.64,19.71-13.657,19.858-22.568l0,13.406c0,8.98-7.642,17.069-19.861,22.744V234.272Z" transform="translate(-90.171 -134.547)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-108)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12187" data-name="Path 12187" d="M176.833,230.5V244.1c-8.033-5.231-12.8-11.772-12.8-18.867V211.648c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-161.703 -134.668)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12188" data-name="Path 12188" d="M193.716,223.263q3.99.26,8.127.261v13.55a116.9,116.9,0,0,1-26.389-2.915V220.6a112.665,112.665,0,0,0,18.262,2.66Z" transform="translate(-137.167 -115.436)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12189" data-name="Path 12189" d="M233.923,242.122a28.354,28.354,0,0,1-.362,4.658.315.315,0,0,1-.62,0,30.139,30.139,0,0,1,0-9.314.315.315,0,0,1,.62,0,28.342,28.342,0,0,1,.362,4.655Zm8.209-.828a29.51,29.51,0,0,1-.349,4.655.3.3,0,0,1-.3.258.3.3,0,0,1-.293-.258,31.206,31.206,0,0,1,0-9.314.3.3,0,0,1,.293-.258.3.3,0,0,1,.3.258,29.521,29.521,0,0,1,.349,4.658Zm8.127-1.426a30.857,30.857,0,0,1-.334,4.658.293.293,0,0,1-.283.255.287.287,0,0,1-.28-.255,32.69,32.69,0,0,1,0-9.317.29.29,0,0,1,.28-.258.3.3,0,0,1,.283.258,30.856,30.856,0,0,1,.334,4.658Zm7.982-2.084a32.245,32.245,0,0,1-.318,4.658.269.269,0,0,1-.538,0,34.271,34.271,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.231,32.231,0,0,1,.318,4.655Zm7.746-2.833a33.85,33.85,0,0,1-.3,4.658.255.255,0,0,1-.51,0,36.065,36.065,0,0,1,0-9.317.255.255,0,1,1,.51,0,33.85,33.85,0,0,1,.3,4.658Zm7.362-3.724a36.15,36.15,0,0,1-.286,4.659.271.271,0,0,1-.242.258.266.266,0,0,1-.242-.258,38,38,0,0,1,0-9.314.266.266,0,0,1,.242-.258.271.271,0,0,1,.242.258,36.06,36.06,0,0,1,.286,4.655Zm6.657-4.863a38.091,38.091,0,0,1-.271,4.658.263.263,0,0,1-.23.258c-.116,0-.211-.107-.23-.258a40.192,40.192,0,0,1,0-9.314c.019-.151.113-.258.23-.258a.263.263,0,0,1,.23.258,38.037,38.037,0,0,1,.271,4.655Zm-63.978,15.757a28.333,28.333,0,0,0,.365,4.658.313.313,0,0,0,.617,0,29.88,29.88,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.321,28.321,0,0,0-.365,4.655Zm-8.206-.828a29.533,29.533,0,0,0,.346,4.655.3.3,0,0,0,.592,0,31.211,31.211,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.545,29.545,0,0,0-.346,4.658Zm-8.127-1.426a30.857,30.857,0,0,0,.334,4.658.283.283,0,0,0,.563,0,32.684,32.684,0,0,0,0-9.317.283.283,0,0,0-.563,0,30.856,30.856,0,0,0-.334,4.658Zm-7.982-2.084a32.245,32.245,0,0,0,.318,4.658.268.268,0,0,0,.535,0,34.264,34.264,0,0,0,0-9.314.268.268,0,0,0-.535,0,32.231,32.231,0,0,0-.318,4.655Zm-7.749-2.833a33.849,33.849,0,0,0,.3,4.658.257.257,0,0,0,.513,0,36.058,36.058,0,0,0,0-9.317.257.257,0,1,0-.513,0,33.849,33.849,0,0,0-.3,4.658Zm-7.359-3.724a35.588,35.588,0,0,0,.286,4.659.243.243,0,1,0,.485,0,38,38,0,0,0,0-9.314.243.243,0,1,0-.485,0,35.5,35.5,0,0,0-.286,4.655Zm-6.657-4.863a37.479,37.479,0,0,0,.271,4.658c.019.151.113.258.23.258a.263.263,0,0,0,.23-.258,40.192,40.192,0,0,0,0-9.314.263.263,0,0,0-.23-.258c-.116,0-.211.107-.23.258a37.427,37.427,0,0,0-.271,4.655Zm54.327,16.04a27.215,27.215,0,0,0,.378,4.655.323.323,0,0,0,.324.258.317.317,0,0,0,.321-.258,28.661,28.661,0,0,0,0-9.314.317.317,0,0,0-.321-.258.323.323,0,0,0-.324.258,27.281,27.281,0,0,0-.378,4.658Zm-59.593-22.36a40.3,40.3,0,0,0,.255,4.655c.019.151.11.258.217.258s.2-.107.217-.258a42.659,42.659,0,0,0,0-9.314c-.019-.151-.107-.258-.217-.258s-.2.107-.217.258a40.32,40.32,0,0,0-.255,4.658Zm120.587,0a40.3,40.3,0,0,1-.255,4.655c-.019.151-.107.258-.217.258s-.2-.107-.214-.258a42.659,42.659,0,0,1,0-9.314c.016-.151.107-.258.214-.258s.2.107.217.258a40.32,40.32,0,0,1,.255,4.658Z" transform="translate(-160.301 -127.194)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12190" data-name="Path 12190" d="M164.193,233.39c0,17.419,27.466,31.539,61.35,31.539s61.35-14.12,61.35-31.539-27.469-31.542-61.35-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-161.351 -155.714)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12191" data-name="Path 12191" d="M164.029,232.906c0,17.227,27.916,31.193,62.35,31.193s62.351-13.966,62.351-31.193-27.916-31.2-62.351-31.2-62.35,13.966-62.35,31.2Z" transform="translate(-161.703 -156.01)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12192" data-name="Path 12192" d="M165.9,229.167c-.4,15.376,24.551,28.5,55.734,29.32s56.792-10.985,57.195-26.364-24.551-28.5-55.737-29.32-56.789,10.985-57.192,26.364Z" transform="translate(-157.69 -153.75)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12193" data-name="Path 12193" d="M166.024,230.762c0,15.492,25.105,28.051,56.071,28.051s56.068-12.559,56.068-28.051-25.1-28.054-56.068-28.054-56.071,12.559-56.071,28.054Z" transform="translate(-157.419 -153.867)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12194" data-name="Path 12194" d="M278.059,232.494c-1.788-14.687-26.16-26.323-55.964-26.323s-54.176,11.637-55.964,26.323a14.437,14.437,0,0,1-.107-1.731c0-15.5,25.105-28.054,56.071-28.054s56.068,12.559,56.068,28.054a14.447,14.447,0,0,1-.1,1.731Z" transform="translate(-157.419 -153.867)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12195" data-name="Path 12195" d="M166.194,230.9c0,15.319,24.819,27.737,55.435,27.737s55.435-12.417,55.435-27.737-24.819-27.737-55.435-27.737S166.194,215.58,166.194,230.9Z" transform="translate(-157.053 -152.89)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12196" data-name="Path 12196" d="M166.987,220.553c0,17.164,27.913,31.079,62.351,31.079s62.347-13.916,62.347-31.079V206.974h-124.7v13.579Z" transform="translate(-155.35 -144.705)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12197" data-name="Path 12197" d="M200.962,219.747q3.99.264,8.13.264a121.922,121.922,0,0,0,16.367-1.086v13.553a92.912,92.912,0,0,1-16.367,1.083c-8.826,0-23.176-1.687-32.678-4.6V215.388a107.371,107.371,0,0,0,24.548,4.359Z" transform="translate(-135.105 -126.636)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12198" data-name="Path 12198" d="M200.294,229.6c12.1-5.64,19.713-13.661,19.861-22.568v13.4c0,8.98-7.642,17.073-19.861,22.744V229.6Z" transform="translate(-83.821 -144.585)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12199" data-name="Path 12199" d="M179.788,225.825v13.6c-8.029-5.231-12.8-11.772-12.8-18.87V206.974c.038,7.088,4.8,13.623,12.8,18.851Z" transform="translate(-155.35 -144.705)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12200" data-name="Path 12200" d="M196.671,218.588q3.99.264,8.13.264V232.4a116.9,116.9,0,0,1-26.389-2.915v-13.56a112.277,112.277,0,0,0,18.259,2.66Z" transform="translate(-130.814 -125.476)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12201" data-name="Path 12201" d="M236.881,237.45a28.017,28.017,0,0,1-.365,4.658.313.313,0,0,1-.617,0,29.9,29.9,0,0,1,0-9.317.313.313,0,0,1,.617,0,28.017,28.017,0,0,1,.365,4.658Zm8.209-.831a29.55,29.55,0,0,1-.349,4.658.3.3,0,0,1-.592,0,31.51,31.51,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.551,29.551,0,0,1,.349,4.658Zm8.124-1.423a30.839,30.839,0,0,1-.331,4.655.284.284,0,0,1-.567,0,32.663,32.663,0,0,1,0-9.314.284.284,0,0,1,.567,0,30.852,30.852,0,0,1,.331,4.658Zm7.985-2.084a32.287,32.287,0,0,1-.318,4.659.27.27,0,0,1-.538,0,34.288,34.288,0,0,1,0-9.317.269.269,0,0,1,.538,0,32.287,32.287,0,0,1,.318,4.658Zm7.746-2.836a33.815,33.815,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.413,36.413,0,0,1,0-9.314.257.257,0,1,1,.513,0,33.768,33.768,0,0,1,.3,4.655Zm7.359-3.721a35.588,35.588,0,0,1-.286,4.658.265.265,0,0,1-.242.255.27.27,0,0,1-.242-.255,38.024,38.024,0,0,1,0-9.317.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.587,35.587,0,0,1,.286,4.658Zm6.657-4.863a37.517,37.517,0,0,1-.271,4.658.23.23,0,1,1-.456,0,40.22,40.22,0,0,1,0-9.317c.019-.148.113-.258.227-.258a.263.263,0,0,1,.23.258,37.517,37.517,0,0,1,.271,4.658ZM218.987,237.45a28.01,28.01,0,0,0,.365,4.658.313.313,0,0,0,.617,0,30.155,30.155,0,0,0,0-9.317.313.313,0,0,0-.617,0,28.01,28.01,0,0,0-.365,4.658Zm-8.209-.831a29.55,29.55,0,0,0,.349,4.658.3.3,0,0,0,.592,0,31.51,31.51,0,0,0,0-9.317.3.3,0,0,0-.592,0,29.551,29.551,0,0,0-.349,4.658Zm-8.124-1.423a30.831,30.831,0,0,0,.331,4.655.284.284,0,0,0,.567,0,32.669,32.669,0,0,0,0-9.314.284.284,0,0,0-.567,0,30.844,30.844,0,0,0-.331,4.658Zm-7.985-2.084a32.287,32.287,0,0,0,.318,4.659.27.27,0,0,0,.538,0,34.288,34.288,0,0,0,0-9.317.269.269,0,0,0-.538,0,32.287,32.287,0,0,0-.318,4.658Zm-7.746-2.836a33.816,33.816,0,0,0,.3,4.658.257.257,0,1,0,.513,0,36.413,36.413,0,0,0,0-9.314.257.257,0,1,0-.513,0,33.768,33.768,0,0,0-.3,4.655Zm-7.359-3.721a35.588,35.588,0,0,0,.286,4.658.265.265,0,0,0,.242.255.27.27,0,0,0,.242-.255,38.024,38.024,0,0,0,0-9.317.271.271,0,0,0-.242-.258.266.266,0,0,0-.242.258,35.587,35.587,0,0,0-.286,4.658Zm-6.657-4.863a37.505,37.505,0,0,0,.271,4.658.23.23,0,1,0,.456,0,40.22,40.22,0,0,0,0-9.317c-.019-.148-.113-.258-.227-.258a.263.263,0,0,0-.23.258,37.506,37.506,0,0,0-.271,4.658Zm54.327,16.037a27.253,27.253,0,0,0,.378,4.658.331.331,0,0,0,.645,0,28.894,28.894,0,0,0,0-9.314.331.331,0,0,0-.645,0,27.215,27.215,0,0,0-.378,4.655ZM167.64,215.37a40.361,40.361,0,0,0,.255,4.658c.019.148.11.258.217.258s.2-.11.217-.258a42.677,42.677,0,0,0,0-9.317c-.019-.148-.11-.258-.217-.258s-.2.11-.217.258a40.361,40.361,0,0,0-.255,4.658Zm120.587,0a40.346,40.346,0,0,1-.255,4.658c-.019.148-.11.258-.217.258s-.2-.11-.217-.258a42.688,42.688,0,0,1,0-9.317c.019-.148.11-.258.217-.258s.2.11.217.258a40.346,40.346,0,0,1,.255,4.658Z" transform="translate(-153.948 -137.234)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12202" data-name="Path 12202" d="M167.151,228.716c0,17.419,27.466,31.539,61.35,31.539s61.35-14.12,61.35-31.539-27.469-31.542-61.35-31.542-61.35,14.12-61.35,31.542Z" transform="translate(-154.998 -165.752)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12203" data-name="Path 12203" d="M166.987,228.231c0,17.23,27.913,31.2,62.351,31.2s62.347-13.966,62.347-31.2-27.913-31.2-62.347-31.2-62.351,13.966-62.351,31.2Z" transform="translate(-155.35 -166.05)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12204" data-name="Path 12204" d="M168.86,224.491c-.4,15.379,24.551,28.508,55.734,29.323s56.789-10.988,57.192-26.364-24.551-28.5-55.734-29.323-56.789,10.988-57.192,26.364Z" transform="translate(-151.338 -163.79)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12205" data-name="Path 12205" d="M168.982,226.085c0,15.492,25.1,28.054,56.071,28.054s56.068-12.562,56.068-28.054-25.1-28.051-56.068-28.051-56.071,12.559-56.071,28.051Z" transform="translate(-151.066 -163.905)" fill-rule="evenodd" fill="url(#linear-gradient-40)"/>
      <path id="Path_12206" data-name="Path 12206" d="M168.092,226.186c0,15.316,24.819,27.737,55.435,27.737s55.435-12.42,55.435-27.737-24.819-27.737-55.435-27.737-55.435,12.417-55.435,27.737Z" transform="translate(-152.977 -163.014)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12207" data-name="Path 12207" d="M281.017,227.816c-1.788-14.684-26.163-26.32-55.964-26.32s-54.176,11.637-55.964,26.32a13.783,13.783,0,0,1-.107-1.731c0-15.492,25.1-28.051,56.071-28.051s56.068,12.559,56.068,28.051a14.4,14.4,0,0,1-.1,1.731Z" transform="translate(-151.066 -163.905)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12208" data-name="Path 12208" d="M163.29,215.881c0,17.164,27.913,31.076,62.347,31.076s62.35-13.912,62.35-31.076l0-13.582H163.29v13.582Z" transform="translate(-163.29 -154.745)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12209" data-name="Path 12209" d="M197.265,215.073q3.99.26,8.127.264a121.405,121.405,0,0,0,16.371-1.089v13.557a93,93,0,0,1-16.371,1.08c-8.823,0-23.176-1.684-32.675-4.6V210.714a107.516,107.516,0,0,0,24.548,4.359Z" transform="translate(-143.045 -136.673)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12210" data-name="Path 12210" d="M196.6,224.923c12.1-5.64,19.713-13.657,19.858-22.568l0,13.406c0,8.98-7.642,17.069-19.861,22.744V224.923Z" transform="translate(-91.76 -154.625)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12211" data-name="Path 12211" d="M176.091,221.153v13.594c-8.029-5.231-12.8-11.772-12.8-18.867V202.3c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-163.29 -154.745)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-20)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12212" data-name="Path 12212" d="M192.974,213.914q3.99.26,8.127.264v13.547a116.892,116.892,0,0,1-26.386-2.915V211.254a112.285,112.285,0,0,0,18.259,2.66Z" transform="translate(-138.754 -135.514)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12213" data-name="Path 12213" d="M233.184,232.773a28.326,28.326,0,0,1-.365,4.658.313.313,0,0,1-.617,0,29.88,29.88,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.314,28.314,0,0,1,.365,4.655Zm8.209-.828a29.508,29.508,0,0,1-.349,4.655.3.3,0,0,1-.592,0,31.211,31.211,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.521,29.521,0,0,1,.349,4.658Zm8.124-1.426a30.448,30.448,0,0,1-.334,4.658.29.29,0,0,1-.28.258.293.293,0,0,1-.283-.258,32.691,32.691,0,0,1,0-9.317.293.293,0,0,1,.283-.258.29.29,0,0,1,.28.258,30.448,30.448,0,0,1,.334,4.659Zm7.982-2.081a32.261,32.261,0,0,1-.315,4.655.283.283,0,0,1-.271.258.28.28,0,0,1-.268-.258,34.264,34.264,0,0,1,0-9.314.28.28,0,0,1,.268-.258.283.283,0,0,1,.271.258,32.274,32.274,0,0,1,.315,4.658Zm7.749-2.836a33.85,33.85,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.058,36.058,0,0,1,0-9.317.257.257,0,1,1,.513,0,33.85,33.85,0,0,1,.3,4.658Zm7.359-3.724a35.552,35.552,0,0,1-.286,4.658.266.266,0,0,1-.242.258.271.271,0,0,1-.242-.258,38.006,38.006,0,0,1,0-9.314.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.5,35.5,0,0,1,.286,4.655Zm6.657-4.86a37.463,37.463,0,0,1-.271,4.655c-.019.151-.113.258-.23.258a.258.258,0,0,1-.227-.258,39.73,39.73,0,0,1,0-9.314.258.258,0,0,1,.227-.258c.116,0,.211.107.23.258a37.443,37.443,0,0,1,.271,4.658ZM215.29,232.773a27.989,27.989,0,0,0,.365,4.658.313.313,0,0,0,.617,0,30.139,30.139,0,0,0,0-9.314.313.313,0,0,0-.617,0,27.977,27.977,0,0,0-.365,4.655Zm-8.209-.828a29.508,29.508,0,0,0,.349,4.655.3.3,0,0,0,.592,0,31.489,31.489,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.52,29.52,0,0,0-.349,4.658Zm-8.127-1.426a30.856,30.856,0,0,0,.334,4.658.285.285,0,0,0,.567,0,33,33,0,0,0,0-9.317.285.285,0,0,0-.567,0,30.856,30.856,0,0,0-.334,4.659Zm-7.982-2.081a32.232,32.232,0,0,0,.318,4.655.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.245,32.245,0,0,0-.318,4.658Zm-7.746-2.836a33.85,33.85,0,0,0,.3,4.658.255.255,0,1,0,.51,0,36.058,36.058,0,0,0,0-9.317.255.255,0,1,0-.51,0,33.85,33.85,0,0,0-.3,4.658Zm-7.362-3.724a36.112,36.112,0,0,0,.286,4.658.244.244,0,1,0,.488,0,38.421,38.421,0,0,0,0-9.314.244.244,0,1,0-.488,0,36.061,36.061,0,0,0-.286,4.655Zm-6.657-4.86a38.075,38.075,0,0,0,.271,4.655.263.263,0,0,0,.23.258c.116,0,.211-.107.23-.258a40.192,40.192,0,0,0,0-9.314c-.019-.151-.113-.258-.23-.258a.263.263,0,0,0-.23.258,38.053,38.053,0,0,0-.271,4.658Zm54.327,16.037a27.26,27.26,0,0,0,.381,4.658.32.32,0,0,0,.321.255.323.323,0,0,0,.324-.255,28.918,28.918,0,0,0,0-9.317.323.323,0,0,0-.324-.258.32.32,0,0,0-.321.258,27.261,27.261,0,0,0-.381,4.658Zm-59.59-22.36a40.3,40.3,0,0,0,.255,4.655c.019.151.107.258.217.258s.2-.107.214-.258a42.138,42.138,0,0,0,0-9.314c-.016-.151-.107-.258-.214-.258s-.2.107-.217.258a40.321,40.321,0,0,0-.255,4.658Zm120.587,0a40.319,40.319,0,0,1-.255,4.655c-.019.151-.11.258-.217.258s-.2-.107-.217-.258a42.649,42.649,0,0,1,0-9.314c.019-.151.11-.258.217-.258s.2.107.217.258a40.336,40.336,0,0,1,.255,4.658Z" transform="translate(-161.888 -147.272)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12214" data-name="Path 12214" d="M163.454,224.039c0,17.419,27.466,31.542,61.35,31.542s61.346-14.123,61.346-31.542S258.684,192.5,224.8,192.5s-61.35,14.12-61.35,31.539Z" transform="translate(-162.938 -175.789)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12215" data-name="Path 12215" d="M163.29,223.557c0,17.227,27.913,31.2,62.347,31.2s62.35-13.969,62.35-31.2-27.916-31.2-62.35-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-163.29 -176.088)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12216" data-name="Path 12216" d="M165.163,219.818c-.4,15.376,24.551,28.5,55.734,29.323s56.789-10.988,57.192-26.364-24.551-28.508-55.734-29.323-56.789,10.988-57.192,26.364Z" transform="translate(-159.277 -173.828)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12217" data-name="Path 12217" d="M165.285,221.413c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051-25.1-28.054-56.071-28.054-56.068,12.562-56.068,28.054Z" transform="translate(-159.006 -173.945)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12218" data-name="Path 12218" d="M277.317,223.145c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.407,14.407,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a13.783,13.783,0,0,1-.107,1.731Z" transform="translate(-159.006 -173.945)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12219" data-name="Path 12219" d="M165.4,221.043c0,15.316,24.819,27.737,55.435,27.737s55.435-12.42,55.435-27.737-24.819-27.737-55.435-27.737S165.4,205.723,165.4,221.043Z" transform="translate(-158.763 -174.059)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12220" data-name="Path 12220" d="M166.247,210.711c0,17.164,27.916,31.076,62.351,31.076s62.351-13.912,62.351-31.076l0-13.582h-124.7v13.582Z" transform="translate(-156.94 -165.848)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12221" data-name="Path 12221" d="M200.225,209.9q3.99.26,8.127.264a121.97,121.97,0,0,0,16.371-1.086v13.553a93.049,93.049,0,0,1-16.371,1.08c-8.823,0-23.176-1.684-32.678-4.6V205.544a107.575,107.575,0,0,0,24.551,4.359Z" transform="translate(-136.694 -147.776)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-18)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12222" data-name="Path 12222" d="M199.555,219.751c12.1-5.637,19.71-13.657,19.858-22.565l0,13.4c0,8.98-7.642,17.072-19.861,22.744V219.751Z" transform="translate(-85.408 -165.726)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12223" data-name="Path 12223" d="M179.051,215.983v13.6c-8.033-5.234-12.8-11.772-12.8-18.87V197.129c.041,7.092,4.806,13.626,12.8,18.854Z" transform="translate(-156.94 -165.848)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-12)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <path id="Path_12224" data-name="Path 12224" d="M195.932,208.744q3.99.26,8.127.264v13.547a116.872,116.872,0,0,1-26.386-2.912v-13.56a112.233,112.233,0,0,0,18.259,2.66Z" transform="translate(-132.401 -146.617)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12225" data-name="Path 12225" d="M236.142,227.606a28.293,28.293,0,0,1-.365,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.362,28.362,0,0,1,.365,4.658Zm8.206-.831a29.574,29.574,0,0,1-.346,4.658.3.3,0,0,1-.592,0,31.232,31.232,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.575,29.575,0,0,1,.346,4.658Zm8.127-1.426a30.826,30.826,0,0,1-.334,4.658.283.283,0,0,1-.563,0,32.669,32.669,0,0,1,0-9.314.283.283,0,0,1,.563,0,30.781,30.781,0,0,1,.334,4.655Zm7.982-2.081a32.2,32.2,0,0,1-.318,4.655.269.269,0,0,1-.538,0,34.264,34.264,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.245,32.245,0,0,1,.318,4.658Zm7.746-2.836a34.389,34.389,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.058,36.058,0,0,1,0-9.317.257.257,0,0,1,.513,0,34.39,34.39,0,0,1,.3,4.658Zm7.362-3.72a35.526,35.526,0,0,1-.286,4.655.243.243,0,1,1-.485,0,38,38,0,0,1,0-9.314.243.243,0,1,1,.485,0,35.542,35.542,0,0,1,.286,4.658Zm6.657-4.863a37.426,37.426,0,0,1-.271,4.655.231.231,0,1,1-.46,0,40.183,40.183,0,0,1,0-9.314.231.231,0,1,1,.46,0,37.516,37.516,0,0,1,.271,4.658Zm-63.975,15.757a28.308,28.308,0,0,0,.362,4.655.313.313,0,0,0,.617,0,29.876,29.876,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.377,28.377,0,0,0-.362,4.658Zm-8.209-.831a29.177,29.177,0,0,0,.349,4.658.3.3,0,0,0,.589,0,31.232,31.232,0,0,0,0-9.317.3.3,0,0,0-.589,0,29.177,29.177,0,0,0-.349,4.658Zm-8.127-1.426a30.818,30.818,0,0,0,.334,4.658.283.283,0,0,0,.563,0,32.674,32.674,0,0,0,0-9.314.283.283,0,0,0-.563,0,30.773,30.773,0,0,0-.334,4.655Zm-7.982-2.081a32.2,32.2,0,0,0,.318,4.655.269.269,0,0,0,.538,0,34.264,34.264,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.245,32.245,0,0,0-.318,4.658Zm-7.746-2.836a33.849,33.849,0,0,0,.3,4.658.274.274,0,0,0,.255.258.279.279,0,0,0,.255-.258,36.065,36.065,0,0,0,0-9.317.276.276,0,0,0-.255-.255.271.271,0,0,0-.255.255,33.85,33.85,0,0,0-.3,4.658Zm-7.362-3.72a35.537,35.537,0,0,0,.286,4.655.243.243,0,1,0,.485,0,38,38,0,0,0,0-9.314.243.243,0,1,0-.485,0,35.552,35.552,0,0,0-.286,4.658Zm-6.657-4.863a38.048,38.048,0,0,0,.271,4.655.231.231,0,1,0,.46,0,40.183,40.183,0,0,0,0-9.314.231.231,0,1,0-.46,0,38.141,38.141,0,0,0-.271,4.658Zm54.327,16.037a26.944,26.944,0,0,0,.381,4.659.329.329,0,0,0,.642,0,28.661,28.661,0,0,0,0-9.314.329.329,0,0,0-.642,0,26.878,26.878,0,0,0-.381,4.655Zm-59.59-22.36a39.58,39.58,0,0,0,.255,4.655c.016.151.107.258.217.258s.2-.107.214-.258a42.659,42.659,0,0,0,0-9.314c-.016-.148-.107-.258-.214-.258s-.2.11-.217.258a39.676,39.676,0,0,0-.255,4.658Zm120.584,0a39.565,39.565,0,0,1-.255,4.655c-.016.151-.107.258-.214.258s-.2-.107-.217-.258a42.659,42.659,0,0,1,0-9.314c.019-.148.107-.258.217-.258s.2.11.214.258a39.662,39.662,0,0,1,.255,4.658Z" transform="translate(-155.535 -158.375)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
      <path id="Path_12226" data-name="Path 12226" d="M166.411,218.869c0,17.422,27.469,31.542,61.35,31.542s61.35-14.12,61.35-31.542-27.466-31.539-61.35-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-156.587 -186.893)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12227" data-name="Path 12227" d="M166.247,218.387c0,17.23,27.916,31.2,62.351,31.2s62.351-13.966,62.351-31.2-27.916-31.2-62.351-31.2-62.351,13.966-62.351,31.2Z" transform="translate(-156.94 -187.191)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
      <path id="Path_12228" data-name="Path 12228" d="M168.12,214.647c-.4,15.379,24.551,28.5,55.734,29.323s56.792-10.988,57.195-26.364-24.551-28.5-55.734-29.323-56.792,10.988-57.195,26.364Z" transform="translate(-152.927 -184.931)" fill="#ffee76" fill-rule="evenodd"/>
      <path id="Path_12229" data-name="Path 12229" d="M168.243,216.243c0,15.492,25.1,28.051,56.068,28.051s56.071-12.559,56.071-28.051-25.105-28.054-56.071-28.054-56.068,12.562-56.068,28.054Z" transform="translate(-152.653 -185.048)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      <path id="Path_12230" data-name="Path 12230" d="M280.275,217.975c-1.788-14.687-26.16-26.32-55.964-26.32s-54.176,11.633-55.964,26.32a14.406,14.406,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a14.394,14.394,0,0,1-.107,1.731Z" transform="translate(-152.653 -185.048)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <path id="Path_12231" data-name="Path 12231" d="M224.24,189.29c-28.681,0-52.332,10.774-55.668,24.674a14.66,14.66,0,0,0-.3,1.646,14.984,14.984,0,0,0,.532,2.512,18.491,18.491,0,0,0,3.245,6.034l.494-.069L242.1,213.964l35.561-5.175c-7.23-11.309-28.41-19.5-53.418-19.5Z" transform="translate(-152.582 -182.683)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      <path id="Path_12232" data-name="Path 12232" d="M172.133,216.907c.359-13.689,20.689-24.545,47.148-26.188,3.031-.255,6.144-.387,9.317-.387,28.083,0,51.343,10.33,55.432,23.808.148.378.277.755.393,1.136l6.021-.878c-3.916-15.344-30.113-27.208-61.847-27.208-34.435,0-62.351,13.966-62.351,31.2v.085c.028,4.7,2.128,9.153,5.87,13.144l6.116-.888a25.563,25.563,0,0,1-1.822-2.065,18.491,18.491,0,0,1-3.245-6.034,14.987,14.987,0,0,1-.985-4.158,14.36,14.36,0,0,1-.047-1.564Z" transform="translate(-156.94 -187.191)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
    </g>
    <g id="Group_3369" data-name="Group 3369" transform="translate(251.727 319.656)">
      <path id="Path_12233" data-name="Path 12233" d="M313.1,259.688c-1.788-14.687-26.16-26.32-55.964-26.32S202.962,245,201.174,259.688a14.4,14.4,0,0,1-.107-1.731c0-15.492,25.105-28.054,56.071-28.054s56.068,12.562,56.068,28.054a14.394,14.394,0,0,1-.1,1.731Z" transform="translate(-183.151 -182.622)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <g id="Group_3365" data-name="Group 3365" transform="translate(11.637 44.139)">
        <path id="Path_12234" data-name="Path 12234" d="M199.072,252.424c0,17.164,27.916,31.076,62.35,31.076s62.351-13.912,62.351-31.076l0-13.582h-124.7v13.582Z" transform="translate(-199.072 -207.561)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_12235" data-name="Path 12235" d="M233.05,251.616q3.99.26,8.127.264a121.887,121.887,0,0,0,16.367-1.086v13.553a92.983,92.983,0,0,1-16.367,1.08c-8.823,0-23.176-1.684-32.678-4.6V247.257a107.548,107.548,0,0,0,24.551,4.359Z" transform="translate(-178.827 -189.489)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12236" data-name="Path 12236" d="M232.38,261.466c12.1-5.64,19.71-13.657,19.858-22.568l0,13.406c0,8.98-7.642,17.069-19.861,22.744V261.466Z" transform="translate(-127.54 -207.441)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-3)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12237" data-name="Path 12237" d="M211.876,257.7V271.29c-8.033-5.231-12.8-11.772-12.8-18.867V238.842c.038,7.092,4.8,13.623,12.8,18.854Z" transform="translate(-199.072 -207.561)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-4)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12238" data-name="Path 12238" d="M228.759,250.457q3.99.26,8.127.264v13.547a116.9,116.9,0,0,1-26.389-2.915V247.8a112.331,112.331,0,0,0,18.262,2.66Z" transform="translate(-174.536 -188.33)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12239" data-name="Path 12239" d="M268.963,269.319a28.315,28.315,0,0,1-.362,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,28.354,28.354,0,0,1,.362,4.658Zm8.209-.831a29.113,29.113,0,0,1-.349,4.655.3.3,0,0,1-.589,0,31.211,31.211,0,0,1,0-9.314.3.3,0,0,1,.589,0,29.184,29.184,0,0,1,.349,4.658Zm8.127-1.426a30.864,30.864,0,0,1-.334,4.658.291.291,0,0,1-.283.258.287.287,0,0,1-.28-.258,32.684,32.684,0,0,1,0-9.317.287.287,0,0,1,.28-.255.291.291,0,0,1,.283.255,30.865,30.865,0,0,1,.334,4.658Zm7.982-2.081a32.24,32.24,0,0,1-.318,4.655.269.269,0,0,1-.538,0,34.271,34.271,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.254,32.254,0,0,1,.318,4.658Zm7.746-2.836a33.849,33.849,0,0,1-.3,4.658.274.274,0,0,1-.255.258.279.279,0,0,1-.255-.258,36.058,36.058,0,0,1,0-9.317.276.276,0,0,1,.255-.255.271.271,0,0,1,.255.255,33.848,33.848,0,0,1,.3,4.658Zm7.362-3.72a36.1,36.1,0,0,1-.286,4.655.271.271,0,0,1-.242.258.266.266,0,0,1-.242-.258,38,38,0,0,1,0-9.314.266.266,0,0,1,.242-.258.271.271,0,0,1,.242.258,36.114,36.114,0,0,1,.286,4.659Zm6.657-4.863a38.037,38.037,0,0,1-.271,4.655.263.263,0,0,1-.23.258c-.116,0-.211-.107-.23-.258a40.193,40.193,0,0,1,0-9.314c.019-.151.113-.258.23-.258a.263.263,0,0,1,.23.258,38.092,38.092,0,0,1,.271,4.658Zm-63.978,15.757a28.287,28.287,0,0,0,.365,4.655.313.313,0,0,0,.617,0,29.881,29.881,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.326,28.326,0,0,0-.365,4.658Zm-8.206-.831a29.5,29.5,0,0,0,.346,4.655.3.3,0,0,0,.592,0,31.211,31.211,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.575,29.575,0,0,0-.346,4.658Zm-8.127-1.426a30.855,30.855,0,0,0,.334,4.658.283.283,0,0,0,.563,0,32.684,32.684,0,0,0,0-9.317.283.283,0,0,0-.563,0,30.856,30.856,0,0,0-.334,4.658Zm-7.982-2.081a32.24,32.24,0,0,0,.318,4.655.281.281,0,0,0,.268.258.286.286,0,0,0,.271-.258,34.613,34.613,0,0,0,0-9.314.286.286,0,0,0-.271-.258.281.281,0,0,0-.268.258,32.254,32.254,0,0,0-.318,4.658ZM219,262.146a34.346,34.346,0,0,0,.3,4.658.257.257,0,1,0,.513,0,36.065,36.065,0,0,0,0-9.317.257.257,0,0,0-.513,0,34.345,34.345,0,0,0-.3,4.658Zm-7.359-3.72a35.538,35.538,0,0,0,.286,4.655.243.243,0,1,0,.485,0,38,38,0,0,0,0-9.314.243.243,0,1,0-.485,0,35.553,35.553,0,0,0-.286,4.659Zm-6.657-4.863a37.427,37.427,0,0,0,.271,4.655c.019.151.113.258.23.258a.263.263,0,0,0,.23-.258,40.193,40.193,0,0,0,0-9.314.263.263,0,0,0-.23-.258c-.117,0-.211.107-.23.258a37.481,37.481,0,0,0-.271,4.658ZM259.316,269.6a26.937,26.937,0,0,0,.381,4.658.33.33,0,0,0,.642,0,28.68,28.68,0,0,0,0-9.317.329.329,0,0,0-.642,0,26.937,26.937,0,0,0-.381,4.658Zm-59.59-22.36a39.566,39.566,0,0,0,.255,4.655c.016.151.107.258.214.258s.2-.107.217-.258a42.659,42.659,0,0,0,0-9.314c-.019-.151-.107-.258-.217-.258s-.2.107-.214.258a39.66,39.66,0,0,0-.255,4.658Zm120.584,0a39.567,39.567,0,0,1-.255,4.655c-.019.151-.107.258-.217.258s-.2-.107-.214-.258a42.669,42.669,0,0,1,0-9.314c.016-.151.107-.258.214-.258s.2.107.217.258a39.661,39.661,0,0,1,.255,4.658Z" transform="translate(-197.667 -200.088)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_12240" data-name="Path 12240" d="M199.236,260.582c0,17.419,27.469,31.542,61.35,31.542s61.35-14.123,61.35-31.542-27.466-31.539-61.35-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-198.72 -228.606)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12241" data-name="Path 12241" d="M199.072,260.1c0,17.227,27.916,31.2,62.35,31.2s62.351-13.969,62.351-31.2-27.916-31.2-62.351-31.2-62.35,13.966-62.35,31.2Z" transform="translate(-199.072 -228.904)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_12242" data-name="Path 12242" d="M200.945,256.36c-.4,15.376,24.551,28.5,55.734,29.323s56.792-10.988,57.195-26.364S289.323,230.811,258.14,230s-56.792,10.988-57.195,26.364Z" transform="translate(-195.059 -226.644)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12243" data-name="Path 12243" d="M201.067,257.956c0,15.492,25.105,28.051,56.071,28.051s56.068-12.559,56.068-28.051S288.1,229.9,257.138,229.9s-56.071,12.562-56.071,28.054Z" transform="translate(-194.788 -226.761)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
      </g>
      <path id="Path_12244" data-name="Path 12244" d="M200.893,257.973c0,15.319,24.819,27.737,55.435,27.737s55.435-12.417,55.435-27.737-24.819-27.733-55.435-27.733-55.435,12.417-55.435,27.733Z" transform="translate(-183.525 -181.896)" fill-rule="evenodd" fill="url(#linear-gradient-7)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_3366" data-name="Group 3366" transform="translate(0 29.427)">
        <path id="Path_12245" data-name="Path 12245" d="M195.375,247.75c0,17.161,27.916,31.076,62.351,31.076s62.347-13.915,62.347-31.076V234.168h-124.7V247.75Z" transform="translate(-195.375 -202.887)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_12246" data-name="Path 12246" d="M229.353,246.942q3.99.26,8.127.261a121.922,121.922,0,0,0,16.367-1.086v13.553a92.918,92.918,0,0,1-16.367,1.083c-8.826,0-23.176-1.684-32.678-4.6V242.583a107.717,107.717,0,0,0,24.551,4.359Z" transform="translate(-175.13 -184.815)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12247" data-name="Path 12247" d="M228.683,256.792c12.1-5.64,19.71-13.657,19.858-22.568V247.63c0,8.977-7.639,17.069-19.858,22.744V256.792Z" transform="translate(-123.843 -202.767)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-154)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12248" data-name="Path 12248" d="M208.176,253.022v13.594c-8.029-5.231-12.8-11.772-12.8-18.867V234.168c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-195.375 -202.887)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12249" data-name="Path 12249" d="M225.062,245.785q3.99.26,8.127.261V259.6a116.9,116.9,0,0,1-26.389-2.915v-13.56a112.665,112.665,0,0,0,18.262,2.663Z" transform="translate(-170.839 -183.658)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12250" data-name="Path 12250" d="M265.269,264.642a28.017,28.017,0,0,1-.365,4.658.313.313,0,0,1-.617,0,30.135,30.135,0,0,1,0-9.314.313.313,0,0,1,.617,0,27.95,27.95,0,0,1,.365,4.655Zm8.209-.831a29.521,29.521,0,0,1-.349,4.658.3.3,0,0,1-.592,0,31.494,31.494,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.479,29.479,0,0,1,.349,4.655Zm8.124-1.423a30.807,30.807,0,0,1-.331,4.655.285.285,0,0,1-.567,0,32.973,32.973,0,0,1,0-9.314.285.285,0,0,1,.567,0,30.882,30.882,0,0,1,.331,4.658Zm7.985-2.084a32.278,32.278,0,0,1-.318,4.658.269.269,0,0,1-.538,0,34.264,34.264,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.2,32.2,0,0,1,.318,4.655Zm7.746-2.833a33.777,33.777,0,0,1-.3,4.655.255.255,0,1,1-.51,0,36.034,36.034,0,0,1,0-9.314.255.255,0,1,1,.51,0,33.825,33.825,0,0,1,.3,4.658Zm7.362-3.724a36.137,36.137,0,0,1-.286,4.658.244.244,0,1,1-.488,0,38.447,38.447,0,0,1,0-9.317.244.244,0,1,1,.488,0,36.137,36.137,0,0,1,.286,4.658Zm6.657-4.863a38.128,38.128,0,0,1-.271,4.658.263.263,0,0,1-.23.258c-.116,0-.211-.107-.23-.258a40.219,40.219,0,0,1,0-9.317.231.231,0,1,1,.46,0,38.128,38.128,0,0,1,.271,4.658Zm-63.978,15.757a28.355,28.355,0,0,0,.365,4.658.313.313,0,0,0,.617,0,29.881,29.881,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.287,28.287,0,0,0-.365,4.655Zm-8.206-.831a29.545,29.545,0,0,0,.346,4.658.3.3,0,0,0,.592,0,31.206,31.206,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.5,29.5,0,0,0-.346,4.655Zm-8.127-1.423a30.373,30.373,0,0,0,.334,4.655.288.288,0,0,0,.28.258.291.291,0,0,0,.283-.258,32.668,32.668,0,0,0,0-9.314.291.291,0,0,0-.283-.258.288.288,0,0,0-.28.258,30.447,30.447,0,0,0-.334,4.658Zm-7.982-2.084a32.307,32.307,0,0,0,.315,4.658.286.286,0,0,0,.271.258.283.283,0,0,0,.268-.258,34.264,34.264,0,0,0,0-9.314.28.28,0,0,0-.268-.258.283.283,0,0,0-.271.258,32.229,32.229,0,0,0-.315,4.655Zm-7.749-2.833a33.768,33.768,0,0,0,.3,4.655.257.257,0,1,0,.513,0,36.034,36.034,0,0,0,0-9.314.257.257,0,1,0-.513,0,33.816,33.816,0,0,0-.3,4.658Zm-7.359-3.724a35.587,35.587,0,0,0,.286,4.658.243.243,0,1,0,.485,0,38.032,38.032,0,0,0,0-9.317.243.243,0,1,0-.485,0,35.587,35.587,0,0,0-.286,4.658Zm-6.657-4.863a37.516,37.516,0,0,0,.271,4.658c.019.151.113.258.23.258a.263.263,0,0,0,.23-.258,40.219,40.219,0,0,0,0-9.317.231.231,0,1,0-.46,0,37.516,37.516,0,0,0-.271,4.658Zm54.327,16.04A27.242,27.242,0,0,0,256,269.58a.323.323,0,0,0,.324.258.32.32,0,0,0,.321-.258,28.661,28.661,0,0,0,0-9.314.32.32,0,0,0-.321-.258.323.323,0,0,0-.324.258,27.253,27.253,0,0,0-.378,4.658Zm-59.593-22.364a40.334,40.334,0,0,0,.255,4.658c.019.151.11.258.217.258s.2-.107.217-.258a42.649,42.649,0,0,0,0-9.314c-.019-.151-.11-.258-.217-.258s-.2.107-.217.258a40.279,40.279,0,0,0-.255,4.655Zm120.587,0a40.305,40.305,0,0,1-.255,4.658c-.019.151-.107.258-.217.258s-.2-.107-.214-.258a42.138,42.138,0,0,1,0-9.314c.016-.151.107-.258.214-.258s.2.107.217.258a40.25,40.25,0,0,1,.255,4.655Z" transform="translate(-193.973 -195.414)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_12251" data-name="Path 12251" d="M195.539,255.91c0,17.419,27.466,31.539,61.35,31.539s61.35-14.12,61.35-31.539-27.469-31.542-61.35-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-195.023 -223.934)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12252" data-name="Path 12252" d="M195.375,255.423c0,17.23,27.916,31.2,62.351,31.2s62.347-13.966,62.347-31.2S292.16,224.23,257.726,224.23s-62.351,13.966-62.351,31.193Z" transform="translate(-195.375 -224.23)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_12253" data-name="Path 12253" d="M197.248,251.687c-.4,15.376,24.551,28.5,55.734,29.32s56.792-10.985,57.192-26.364-24.548-28.5-55.734-29.32-56.789,10.985-57.192,26.364Z" transform="translate(-191.362 -221.97)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12254" data-name="Path 12254" d="M197.37,253.279c0,15.5,25.105,28.054,56.071,28.054s56.068-12.559,56.068-28.054-25.1-28.051-56.068-28.051-56.071,12.559-56.071,28.051Z" transform="translate(-191.091 -222.087)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
        <path id="Path_12255" data-name="Path 12255" d="M309.405,255.014c-1.788-14.687-26.163-26.323-55.964-26.323s-54.176,11.637-55.964,26.323a14.459,14.459,0,0,1-.107-1.734c0-15.492,25.105-28.051,56.071-28.051s56.068,12.559,56.068,28.051a14.458,14.458,0,0,1-.1,1.734Z" transform="translate(-191.091 -222.087)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      </g>
      <path id="Path_12256" data-name="Path 12256" d="M197.724,253.247c0,15.319,24.819,27.737,55.435,27.737S308.6,268.566,308.6,253.247s-24.822-27.737-55.438-27.737-55.435,12.417-55.435,27.737Z" transform="translate(-190.33 -192.054)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_3367" data-name="Group 3367" transform="translate(9.311 14.712)">
        <path id="Path_12257" data-name="Path 12257" d="M198.333,243.075c0,17.164,27.913,31.079,62.347,31.079s62.35-13.916,62.35-31.079V229.493h-124.7v13.582Z" transform="translate(-198.333 -198.212)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_12258" data-name="Path 12258" d="M232.308,242.267q3.99.26,8.127.264a121.935,121.935,0,0,0,16.371-1.086V255a92.934,92.934,0,0,1-16.371,1.083c-8.823,0-23.176-1.687-32.675-4.6V237.908a107.519,107.519,0,0,0,24.548,4.359Z" transform="translate(-178.088 -180.14)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12259" data-name="Path 12259" d="M231.64,252.115c12.1-5.637,19.713-13.657,19.861-22.565v13.4c0,8.98-7.642,17.069-19.861,22.744V252.115Z" transform="translate(-126.803 -198.09)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-27)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12260" data-name="Path 12260" d="M211.134,248.347v13.6c-8.029-5.234-12.8-11.772-12.8-18.87V229.493c.038,7.092,4.8,13.626,12.8,18.854Z" transform="translate(-198.333 -198.212)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-164)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12261" data-name="Path 12261" d="M228.017,241.108q3.99.26,8.127.264v13.55a116.884,116.884,0,0,1-26.386-2.915v-13.56a112.29,112.29,0,0,0,18.259,2.66Z" transform="translate(-173.797 -178.981)" fill-rule="evenodd" fill="url(#linear-gradient-29)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12262" data-name="Path 12262" d="M268.227,259.97a27.951,27.951,0,0,1-.365,4.655.313.313,0,0,1-.617,0,29.881,29.881,0,0,1,0-9.314.313.313,0,0,1,.617,0,27.989,27.989,0,0,1,.365,4.658Zm8.209-.831a29.559,29.559,0,0,1-.349,4.659.3.3,0,0,1-.592,0,31.233,31.233,0,0,1,0-9.317.3.3,0,0,1,.592,0,29.559,29.559,0,0,1,.349,4.658Zm8.124-1.426a30.851,30.851,0,0,1-.331,4.658.285.285,0,0,1-.567,0,32.668,32.668,0,0,1,0-9.314.285.285,0,0,1,.567,0,30.807,30.807,0,0,1,.331,4.655Zm7.982-2.081a32.229,32.229,0,0,1-.315,4.655.283.283,0,0,1-.271.258.281.281,0,0,1-.268-.258,34.271,34.271,0,0,1,0-9.314.281.281,0,0,1,.268-.258.283.283,0,0,1,.271.258,32.275,32.275,0,0,1,.315,4.658Zm7.749-2.836a33.85,33.85,0,0,1-.3,4.658.257.257,0,1,1-.513,0,36.058,36.058,0,0,1,0-9.317.257.257,0,0,1,.513,0,33.85,33.85,0,0,1,.3,4.658Zm7.359-3.72a35.5,35.5,0,0,1-.286,4.655.266.266,0,0,1-.242.258.271.271,0,0,1-.242-.258,38,38,0,0,1,0-9.314.271.271,0,0,1,.242-.258.266.266,0,0,1,.242.258,35.553,35.553,0,0,1,.286,4.659Zm6.657-4.863a37.413,37.413,0,0,1-.271,4.655c-.019.151-.113.258-.23.258s-.208-.107-.227-.258a39.73,39.73,0,0,1,0-9.314c.019-.151.113-.258.227-.258s.211.107.23.258a37.506,37.506,0,0,1,.271,4.658ZM250.333,259.97a27.957,27.957,0,0,0,.365,4.655.313.313,0,0,0,.617,0,30.135,30.135,0,0,0,0-9.314.313.313,0,0,0-.617,0,28,28,0,0,0-.365,4.658Zm-8.209-.831a29.552,29.552,0,0,0,.349,4.659.3.3,0,0,0,.592,0,31.516,31.516,0,0,0,0-9.317.3.3,0,0,0-.592,0,29.551,29.551,0,0,0-.349,4.658ZM234,257.713a30.852,30.852,0,0,0,.33,4.658.285.285,0,0,0,.567,0,32.973,32.973,0,0,0,0-9.314.285.285,0,0,0-.567,0,30.807,30.807,0,0,0-.33,4.655Zm-7.985-2.081a32.208,32.208,0,0,0,.318,4.655.269.269,0,0,0,.538,0,34.271,34.271,0,0,0,0-9.314.269.269,0,0,0-.538,0,32.254,32.254,0,0,0-.318,4.658Zm-7.746-2.836a33.86,33.86,0,0,0,.3,4.658.276.276,0,0,0,.255.258.272.272,0,0,0,.255-.258,36.051,36.051,0,0,0,0-9.317.271.271,0,0,0-.255-.255.276.276,0,0,0-.255.255,33.86,33.86,0,0,0-.3,4.658Zm-7.359-3.72a36.1,36.1,0,0,0,.283,4.655.244.244,0,1,0,.488,0,38,38,0,0,0,0-9.314.244.244,0,1,0-.488,0,36.151,36.151,0,0,0-.283,4.659Zm-6.657-4.863a37.425,37.425,0,0,0,.271,4.655c.019.151.113.258.227.258s.211-.107.23-.258a40.192,40.192,0,0,0,0-9.314c-.019-.151-.113-.258-.23-.258s-.208.107-.227.258a37.517,37.517,0,0,0-.271,4.658Zm54.327,16.037a27.281,27.281,0,0,0,.378,4.658.323.323,0,0,0,.321.258.326.326,0,0,0,.324-.258,28.913,28.913,0,0,0,0-9.317.323.323,0,0,0-.324-.255.32.32,0,0,0-.321.255,27.281,27.281,0,0,0-.378,4.658Zm-59.593-22.36a40.263,40.263,0,0,0,.255,4.655c.019.151.11.258.217.258s.2-.107.217-.258a42.67,42.67,0,0,0,0-9.314c-.019-.151-.11-.258-.217-.258s-.2.107-.217.258a40.361,40.361,0,0,0-.255,4.659Zm120.587,0a40.264,40.264,0,0,1-.255,4.655c-.019.151-.11.258-.217.258s-.2-.107-.217-.258a42.66,42.66,0,0,1,0-9.314c.019-.151.11-.258.217-.258s.2.107.217.258a40.362,40.362,0,0,1,.255,4.659Z" transform="translate(-196.931 -190.739)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_12263" data-name="Path 12263" d="M198.5,251.233c0,17.422,27.466,31.542,61.35,31.542s61.346-14.12,61.346-31.542-27.466-31.539-61.346-31.539-61.35,14.12-61.35,31.539Z" transform="translate(-197.981 -219.257)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12264" data-name="Path 12264" d="M198.333,250.751c0,17.23,27.913,31.2,62.347,31.2s62.35-13.966,62.35-31.2-27.913-31.2-62.35-31.2-62.347,13.966-62.347,31.2Z" transform="translate(-198.333 -219.555)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_12265" data-name="Path 12265" d="M200.206,247.011c-.4,15.379,24.551,28.5,55.734,29.323s56.789-10.988,57.192-26.364-24.551-28.5-55.734-29.323-56.789,10.988-57.192,26.364Z" transform="translate(-194.32 -217.295)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12266" data-name="Path 12266" d="M200.328,248.607c0,15.492,25.1,28.054,56.068,28.054s56.071-12.562,56.071-28.054-25.1-28.054-56.071-28.054-56.068,12.562-56.068,28.054Z" transform="translate(-194.049 -217.412)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
        <path id="Path_12267" data-name="Path 12267" d="M312.363,250.339c-1.791-14.687-26.163-26.32-55.967-26.32s-54.176,11.633-55.964,26.32a14.407,14.407,0,0,1-.1-1.731c0-15.492,25.1-28.054,56.068-28.054s56.071,12.562,56.071,28.054a14.394,14.394,0,0,1-.1,1.731Z" transform="translate(-194.049 -217.412)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      </g>
      <path id="Path_12268" data-name="Path 12268" d="M199.377,248.563c0,15.319,24.822,27.737,55.438,27.737s55.435-12.417,55.435-27.737-24.819-27.737-55.435-27.737-55.438,12.42-55.438,27.737Z" transform="translate(-186.78 -202.114)" fill-rule="evenodd" fill="url(#linear-gradient-41)" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_3368" data-name="Group 3368" transform="translate(16.292)">
        <path id="Path_12269" data-name="Path 12269" d="M200.551,238.4c0,17.161,27.916,31.076,62.35,31.076s62.347-13.916,62.347-31.076V224.819h-124.7V238.4Z" transform="translate(-200.551 -193.538)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_12270" data-name="Path 12270" d="M234.529,237.593q3.99.26,8.127.261a121.922,121.922,0,0,0,16.367-1.086v13.553a92.608,92.608,0,0,1-16.367,1.083c-8.823,0-23.176-1.684-32.678-4.6V233.234a107.714,107.714,0,0,0,24.551,4.359Z" transform="translate(-180.306 -175.466)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12271" data-name="Path 12271" d="M233.859,247.443c12.1-5.64,19.71-13.657,19.858-22.568v13.406c0,8.98-7.639,17.069-19.858,22.744V247.443Z" transform="translate(-129.019 -193.418)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-154)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12272" data-name="Path 12272" d="M213.352,243.673v13.594c-8.029-5.231-12.8-11.772-12.8-18.867V224.819c.038,7.088,4.8,13.623,12.8,18.854Z" transform="translate(-200.551 -193.538)" fill-rule="evenodd" opacity="0.5" fill="url(#linear-gradient-91)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_12273" data-name="Path 12273" d="M230.238,236.434q3.99.26,8.127.261v13.55a116.892,116.892,0,0,1-26.389-2.915V233.774a112.673,112.673,0,0,0,18.262,2.66Z" transform="translate(-176.015 -174.307)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12274" data-name="Path 12274" d="M270.445,255.293a28.348,28.348,0,0,1-.362,4.658.314.314,0,0,1-.312.258.311.311,0,0,1-.309-.258,30.14,30.14,0,0,1,0-9.314.311.311,0,0,1,.309-.258.314.314,0,0,1,.312.258,28.308,28.308,0,0,1,.362,4.655Zm8.209-.831a29.529,29.529,0,0,1-.349,4.659.3.3,0,0,1-.592,0,31.489,31.489,0,0,1,0-9.314.3.3,0,0,1,.592,0,29.516,29.516,0,0,1,.349,4.655Zm8.127-1.423a30.856,30.856,0,0,1-.334,4.658.293.293,0,0,1-.283.255.287.287,0,0,1-.28-.255,32.69,32.69,0,0,1,0-9.317.288.288,0,0,1,.28-.258.293.293,0,0,1,.283.258,30.855,30.855,0,0,1,.334,4.658Zm7.982-2.084a32.246,32.246,0,0,1-.318,4.658.269.269,0,0,1-.538,0,34.258,34.258,0,0,1,0-9.314.269.269,0,0,1,.538,0,32.2,32.2,0,0,1,.318,4.655Zm7.746-2.833a33.767,33.767,0,0,1-.3,4.655.255.255,0,1,1-.51,0,36.041,36.041,0,0,1,0-9.314.255.255,0,1,1,.51,0,33.849,33.849,0,0,1,.3,4.658Zm7.362-3.724a36.137,36.137,0,0,1-.286,4.658.27.27,0,0,1-.242.258.266.266,0,0,1-.242-.258,38.024,38.024,0,0,1,0-9.317.265.265,0,0,1,.242-.255.27.27,0,0,1,.242.255,36.137,36.137,0,0,1,.286,4.658Zm6.657-4.863a38.091,38.091,0,0,1-.271,4.658.263.263,0,0,1-.23.258c-.116,0-.211-.107-.23-.258a40.2,40.2,0,0,1,0-9.314c.019-.151.113-.258.23-.258a.263.263,0,0,1,.23.258,38.037,38.037,0,0,1,.271,4.655Zm-63.978,15.757a28.326,28.326,0,0,0,.365,4.658.313.313,0,0,0,.617,0,29.876,29.876,0,0,0,0-9.314.313.313,0,0,0-.617,0,28.287,28.287,0,0,0-.365,4.655Zm-8.206-.831a29.538,29.538,0,0,0,.346,4.659.3.3,0,0,0,.592,0,31.206,31.206,0,0,0,0-9.314.3.3,0,0,0-.592,0,29.525,29.525,0,0,0-.346,4.655Zm-8.127-1.423a30.44,30.44,0,0,0,.334,4.658.287.287,0,0,0,.28.255.29.29,0,0,0,.283-.255,32.69,32.69,0,0,0,0-9.317.291.291,0,0,0-.283-.258.287.287,0,0,0-.28.258,30.439,30.439,0,0,0-.334,4.658Zm-7.982-2.084a32.246,32.246,0,0,0,.318,4.658.268.268,0,0,0,.535,0,34.271,34.271,0,0,0,0-9.314.268.268,0,0,0-.535,0,32.2,32.2,0,0,0-.318,4.655Zm-7.749-2.833a33.777,33.777,0,0,0,.3,4.655.257.257,0,1,0,.513,0,36.041,36.041,0,0,0,0-9.314.257.257,0,1,0-.513,0,33.859,33.859,0,0,0-.3,4.658Zm-7.359-3.724a35.587,35.587,0,0,0,.286,4.658.243.243,0,1,0,.485,0,38.024,38.024,0,0,0,0-9.317.243.243,0,1,0-.485,0,35.587,35.587,0,0,0-.286,4.658Zm-6.657-4.863a37.479,37.479,0,0,0,.271,4.658c.019.151.113.258.23.258a.263.263,0,0,0,.23-.258,40.192,40.192,0,0,0,0-9.314.263.263,0,0,0-.23-.258c-.116,0-.211.107-.23.258a37.427,37.427,0,0,0-.271,4.655Zm54.327,16.04a27.208,27.208,0,0,0,.378,4.655.323.323,0,0,0,.324.258.32.32,0,0,0,.321-.258,28.665,28.665,0,0,0,0-9.314.32.32,0,0,0-.321-.258.323.323,0,0,0-.324.258,27.247,27.247,0,0,0-.378,4.658ZM201.2,233.212a40.32,40.32,0,0,0,.255,4.658c.019.151.11.258.217.258s.2-.107.217-.258a42.659,42.659,0,0,0,0-9.314c-.019-.151-.11-.258-.217-.258s-.2.107-.217.258a40.263,40.263,0,0,0-.255,4.655Zm120.587,0a40.32,40.32,0,0,1-.255,4.658c-.019.151-.107.258-.217.258s-.2-.107-.214-.258a42.659,42.659,0,0,1,0-9.314c.016-.151.107-.258.214-.258s.2.107.217.258a40.263,40.263,0,0,1,.255,4.655Z" transform="translate(-199.149 -186.065)" fill="#663d1f" fill-rule="evenodd" opacity="0.65"/>
        <path id="Path_12275" data-name="Path 12275" d="M200.715,246.561c0,17.419,27.466,31.539,61.35,31.539s61.35-14.12,61.35-31.539-27.469-31.542-61.35-31.542-61.35,14.123-61.35,31.542Z" transform="translate(-200.199 -214.585)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12276" data-name="Path 12276" d="M200.551,246.074c0,17.23,27.916,31.2,62.35,31.2s62.347-13.966,62.347-31.2-27.913-31.193-62.347-31.193-62.35,13.966-62.35,31.193Z" transform="translate(-200.551 -214.881)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <path id="Path_12277" data-name="Path 12277" d="M202.424,242.338c-.4,15.376,24.551,28.5,55.734,29.32s56.792-10.985,57.192-26.364-24.548-28.5-55.734-29.32-56.789,10.985-57.192,26.364Z" transform="translate(-196.538 -212.621)" fill="#ffee76" fill-rule="evenodd"/>
        <path id="Path_12278" data-name="Path 12278" d="M202.546,243.93c0,15.5,25.105,28.054,56.071,28.054s56.068-12.559,56.068-28.054-25.1-28.051-56.068-28.051-56.071,12.559-56.071,28.051Z" transform="translate(-196.267 -212.738)" fill-rule="evenodd" fill="url(#linear-gradient-15)"/>
        <path id="Path_12279" data-name="Path 12279" d="M314.581,245.665c-1.788-14.687-26.16-26.323-55.964-26.323s-54.176,11.637-55.964,26.323a14.46,14.46,0,0,1-.107-1.734c0-15.492,25.105-28.051,56.071-28.051s56.068,12.559,56.068,28.051a14.446,14.446,0,0,1-.1,1.734Z" transform="translate(-196.267 -212.738)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
        <path id="Path_12280" data-name="Path 12280" d="M258.544,216.979c-28.681,0-52.335,10.777-55.668,24.677a14.66,14.66,0,0,0-.3,1.646,14.816,14.816,0,0,0,.532,2.512,18.434,18.434,0,0,0,3.245,6.034l.494-.072,69.549-10.12,35.558-5.175c-7.227-11.309-28.41-19.5-53.415-19.5Z" transform="translate(-196.194 -210.375)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
        <path id="Path_12281" data-name="Path 12281" d="M206.437,244.6c.359-13.689,20.689-24.548,47.148-26.191q4.547-.378,9.317-.384c28.083,0,51.343,10.33,55.432,23.808.145.375.277.752.393,1.133l6.018-.875c-3.912-15.348-30.11-27.208-61.844-27.208-34.435,0-62.35,13.966-62.35,31.193v.088c.025,4.7,2.128,9.153,5.87,13.144l6.113-.891a25.479,25.479,0,0,1-1.819-2.062,18.434,18.434,0,0,1-3.245-6.034,14.819,14.819,0,0,1-.985-4.158,14.392,14.392,0,0,1-.047-1.564Z" transform="translate(-200.551 -214.881)" fill-rule="evenodd" fill="url(#linear-gradient-2)" style="mix-blend-mode: screen;isolation: isolate"/>
      </g>
    </g>
  </g>
</svg>
