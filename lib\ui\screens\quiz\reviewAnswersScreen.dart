import 'dart:developer';

import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:flutterquiz/app/appLocalization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/musicPlayer/musicPlayerCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/questionsCubit.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/guessTheWordQuestion.dart';
import 'package:flutterquiz/features/quiz/models/question.dart' hide Options;
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/models/result_detail.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionCubit.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';
import 'package:flutterquiz/globals.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/musicPlayerContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/questionContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/reportQuestionPopup.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart';
import 'package:flutterquiz/ui/widgets/customBackButton.dart';

import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/pageBackgroundGradientContainer.dart';
import 'package:flutterquiz/ui/widgets/roundedAppbar.dart';
import 'package:flutterquiz/utils/answerEncryption.dart';
import 'package:flutterquiz/utils/errorMessageKeys.dart';
import 'package:flutterquiz/utils/stringLabels.dart';
import 'package:flutterquiz/utils/uiUtils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ReviewAnswersScreen extends StatefulWidget {
  final List<Question> questions;
  final String rid;
  final String quizId;
  final List<GuessTheWordQuestion> guessTheWordQuestions;

  ReviewAnswersScreen({
    Key? key,
    required this.questions,
    required this.guessTheWordQuestions,
    required this.rid,
    required this.quizId,
  }) : super(key: key);

  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments as Map?;
    //arguments will map and keys of the map are following
    //questions and guessTheWordQuestions
    return CupertinoPageRoute(
        builder: (_) => MultiBlocProvider(
                providers: [
                  BlocProvider<UpdateBookmarkCubit>(
                    create: (context) =>
                        UpdateBookmarkCubit(BookmarkRepository()),
                  ),
                  BlocProvider<ReportQuestionCubit>(
                      create: (_) =>
                          ReportQuestionCubit(ReportQuestionRepository())),
                  BlocProvider<QuestionsCubit>(
                      create: (_) => QuestionsCubit(QuizRepository())),
                ],
                child: ReviewAnswersScreen(
                  rid: arguments!['rid'],
                  quizId: arguments['quizId'],
                  guessTheWordQuestions: arguments['guessTheWordQuestions'] ??
                      List<GuessTheWordQuestion>.from([]),
                  questions: arguments['questions'] ?? List<Question>.from([]),
                )));
  }

  @override
  _ReviewAnswersScreenState createState() => _ReviewAnswersScreenState();
}

class _ReviewAnswersScreenState extends State<ReviewAnswersScreen> {
  int _currentSelectedTab = 1;
  PageController? _pageController;
  int _currentIndex = 0;
  String popBack = '';
  List<GlobalKey<MusicPlayerContainerState>> musicPlayerContainerKeys = [];
  List<ResultDetail> resultQuestion = [];
  Map countQuestion = {};
  Future<void> getRollBackPoint() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    popBack = preferences.getString("popBack") ?? "";
  }

  @override
  void initState() {
    _pageController = PageController();
    if (_hasAudioQuestion()) {
      widget.questions.forEach((element) {
        musicPlayerContainerKeys.add(GlobalKey<MusicPlayerContainerState>());
      });
    }
    getQuestion();
    getRollBackPoint();
    super.initState();
  }

  void getQuestion() {
    context.read<QuestionsCubit>().getResultQuestion(widget.rid);
  }

  bool _hasAudioQuestion() {
    if (widget.questions.isNotEmpty) {
      return widget.questions.first.audio!.isNotEmpty;
    }
    return false;
  }

  void showNotes() {
    if (widget.questions[_currentIndex].note!.isEmpty) {
      UiUtils.setSnackbar(
          AppLocalization.of(context)!.getTranslatedValues(
              convertErrorCodeToLanguageKey(notesNotAvailableCode))!,
          context,
          false);
      return;
    }
    showModalBottomSheet(
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      context: context,
      builder: (context) => Container(
          constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * (0.6)),
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: 10.0,
                ),
                Text(
                  AppLocalization.of(context)!.getTranslatedValues("notesLbl")!,
                  style: TextStyle(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).primaryColor),
                ),
                SizedBox(
                  height: 10.0,
                ),
                Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * (0.1)),
                  child: Text(
                    "${widget.questions[_currentIndex].question}",
                    style: TextStyle(
                        fontSize: 18.0,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).primaryColor),
                  ),
                ),
                SizedBox(
                  height: 5.0,
                ),
                Divider(),
                Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * (0.1)),
                  child: Text(
                    "${widget.questions[_currentIndex].note}",
                    style: TextStyle(
                        fontSize: 17.0, color: Theme.of(context).primaryColor),
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * (0.1),
                ),
              ],
            ),
          )),
    );
  }

  int getQuestionsLength() {
    return resultQuestion.length;
  }

  bool isGuessTheWordQuizModule() {
    return widget.guessTheWordQuestions.isNotEmpty;
  }

  Color getOptionColor(ResultDetail question, Options option) {
    List<String>? selectedOptionId = question.selectedOptions;
    if (selectedOptionId!.isNotEmpty) {
      if (question.questionType == "Câu hỏi một lựa chọn") {
// if given answer is correct
        if (double.parse(option.score ?? "0") > 0) {
          //if given option is same as answer
          if (question.selectedOptions!.contains(option.oid))
            return Colors.green;
          else
            return Colors.green;
          //return Theme.of(context).colorScheme.secondary;
          //color will not change for other options
          // return Theme.of(context).colorScheme.secondary;
        } else {
          //option id is same as given answer then change color to red
          if (question.selectedOptions!.contains(option.oid) &&
              int.parse(option.score ?? "0") == 0) {
            return Colors.red;
          }
          //if given option id is correct as same answer then change color to green
          else if (question.selectedOptions!.contains(option.oid) &&
              int.parse(option.score ?? "0") > 0) {
            return Colors.green;
          }
          //do not change color
          return Theme.of(context).colorScheme.secondary;
        }
      } else {
        // Câu hỏi multi
        double score = 0;
        int countCorrect = 0;
        for (String value in selectedOptionId) {
          int index =
              question.options!.indexWhere((element) => element.oid == value);
          double optionScore = index != -1
              ? double.parse(question.options![index].score ?? "0")
              : 0;
          if (optionScore != 0) countCorrect++;
          score += optionScore;
        }
        if (score >= 0.9 && selectedOptionId.length == countCorrect) {
          if (question.selectedOptions!.contains(option.oid)) {
            return Colors.green;
          } else {
            //return Colors.green;
            return Theme.of(context).colorScheme.secondary;
          }
        } else {
          //câu sai được chọn
          if (question.selectedOptions!.contains(option.oid) &&
              double.parse(option.score ?? "0") == 0) {
            return Colors.red;
          }
          //câu đúng được chọn
          else if (question.selectedOptions!.contains(option.oid) &&
              double.parse(option.score ?? "0") > 0) {
            //để xanh cho giống exam sim
            return Colors.green;
          }
          //câu đúng không chọn
          else if (!question.selectedOptions!.contains(option.oid) &&
              double.parse(option.score ?? "0") > 0) {
            return Colors.green;
            //return Theme.of(context).colorScheme.secondary;
          }
          //do not change color
          return Theme.of(context).colorScheme.secondary;
        }
      }
    } else {
      // if answer not given then only show correct answer
      if (double.parse(option.score ?? "0") > 0) {
        //return Colors.green;
        return Theme.of(context).colorScheme.secondary;
      }
      return Theme.of(context).colorScheme.secondary;
    }

    @override
    Widget build(BuildContext context) {
      // TODO: implement build
      throw UnimplementedError();
    }
  }

  Color getOptionTextColor(Question question, String? optionId) {
    String correctAnswerId = AnswerEncryption.decryptCorrectAnswer(
        rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
        correctAnswer: question.correctAnswer!);
    if (question.attempted) {
      // if given answer is correct
      if (question.submittedAnswerId == correctAnswerId) {
        //if given option is same as answer
        if (question.submittedAnswerId == optionId) {
          return Theme.of(context).primaryColor;
        }
        //color will not change for other options
        return Theme.of(context).colorScheme.secondary;
      } else {
        //option id is same as given answer then change color to red
        if (question.submittedAnswerId == optionId) {
          return Theme.of(context).primaryColor;
        }
        //if given option id is correct as same answer then change color to green
        else if (correctAnswerId == optionId) {
          return Theme.of(context).primaryColor;
        }
        //do not change color
        return Theme.of(context).colorScheme.secondary;
      }
    } else {
      // if answer not given then only show correct answer
      if (correctAnswerId == optionId) {
        return Theme.of(context).primaryColor;
      }
      return Theme.of(context).colorScheme.secondary;
    }
  }

  Widget _buildBottomMenu(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 5.0),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(color: Colors.black.withOpacity(0.35), blurRadius: 5.0)
      ], color: Theme.of(context).colorScheme.surface),
      height: MediaQuery.of(context).size.height * UiUtils.bottomMenuPercentage,
      child: Row(
        children: [
          IconButton(
              onPressed: () {
                if (_currentIndex != 0) {
                  _pageController!.animateToPage(_currentIndex - 1,
                      duration: Duration(milliseconds: 500),
                      curve: Curves.easeInOut);
                }
              },
              icon: Icon(
                Icons.arrow_back_ios,
                color: Theme.of(context).primaryColor,
              )),
          Spacer(),
          Text(
            "${_currentIndex + 1}/${getQuestionsLength()}",
            style: TextStyle(
                color: Theme.of(context).primaryColor, fontSize: 18.0),
          ),
          Spacer(),
          IconButton(
              onPressed: () {
                if (_currentIndex != (getQuestionsLength() - 1)) {
                  _pageController!.animateToPage(_currentIndex + 1,
                      duration: Duration(milliseconds: 500),
                      curve: Curves.easeInOut);
                }
              },
              icon: Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).primaryColor,
              )),
        ],
      ),
    );
  }

  //to build option of given question
  Widget _buildOption(Options option, ResultDetail question) {
    final theme = context.read<ThemeCubit>().state.appTheme;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        /* Container(
          child: Text(question.selectedOptions![0].toString()),
        ), */
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: getOptionColor(question, option),
          ),
          width: MediaQuery.of(context).size.width * (0.8),
          margin: EdgeInsets.only(top: 15.0, left: 0),
          padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 15.0),
          child: Row(
            children: [
              question.selectedOptions!.contains(option.oid) == true
                  ? double.parse(option.score!) > 0
                      ? question.questionType == "Câu hỏi một lựa chọn"
                          ? Icon(
                              Icons.radio_button_checked_rounded,
                              color: Colors.white,
                            )
                          : Icon(
                              Icons.check_box,
                              color: Colors.white,
                            )
                      : question.questionType == "Câu hỏi một lựa chọn"
                          ? Icon(
                              Icons.radio_button_checked_rounded,
                              color: Colors.white,
                            )
                          : Icon(
                              Icons.check_box,
                              color: Colors.white,
                            )
                  : double.parse(option.score!) > 0
                      ? question.questionType == "Câu hỏi một lựa chọn"
                          ? Icon(
                              Icons.radio_button_off_rounded,
                              color: question.selectedOptions!.isEmpty
                                  ? Colors.grey
                                  : Colors.white,
                            )
                          : Icon(
                              Icons.check_box_outline_blank_rounded,
                              color: question.selectedOptions!.isEmpty
                                  ? Colors.grey
                                  : Colors.white,
                            )
                      : question.questionType == "Câu hỏi một lựa chọn"
                          ? Icon(
                              Icons.radio_button_off_rounded,
                              color: Colors.grey,
                            )
                          : Icon(
                              Icons.check_box_outline_blank_rounded,
                              color: Colors.grey,
                            ),
              SizedBox(
                width: 12,
              ),
              Flexible(
                  child: HtmlWidget(
                option.qOption?.replaceAll('../../', Common.apiDomain) ?? "",
                textStyle: TextStyle(
                    color: question.selectedOptions!.contains(option.oid)
                        ? (double.parse(option.score!) > 0
                            ? Colors.white
                            : Theme.of(context).colorScheme.surface)
                        : Theme.of(context).colorScheme.surface,
                    height: 1.0,
                    fontSize: 16.0),
                onTapImage: (p0) {
                  print(p0.sources.first.url);
                  final imageProvider =
                      Image.network(p0.sources.first.url).image;
                  showImageViewer(context, imageProvider,
                      backgroundColor: Colors.black.withOpacity(0.8),
                      useSafeArea: true,
                      doubleTapZoomable: true);
                },
              )
                  // Text(
                  //   UiUtils.parseHtmlString(option.qOption ?? "")
                  //       .replaceAll("\n", " "),
                  //   style: TextStyle(
                  //       color: question.selectedOptions!.contains(option.oid)
                  //           ? (double.parse(option.score!) > 0
                  //               ? Colors.white
                  //               : Theme.of(context).colorScheme.surface)
                  //           : Theme.of(context).colorScheme.surface),
                  // ),
                  ),
            ],
          ),
        ),
        /* Container(
            margin: EdgeInsets.only(top: 15.0, left: 5),
            child: question.selectedOptions!.contains(option.oid) == true
                ? double.parse(option.score!) > 0
                    ? Icon(
                        Icons.check_box,
                        color: Colors.green,
                      )
                    : Icon(
                        Icons.check_box,
                        color: Colors.red,
                      )
                : double.parse(option.score!) > 0
                    ? Icon(
                        Icons.check_box,
                        color: Colors.green,
                      )
                    : Icon(
                        Icons.check_box_outline_blank_rounded,
                        color: Colors.green,
                      )) */
      ],
    );
  }

  Widget _buildOptions(ResultDetail question) {
    return Column(
      children: question.options!.map((option) {
        return _buildOption(option, question);
      }).toList(),
    );
  }

  Widget _buildGuessTheWordOptionAndAnswer(
      GuessTheWordQuestion guessTheWordQuestion) {
    return Container(
      margin: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * (0.1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 25.0,
          ),
          Padding(
            padding: const EdgeInsetsDirectional.only(start: 0.0),
            child: Text(
              AppLocalization.of(context)!.getTranslatedValues("yourAnsLbl")! +
                  " : " +
                  "${UiUtils.buildGuessTheWordQuestionAnswer(guessTheWordQuestion.submittedAnswer)}",
              style: TextStyle(
                  fontSize: 18.0,
                  color: UiUtils.buildGuessTheWordQuestionAnswer(
                              guessTheWordQuestion.submittedAnswer) ==
                          guessTheWordQuestion.answer
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).colorScheme.secondary),
            ),
          ),
          UiUtils.buildGuessTheWordQuestionAnswer(
                      guessTheWordQuestion.submittedAnswer) ==
                  guessTheWordQuestion.answer
              ? SizedBox()
              : Padding(
                  padding: const EdgeInsetsDirectional.only(start: 0.0),
                  child: Text(
                    AppLocalization.of(context)!
                            .getTranslatedValues("correctAndLbl")! +
                        ":" +
                        " ${guessTheWordQuestion.answer}",
                    style: TextStyle(
                        fontSize: 18.0, color: Theme.of(context).primaryColor),
                  ),
                )
        ],
      ),
    );
  }

  Widget _buildNotes(String notes) {
    return notes.isEmpty
        ? Container()
        : Container(
            width: MediaQuery.of(context).size.width * (0.8),
            margin: EdgeInsets.only(top: 25.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    AppLocalization.of(context)!.getTranslatedValues(notesKey)!,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 18.0)),
                SizedBox(
                  height: 10.0,
                ),
                Text(
                  notes,
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              ],
            ),
          );
  }

  Widget _buildQuestionAndOptions(ResultDetail question, int index) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
          top: 10.0,
          bottom: MediaQuery.of(context).size.height *
                  UiUtils.bottomMenuPercentage +
              25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          QuestionContainer(
            isMathQuestion: false,
            question: question,
          ),

          //build options
          _buildOptions(question),
          // _buildNotes(question.note!),
          DescriptionContainer(
            text: question.description ?? "",
          ),
        ],
      ),
    );
  }

  Widget _buildQuestions() {
    return Container(
      height: MediaQuery.of(context).size.height * (0.85),
      child: PageView.builder(
          onPageChanged: (index) {
            if (_hasAudioQuestion()) {
              musicPlayerContainerKeys[_currentIndex].currentState?.stopAudio();
            }
            setState(() {
              _currentIndex = index;
            });
            if (_hasAudioQuestion()) {
              musicPlayerContainerKeys[_currentIndex].currentState?.playAudio();
            }
          },
          controller: _pageController,
          itemCount: getQuestionsLength(),
          itemBuilder: (context, index) {
            return _buildQuestionAndOptions(resultQuestion[index], index);
          }),
    );
  }

  Widget _buildReportButton(ReportQuestionCubit reportQuestionCubit) {
    return Transform.translate(
      offset: Offset(-5.0, 10.0),
      child: IconButton(
          onPressed: () {
            showModalBottomSheet(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.0),
                  topRight: Radius.circular(20.0),
                )),
                isDismissible: true,
                enableDrag: true,
                isScrollControlled: true,
                context: context,
                builder: (_) => ReportQuestionBottomSheetContainer(
                    questionId: isGuessTheWordQuizModule()
                        ? widget.guessTheWordQuestions[_currentIndex].id
                        : resultQuestion[_currentIndex].qid!,
                    reportQuestionCubit: reportQuestionCubit));
          },
          icon: Icon(
            Icons.report_problem,
            color: Theme.of(context).primaryColor,
          )),
    );
  }

  Future openDialog() => showDialog(
      context: context,
      builder: (context) {
        return ReportQuestionPopup(
          question: resultQuestion[_currentIndex].question ?? "",
          questionId: resultQuestion[_currentIndex].qid ?? "",
          quizId: widget.quizId,
        );
      });

  Widget _buildAppbar() {
    return Container(
      padding: EdgeInsets.only(bottom: 15),
      child: Stack(
        children: [
          Positioned.fill(
            top: 0,
            child: Align(
              alignment: Alignment.center,
              child: Text(
                AppLocalization.of(context)!
                    .getTranslatedValues("reviewAnswerLbl")!,
                style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ),
          Positioned.fill(
            top: -5,
            left: 25,
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: CustomBackButton(
                removeSnackBars: false,
                iconColor: Theme.of(context).primaryColor,
                onTap: () {
                  /* if (popBack == 'practice') {
                    Navigator.of(context).pushNamed(Routes.practice);
                  } else if (popBack == 'exam') {
                    Navigator.of(context).pushNamed(Routes.exams);
                  } else {
                    Navigator.pop(context);
                  } */
                  //Navigator.of(context).popUntil((route) => route.isFirst);
                  Navigator.pop(context);
                },
              ),
            ),
          ),
          Positioned.fill(
            top: -5,
            right: 25,
            child: Align(
              alignment: AlignmentDirectional.centerEnd,
              child: InkWell(
                onTap: openDialog,
                child: Icon(
                  Icons.chat,
                  size: 20,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildTabContainer(
                        AppLocalization.of(context)!
                            .getTranslatedValues("allQuestion")!,
                        1),
                    countQuestion['marked'] != 0
                        ? _buildTabContainer(
                            AppLocalization.of(context)!
                                    .getTranslatedValues("markedQuestions")! +
                                " (${countQuestion['marked']})",
                            2)
                        : SizedBox(),
                    countQuestion['wrong'] != 0
                        ? _buildTabContainer(
                            AppLocalization.of(context)!
                                    .getTranslatedValues("wrongAnswers")! +
                                " (${countQuestion['wrong']})",
                            3)
                        : SizedBox(),
                    countQuestion['correct'] != 0
                        ? _buildTabContainer(
                            AppLocalization.of(context)!
                                    .getTranslatedValues("correctAnswers")! +
                                " (${countQuestion['correct']})",
                            4)
                        : SizedBox(),
                    countQuestion['unanswer'] != 0
                        ? _buildTabContainer(
                            AppLocalization.of(context)!
                                    .getTranslatedValues("unanswerQuestions")! +
                                " (${countQuestion['unanswer']})",
                            5)
                        : SizedBox(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      height: MediaQuery.of(context).size.height *
          (UiUtils.appBarHeightPercentage + 0.055),
      decoration: BoxDecoration(
          boxShadow: [UiUtils.buildAppbarShadow()],
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.0),
              bottomRight: Radius.circular(20.0))),
    );
  }

  Widget _buildTabContainer(String title, int index) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = 0;
          _pageController!.animateToPage(_currentIndex,
              duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
          _currentSelectedTab = index;
          context.read<QuestionsCubit>().getQuestionByTab(_currentSelectedTab);
        });
      },
      child: Padding(
        padding: const EdgeInsets.only(left: 5.0, right: 5, top: 10),
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              color: Theme.of(context).primaryColor),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              title,
              style: TextStyle(
                color: /* Theme.of(context)
                    .primaryColor */
                    Colors.white
                        .withOpacity(_currentSelectedTab == index ? 1.0 : 0.5),
                fontSize: 16.0,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Widget _buildAppbar() {
  //   return Align(
  //     alignment: Alignment.topCenter,
  //     child: RoundedAppbar(
  //       title: AppLocalization.of(context)!
  //           .getTranslatedValues("reviewAnswerLbl")!,
  //       trailingWidget: resultQuestion.isEmpty
  //           ? _buildReportButton(context.read<ReportQuestionCubit>())
  //           : Row(
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 // Transform.translate(
  //                 //   offset: Offset(5.0, 10.0),
  //                 //   child: BookmarkButton(
  //                 //     bookmarkButtonColor: Theme.of(context).primaryColor,
  //                 //     bookmarkFillColor: Theme.of(context).primaryColor,
  //                 //     question: widget.questions[_currentIndex],
  //                 //   ),
  //                 // ),
  //                 // _buildReportButton(context.read<ReportQuestionCubit>()),
  //               ],
  //             ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    return Scaffold(
      body: BlocBuilder<QuestionsCubit, QuestionsState>(
          builder: (context, state) {
        if (state is QuestionsIntial) {
          // context.read<QuestionsCubit>().getResultQuestion(widget.rid);
          print('init');
          return const CircularProgressIndicator();
        } else if (state is QuestionsFetchInProgress) {
          print('in progress');
          context.read<QuestionsCubit>().getResultQuestion(widget.rid);
          return Center(child: const CircularProgressIndicator());
        } else if (state is ResultQuestionsFetchSuccess) {
          resultQuestion = state.resultQuestions;
          countQuestion = context.read<QuestionsCubit>().countQuestion;
          print(countQuestion);
          return Stack(
            children: [
              PageBackgroundGradientContainer(),
              //topDesign(),
              _buildAppbar(),
              Align(
                alignment: Alignment.topCenter,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height *
                        UiUtils.appBarHeightPercentage *
                        1.475,
                  ),
                  child: _buildQuestions(),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: _buildBottomMenu(context),
              ),
            ],
          );
        } else {
          inspect(state);
          return Container();
        }
      }),
    );
  }
}

class DescriptionContainer extends StatelessWidget {
  final String text;

  const DescriptionContainer({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        text != ''
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: Container(
                      alignment: Alignment.centerLeft,
                      padding: EdgeInsets.symmetric(
                          horizontal:
                              MediaQuery.of(context).size.width * (0.1)),
                      child: Column(
                        //mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                              padding: const EdgeInsets.only(top: 12.0),
                              child: HtmlWidget(
                                "<p><b>${AppLocalization.of(context)!.getTranslatedValues('description')!} :</b></p>" +
                                    text,
                                key: ValueKey(1),
                                textStyle: TextStyle(
                                    fontSize: 18,
                                    color: Theme.of(context).primaryColor,
                                    height: 1.5),
                                onTapImage: (p0) {
                                  print(p0.sources.first.url);
                                  final imageProvider =
                                      Image.network(p0.sources.first.url).image;
                                  showImageViewer(context, imageProvider,
                                      backgroundColor:
                                          Colors.black.withOpacity(0.8),
                                      useSafeArea: true,
                                      doubleTapZoomable: true);
                                },
                              )),
                        ],
                      ),
                    ),
                  ),
                ],
              )
            : Container(),
        SizedBox(
          height: 15.0,
        ),
      ],
    );
  }
}

class ReportQuestionBottomSheetContainer extends StatefulWidget {
  final ReportQuestionCubit reportQuestionCubit;
  final String questionId;
  ReportQuestionBottomSheetContainer(
      {Key? key, required this.reportQuestionCubit, required this.questionId})
      : super(key: key);

  @override
  _ReportQuestionBottomSheetContainerState createState() =>
      _ReportQuestionBottomSheetContainerState();
}

class _ReportQuestionBottomSheetContainerState
    extends State<ReportQuestionBottomSheetContainer> {
  final TextEditingController textEditingController = TextEditingController();
  late String errorMessage = "";

  String _buildButtonTitle(ReportQuestionState state) {
    if (state is ReportQuestionInProgress) {
      return AppLocalization.of(context)!
          .getTranslatedValues(submittingButton)!;
    }
    if (state is ReportQuestionFailure) {
      return AppLocalization.of(context)!.getTranslatedValues(retryLbl)!;
    }
    return AppLocalization.of(context)!.getTranslatedValues(submitBtn)!;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReportQuestionCubit, ReportQuestionState>(
      bloc: widget.reportQuestionCubit,
      listener: (context, state) {
        if (state is ReportQuestionSuccess) {
          Navigator.of(context).pop();
        }
        if (state is ReportQuestionFailure) {
          if (state.errorMessageCode == unauthorizedAccessCode) {
            UiUtils.showAlreadyLoggedInDialog(context: context);
            return;
          }
          //
          setState(() {
            errorMessage = AppLocalization.of(context)!.getTranslatedValues(
                convertErrorCodeToLanguageKey(state.errorMessageCode))!;
          });
        }
      },
      child: WillPopScope(
        onWillPop: () {
          if (widget.reportQuestionCubit.state is ReportQuestionInProgress) {
            return Future.value(false);
          }
          return Future.value(true);
        },
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              gradient: UiUtils.buildLinerGradient([
                Theme.of(context).scaffoldBackgroundColor,
                Theme.of(context).canvasColor
              ], Alignment.topCenter, Alignment.bottomCenter)),
          child: Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      margin: EdgeInsets.all(10.0),
                      child: IconButton(
                          onPressed: () {
                            if (widget.reportQuestionCubit.state
                                is! ReportQuestionInProgress) {
                              Navigator.of(context).pop();
                            }
                          },
                          icon: Icon(
                            Icons.close,
                            size: 28.0,
                            color: Theme.of(context).primaryColor,
                          )),
                    ),
                  ],
                ),

                Container(
                  padding: EdgeInsets.symmetric(horizontal: 5.0),
                  alignment: Alignment.center,
                  child: Text(
                    AppLocalization.of(context)!
                        .getTranslatedValues(reportQuestionKey)!,
                    style: TextStyle(
                        fontSize: 20.0,
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                SizedBox(
                  height: 15.0,
                ),
                //
                Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.symmetric(
                    horizontal: MediaQuery.of(context).size.width * (0.125),
                  ),
                  padding: EdgeInsets.only(left: 20.0),
                  height: 60.0,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                    color: Theme.of(context).colorScheme.surface,
                  ),
                  child: TextField(
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    controller: textEditingController,
                    decoration: InputDecoration(
                      hintText: AppLocalization.of(context)!
                          .getTranslatedValues(enterReasonKey)!,
                      hintStyle: TextStyle(
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                      border: InputBorder.none,
                    ),
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * (0.02),
                ),

                AnimatedSwitcher(
                  duration: Duration(milliseconds: 250),
                  child: errorMessage.isEmpty
                      ? SizedBox(
                          height: 20.0,
                        )
                      : Container(
                          height: 20.0,
                          child: Text(
                            errorMessage,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                          ),
                        ),
                ),

                SizedBox(
                  height: MediaQuery.of(context).size.height * (0.02),
                ),
                //

                BlocBuilder<ReportQuestionCubit, ReportQuestionState>(
                  bloc: widget.reportQuestionCubit,
                  builder: (context, state) {
                    return Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: MediaQuery.of(context).size.width * (0.3),
                      ),
                      child: CustomRoundedButton(
                        widthPercentage: MediaQuery.of(context).size.width,
                        backgroundColor: Theme.of(context).primaryColor,
                        buttonTitle: _buildButtonTitle(state),
                        radius: 10.0,
                        showBorder: false,
                        onTap: () {
                          if (state is! ReportQuestionInProgress) {
                            widget.reportQuestionCubit.reportQuestion(
                                message: textEditingController.text.trim(),
                                questionId: widget.questionId,
                                userId: context
                                    .read<UserDetailsCubit>()
                                    .getUserId());
                          }
                        },
                        fontWeight: FontWeight.bold,
                        titleColor: Theme.of(context).colorScheme.surface,
                        height: 40.0,
                      ),
                    );
                  },
                ),

                //
                SizedBox(
                  height: MediaQuery.of(context).size.height * (0.05),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
