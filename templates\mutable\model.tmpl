import 'package:equatable/equatable.dart';

/// generate by https://javiercbk.github.io/json_to_dart/
class Autogenerated${upperName} {
  final List<${upperName}QuizType> results;

  Autogenerated${upperName}({this.results});

  factory Autogenerated${upperName}.fromJson(Map<String, dynamic> json) {
    List<${upperName}QuizType> temp;
    if (json['results'] != null) {
      temp = <${upperName}QuizType>[];
      json['results'].forEach((v) {
        temp.add(${upperName}QuizType.fromJson(v as Map<String, dynamic>));
      });
    }
    return Autogenerated${upperName}(results: temp);
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (results != null) {
      data['results'] = results.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ${upperName}QuizType extends Equatable {
  final int id;
  final String name;

  ${upperName}QuizType(this.id, this.name);

  @override
  List<Object> get props => [id, name];

  factory ${upperName}QuizType.fromJson(Map<String, dynamic> json) {
    return ${upperName}QuizType(json['id'] as int, json['name'] as String);
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
  
}
